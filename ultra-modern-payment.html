<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ultra-Modern Payment System - AI-Powered Checkout</title>
    <script src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
            overflow-x: hidden;
        }

        .payment-container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 24px;
            box-shadow: 0 32px 64px rgba(0, 0, 0, 0.2);
            overflow: hidden;
            position: relative;
        }

        .payment-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4, #feca57);
            background-size: 300% 100%;
            animation: gradientShift 3s ease-in-out infinite;
        }

        @keyframes gradientShift {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }

        .header-section {
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
            color: white;
            padding: 40px;
            position: relative;
            overflow: hidden;
        }

        .header-section::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }

        .header-content {
            position: relative;
            z-index: 2;
            text-align: center;
        }

        .header-title {
            font-size: 32px;
            font-weight: 700;
            margin-bottom: 12px;
            background: linear-gradient(45deg, #fff, #a8edea);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .header-subtitle {
            font-size: 16px;
            opacity: 0.9;
            margin-bottom: 20px;
        }

        .ai-assistant {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            background: rgba(255, 255, 255, 0.1);
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .ai-pulse {
            width: 8px;
            height: 8px;
            background: #4ecdc4;
            border-radius: 50%;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); opacity: 1; }
            50% { transform: scale(1.5); opacity: 0.7; }
            100% { transform: scale(1); opacity: 1; }
        }

        .main-content {
            display: grid;
            grid-template-columns: 1fr 400px;
            gap: 40px;
            padding: 40px;
        }

        .payment-section {
            display: flex;
            flex-direction: column;
            gap: 30px;
        }

        .smart-suggestions {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 24px;
            border-radius: 16px;
            position: relative;
            overflow: hidden;
        }

        .smart-suggestions::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            animation: shimmer 3s infinite;
        }

        @keyframes shimmer {
            0% { left: -100%; }
            100% { left: 100%; }
        }

        .suggestion-header {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 16px;
        }

        .ai-brain {
            width: 32px;
            height: 32px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
        }

        .suggestion-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .suggestion-text {
            flex: 1;
        }

        .suggestion-title {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 4px;
        }

        .suggestion-desc {
            font-size: 14px;
            opacity: 0.9;
        }

        .quick-pay-btn {
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 12px 20px;
            border-radius: 12px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .quick-pay-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }

        .payment-methods-container {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 24px;
        }

        .section-title {
            font-size: 24px;
            font-weight: 700;
            color: #1a1a2e;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .view-toggle {
            display: flex;
            background: #f8f9fa;
            border-radius: 12px;
            padding: 4px;
        }

        .toggle-btn {
            padding: 8px 16px;
            border: none;
            background: transparent;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 14px;
            font-weight: 500;
        }

        .toggle-btn.active {
            background: white;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            color: #667eea;
        }

        .payment-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .payment-grid.list-view {
            grid-template-columns: 1fr;
        }

        .payment-card {
            background: white;
            border: 2px solid #e9ecef;
            border-radius: 16px;
            padding: 24px;
            cursor: pointer;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
        }

        .payment-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(102, 126, 234, 0.1), transparent);
            transition: left 0.5s ease;
        }

        .payment-card:hover::before {
            left: 100%;
        }

        .payment-card:hover {
            border-color: #667eea;
            transform: translateY(-8px) scale(1.02);
            box-shadow: 0 20px 40px rgba(102, 126, 234, 0.2);
        }

        .payment-card.selected {
            border-color: #667eea;
            background: linear-gradient(135deg, #f8f9ff 0%, #ffffff 100%);
            box-shadow: 0 12px 32px rgba(102, 126, 234, 0.25);
            transform: translateY(-4px);
        }

        .payment-card.recommended {
            border-color: #4ecdc4;
            position: relative;
        }

        .payment-card.recommended::after {
            content: '⭐ AI Recommended';
            position: absolute;
            top: 16px;
            right: 16px;
            background: linear-gradient(135deg, #4ecdc4, #44a08d);
            color: white;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 11px;
            font-weight: 600;
            z-index: 2;
        }

        .card-header {
            display: flex;
            align-items: center;
            gap: 16px;
            margin-bottom: 20px;
        }

        .payment-icon {
            width: 56px;
            height: 56px;
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            position: relative;
        }

        .payment-icon::after {
            content: '';
            position: absolute;
            inset: 2px;
            border-radius: 14px;
            background: linear-gradient(135deg, rgba(255,255,255,0.8), rgba(255,255,255,0.4));
        }

        .payment-icon span {
            position: relative;
            z-index: 1;
        }

        .card-info {
            flex: 1;
        }

        .card-title {
            font-size: 18px;
            font-weight: 600;
            color: #1a1a2e;
            margin-bottom: 6px;
        }

        .card-description {
            font-size: 14px;
            color: #6c757d;
            line-height: 1.4;
        }

        .card-stats {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 16px;
            margin-bottom: 20px;
        }

        .stat-item {
            text-align: center;
            padding: 12px;
            background: #f8f9fa;
            border-radius: 12px;
            transition: all 0.3s ease;
        }

        .payment-card:hover .stat-item {
            background: rgba(102, 126, 234, 0.1);
        }

        .stat-label {
            font-size: 11px;
            color: #6c757d;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-bottom: 4px;
        }

        .stat-value {
            font-size: 14px;
            font-weight: 600;
            color: #1a1a2e;
        }

        .popularity-meter {
            margin-bottom: 16px;
        }

        .popularity-label {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
            font-size: 12px;
            color: #6c757d;
        }

        .popularity-bar {
            height: 6px;
            background: #e9ecef;
            border-radius: 3px;
            overflow: hidden;
            position: relative;
        }

        .popularity-fill {
            height: 100%;
            background: linear-gradient(90deg, #4ecdc4, #44a08d);
            border-radius: 3px;
            transition: width 1s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
        }

        .popularity-fill::after {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.6), transparent);
            animation: popularityShine 2s infinite;
        }

        @keyframes popularityShine {
            0% { left: -100%; }
            100% { left: 100%; }
        }

        .benefits-list {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }

        .benefit-tag {
            background: linear-gradient(135deg, #e8f5e8, #d4edda);
            color: #155724;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 11px;
            font-weight: 500;
            border: 1px solid rgba(21, 87, 36, 0.1);
        }

        .sidebar {
            display: flex;
            flex-direction: column;
            gap: 24px;
        }

        .order-summary {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            position: sticky;
            top: 20px;
        }

        .summary-header {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 24px;
        }

        .summary-title {
            font-size: 20px;
            font-weight: 700;
            color: #1a1a2e;
        }

        .summary-items {
            display: flex;
            flex-direction: column;
            gap: 16px;
            margin-bottom: 24px;
        }

        .summary-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 16px;
        }

        .summary-item.total {
            border-top: 2px solid #e9ecef;
            padding-top: 16px;
            font-size: 20px;
            font-weight: 700;
            color: #1a1a2e;
        }

        .discount-item {
            color: #28a745;
            font-weight: 500;
        }

        .pay-button {
            width: 100%;
            padding: 18px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 16px;
            font-size: 18px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
            margin-bottom: 16px;
        }

        .pay-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s ease;
        }

        .pay-button:hover::before {
            left: 100%;
        }

        .pay-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 16px 32px rgba(102, 126, 234, 0.4);
        }

        .pay-button:active {
            transform: translateY(-1px);
        }

        .pay-button:disabled {
            opacity: 0.7;
            cursor: not-allowed;
            transform: none;
        }

        .security-badges {
            display: flex;
            justify-content: center;
            gap: 16px;
            margin-top: 16px;
        }

        .security-badge {
            display: flex;
            align-items: center;
            gap: 6px;
            font-size: 12px;
            color: #6c757d;
        }

        .trust-indicators {
            background: #f8f9fa;
            border-radius: 16px;
            padding: 20px;
            text-align: center;
        }

        .trust-title {
            font-size: 16px;
            font-weight: 600;
            color: #1a1a2e;
            margin-bottom: 12px;
        }

        .trust-items {
            display: flex;
            justify-content: space-around;
            gap: 12px;
        }

        .trust-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 4px;
        }

        .trust-icon {
            width: 32px;
            height: 32px;
            background: linear-gradient(135deg, #4ecdc4, #44a08d);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 14px;
        }

        .trust-label {
            font-size: 10px;
            color: #6c757d;
            text-align: center;
        }

        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
                gap: 20px;
                padding: 20px;
            }

            .payment-grid {
                grid-template-columns: 1fr;
            }

            .card-stats {
                grid-template-columns: 1fr 1fr;
            }

            .header-section {
                padding: 20px;
            }

            .header-title {
                font-size: 24px;
            }
        }
    </style>
</head>
<body>
    <div id="root"></div>

    <script type="text/babel">
        const { useState, useEffect, useRef } = React;

        const UltraModernPayment = () => {
            const [selectedMethod, setSelectedMethod] = useState('');
            const [viewMode, setViewMode] = useState('grid');
            const [isProcessing, setIsProcessing] = useState(false);
            const [aiSuggestion, setAiSuggestion] = useState('');
            const [userPreferences, setUserPreferences] = useState({
                speed: 'fast',
                cost: 'low',
                security: 'high'
            });
            const [paymentHistory, setPaymentHistory] = useState([]);
            const [currentTime, setCurrentTime] = useState(new Date());
            const [showConfetti, setShowConfetti] = useState(false);

            // Update time every second
            useEffect(() => {
                const timer = setInterval(() => setCurrentTime(new Date()), 1000);
                return () => clearInterval(timer);
            }, []);

            const paymentMethods = [
                {
                    id: 'upi',
                    name: 'UPI',
                    icon: '📱',
                    description: 'Instant payments via UPI apps with AI fraud detection',
                    processingTime: 'Instant',
                    fees: 'Free',
                    popularity: 95,
                    recommended: true,
                    benefits: ['Instant transfer', 'AI fraud protection', '24/7 available', 'Biometric auth'],
                    trustScore: 98,
                    avgTime: '2 seconds',
                    successRate: '99.8%',
                    providers: ['Google Pay', 'PhonePe', 'Paytm', 'BHIM'],
                    aiScore: 95
                },
                {
                    id: 'card',
                    name: 'Smart Card',
                    icon: '💳',
                    description: 'AI-powered card payments with dynamic security',
                    processingTime: '2-3 mins',
                    fees: '2.5%',
                    popularity: 88,
                    recommended: false,
                    benefits: ['EMI options', 'Reward points', 'Global acceptance', 'Smart routing'],
                    trustScore: 96,
                    avgTime: '45 seconds',
                    successRate: '99.2%',
                    providers: ['Visa', 'Mastercard', 'RuPay', 'Amex'],
                    aiScore: 82
                },
                {
                    id: 'wallet',
                    name: 'Digital Wallet',
                    icon: '👛',
                    description: 'Smart wallet with predictive balance management',
                    processingTime: 'Instant',
                    fees: 'Free',
                    popularity: 72,
                    recommended: false,
                    benefits: ['Quick checkout', 'Cashback AI', 'Auto-reload', 'Expense tracking'],
                    trustScore: 94,
                    avgTime: '3 seconds',
                    successRate: '99.5%',
                    providers: ['Paytm', 'PhonePe', 'Amazon Pay', 'MobiKwik'],
                    aiScore: 78
                },
                {
                    id: 'crypto',
                    name: 'Cryptocurrency',
                    icon: '₿',
                    description: 'Next-gen crypto payments with smart contracts',
                    processingTime: '5-15 mins',
                    fees: '0.5%',
                    popularity: 25,
                    recommended: false,
                    benefits: ['Decentralized', 'Smart contracts', 'Global reach', 'Future-proof'],
                    trustScore: 89,
                    avgTime: '8 minutes',
                    successRate: '98.9%',
                    providers: ['Bitcoin', 'Ethereum', 'USDT', 'BNB'],
                    aiScore: 65
                },
                {
                    id: 'bnpl',
                    name: 'Smart BNPL',
                    icon: '📅',
                    description: 'AI-driven buy now pay later with flexible terms',
                    processingTime: 'Instant',
                    fees: 'Varies',
                    popularity: 45,
                    recommended: false,
                    benefits: ['Flexible terms', 'Credit building', 'Smart reminders', 'Auto-pay'],
                    trustScore: 91,
                    avgTime: '10 seconds',
                    successRate: '99.1%',
                    providers: ['Klarna', 'Afterpay', 'Sezzle', 'Affirm'],
                    aiScore: 71
                },
                {
                    id: 'bank',
                    name: 'Open Banking',
                    icon: '🏦',
                    description: 'Direct bank integration with AI risk assessment',
                    processingTime: '1-3 mins',
                    fees: 'Free',
                    popularity: 68,
                    recommended: false,
                    benefits: ['Direct transfer', 'High security', 'No intermediary', 'Real-time'],
                    trustScore: 97,
                    avgTime: '90 seconds',
                    successRate: '99.6%',
                    providers: ['Chase', 'Wells Fargo', 'Bank of America', 'Citi'],
                    aiScore: 85
                }
            ];

            // AI Recommendation Engine
            useEffect(() => {
                const calculateAIRecommendation = () => {
                    const timeOfDay = currentTime.getHours();
                    const isWeekend = currentTime.getDay() === 0 || currentTime.getDay() === 6;

                    let recommendation = '';
                    let suggestedMethod = '';

                    if (timeOfDay >= 9 && timeOfDay <= 17 && !isWeekend) {
                        // Business hours - recommend fastest
                        suggestedMethod = 'upi';
                        recommendation = 'UPI is 3x faster during business hours with our AI optimization';
                    } else if (timeOfDay >= 18 && timeOfDay <= 22) {
                        // Evening - recommend cashback
                        suggestedMethod = 'wallet';
                        recommendation = 'Digital wallets offer 2x cashback during evening hours';
                    } else {
                        // Night/early morning - recommend secure
                        suggestedMethod = 'bank';
                        recommendation = 'Open Banking provides enhanced security for off-hours transactions';
                    }

                    setAiSuggestion(recommendation);
                    if (!selectedMethod) {
                        setSelectedMethod(suggestedMethod);
                    }
                };

                calculateAIRecommendation();
            }, [currentTime, selectedMethod]);

            const handlePayment = () => {
                setIsProcessing(true);

                // Simulate AI processing
                setTimeout(() => {
                    setShowConfetti(true);
                    setIsProcessing(false);

                    // Add to payment history
                    const newPayment = {
                        id: Date.now(),
                        method: selectedMethod,
                        amount: 42.99,
                        timestamp: new Date(),
                        status: 'success'
                    };
                    setPaymentHistory(prev => [newPayment, ...prev.slice(0, 4)]);

                    setTimeout(() => {
                        alert('🎉 Payment successful! AI has optimized your transaction for maximum security and speed.');
                        setShowConfetti(false);
                    }, 1000);
                }, 3000);
            };

            const getSelectedMethodData = () => {
                return paymentMethods.find(method => method.id === selectedMethod);
            };

            return (
                <div className="payment-container">
                    {/* Animated Header */}
                    <div className="header-section">
                        <div className="header-content">
                            <h1 className="header-title">AI-Powered Checkout</h1>
                            <p className="header-subtitle">
                                Smart payments optimized by artificial intelligence
                            </p>
                            <div className="ai-assistant">
                                <div className="ai-pulse"></div>
                                <span>AI Assistant Active</span>
                            </div>
                        </div>
                    </div>

                    <div className="main-content">
                        <div className="payment-section">
                            {/* AI Suggestions */}
                            <div className="smart-suggestions">
                                <div className="suggestion-header">
                                    <div className="ai-brain">🧠</div>
                                    <div>
                                        <div className="suggestion-title">Smart Recommendation</div>
                                        <div className="suggestion-desc">Based on your preferences and current conditions</div>
                                    </div>
                                </div>
                                <div className="suggestion-content">
                                    <div className="suggestion-text">
                                        <div className="suggestion-title">{aiSuggestion}</div>
                                        <div className="suggestion-desc">
                                            Optimized for speed, cost, and security
                                        </div>
                                    </div>
                                    <button
                                        className="quick-pay-btn"
                                        onClick={() => {
                                            const recommended = paymentMethods.find(m => m.recommended);
                                            if (recommended) setSelectedMethod(recommended.id);
                                        }}
                                    >
                                        Use AI Choice
                                    </button>
                                </div>
                            </div>

                            {/* Payment Methods */}
                            <div className="payment-methods-container">
                                <div className="section-header">
                                    <h2 className="section-title">
                                        <span>🚀</span>
                                        Choose Payment Method
                                    </h2>
                                    <div className="view-toggle">
                                        <button
                                            className={`toggle-btn ${viewMode === 'grid' ? 'active' : ''}`}
                                            onClick={() => setViewMode('grid')}
                                        >
                                            Grid
                                        </button>
                                        <button
                                            className={`toggle-btn ${viewMode === 'list' ? 'active' : ''}`}
                                            onClick={() => setViewMode('list')}
                                        >
                                            List
                                        </button>
                                    </div>
                                </div>

                                <div className={`payment-grid ${viewMode === 'list' ? 'list-view' : ''}`}>
                                    {paymentMethods.map((method) => (
                                        <div
                                            key={method.id}
                                            className={`payment-card ${selectedMethod === method.id ? 'selected' : ''} ${method.recommended ? 'recommended' : ''}`}
                                            onClick={() => setSelectedMethod(method.id)}
                                        >
                                            <div className="card-header">
                                                <div className="payment-icon">
                                                    <span>{method.icon}</span>
                                                </div>
                                                <div className="card-info">
                                                    <h3 className="card-title">{method.name}</h3>
                                                    <p className="card-description">{method.description}</p>
                                                </div>
                                            </div>

                                            <div className="card-stats">
                                                <div className="stat-item">
                                                    <div className="stat-label">Speed</div>
                                                    <div className="stat-value">{method.avgTime}</div>
                                                </div>
                                                <div className="stat-item">
                                                    <div className="stat-label">Success</div>
                                                    <div className="stat-value">{method.successRate}</div>
                                                </div>
                                                <div className="stat-item">
                                                    <div className="stat-label">AI Score</div>
                                                    <div className="stat-value">{method.aiScore}/100</div>
                                                </div>
                                            </div>

                                            <div className="popularity-meter">
                                                <div className="popularity-label">
                                                    <span>User Preference</span>
                                                    <span>{method.popularity}%</span>
                                                </div>
                                                <div className="popularity-bar">
                                                    <div
                                                        className="popularity-fill"
                                                        style={{width: `${method.popularity}%`}}
                                                    ></div>
                                                </div>
                                            </div>

                                            <div className="benefits-list">
                                                {method.benefits.slice(0, 3).map((benefit, index) => (
                                                    <span key={index} className="benefit-tag">
                                                        ✓ {benefit}
                                                    </span>
                                                ))}
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            </div>
                        </div>

                        {/* Sidebar */}
                        <div className="sidebar">
                            {/* Order Summary */}
                            <div className="order-summary">
                                <div className="summary-header">
                                    <span>📊</span>
                                    <h3 className="summary-title">Smart Summary</h3>
                                </div>

                                <div className="summary-items">
                                    <div className="summary-item">
                                        <span>Subtotal</span>
                                        <span>€39.99</span>
                                    </div>
                                    <div className="summary-item">
                                        <span>AI Optimization</span>
                                        <span className="discount-item">-€2.00</span>
                                    </div>
                                    <div className="summary-item">
                                        <span>Smart Routing</span>
                                        <span className="discount-item">-€0.50</span>
                                    </div>
                                    <div className="summary-item">
                                        <span>Processing Fee</span>
                                        <span>€5.50</span>
                                    </div>
                                    <div className="summary-item total">
                                        <span>Total</span>
                                        <span>€42.99</span>
                                    </div>
                                </div>

                                {selectedMethod && (
                                    <div style={{
                                        background: '#f8f9fa',
                                        padding: '16px',
                                        borderRadius: '12px',
                                        marginBottom: '20px'
                                    }}>
                                        <div style={{fontSize: '14px', fontWeight: '600', marginBottom: '8px'}}>
                                            Selected: {getSelectedMethodData()?.name}
                                        </div>
                                        <div style={{fontSize: '12px', color: '#6c757d'}}>
                                            Estimated time: {getSelectedMethodData()?.avgTime}
                                        </div>
                                        <div style={{fontSize: '12px', color: '#6c757d'}}>
                                            Success rate: {getSelectedMethodData()?.successRate}
                                        </div>
                                    </div>
                                )}

                                <button
                                    className="pay-button"
                                    onClick={handlePayment}
                                    disabled={isProcessing || !selectedMethod}
                                >
                                    {isProcessing ? (
                                        <>
                                            <span>🔄</span>
                                            AI Processing...
                                        </>
                                    ) : (
                                        <>
                                            <span>🚀</span>
                                            Pay €42.99 Securely
                                        </>
                                    )}
                                </button>

                                <div className="security-badges">
                                    <div className="security-badge">
                                        <span>🛡️</span>
                                        <span>256-bit SSL</span>
                                    </div>
                                    <div className="security-badge">
                                        <span>🔒</span>
                                        <span>PCI Compliant</span>
                                    </div>
                                    <div className="security-badge">
                                        <span>🧠</span>
                                        <span>AI Protected</span>
                                    </div>
                                </div>
                            </div>

                            {/* Trust Indicators */}
                            <div className="trust-indicators">
                                <h4 className="trust-title">Why Choose Our AI Payment?</h4>
                                <div className="trust-items">
                                    <div className="trust-item">
                                        <div className="trust-icon">⚡</div>
                                        <div className="trust-label">3x Faster</div>
                                    </div>
                                    <div className="trust-item">
                                        <div className="trust-icon">🛡️</div>
                                        <div className="trust-label">99.9% Secure</div>
                                    </div>
                                    <div className="trust-item">
                                        <div className="trust-icon">💰</div>
                                        <div className="trust-label">Smart Savings</div>
                                    </div>
                                    <div className="trust-item">
                                        <div className="trust-icon">🎯</div>
                                        <div className="trust-label">AI Optimized</div>
                                    </div>
                                </div>
                            </div>

                            {/* Payment History */}
                            {paymentHistory.length > 0 && (
                                <div style={{
                                    background: 'white',
                                    borderRadius: '16px',
                                    padding: '20px',
                                    boxShadow: '0 4px 16px rgba(0, 0, 0, 0.1)'
                                }}>
                                    <h4 style={{marginBottom: '16px', color: '#1a1a2e'}}>Recent Transactions</h4>
                                    {paymentHistory.map((payment) => (
                                        <div key={payment.id} style={{
                                            display: 'flex',
                                            justifyContent: 'space-between',
                                            alignItems: 'center',
                                            padding: '8px 0',
                                            borderBottom: '1px solid #f0f0f0'
                                        }}>
                                            <div>
                                                <div style={{fontSize: '14px', fontWeight: '500'}}>
                                                    {paymentMethods.find(m => m.id === payment.method)?.name}
                                                </div>
                                                <div style={{fontSize: '12px', color: '#6c757d'}}>
                                                    {payment.timestamp.toLocaleTimeString()}
                                                </div>
                                            </div>
                                            <div style={{
                                                color: '#28a745',
                                                fontSize: '14px',
                                                fontWeight: '600'
                                            }}>
                                                €{payment.amount}
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            )}
                        </div>
                    </div>

                    {/* Confetti Effect */}
                    {showConfetti && (
                        <div style={{
                            position: 'fixed',
                            top: 0,
                            left: 0,
                            width: '100%',
                            height: '100%',
                            pointerEvents: 'none',
                            zIndex: 9999
                        }}>
                            {[...Array(50)].map((_, i) => (
                                <div
                                    key={i}
                                    style={{
                                        position: 'absolute',
                                        top: '-10px',
                                        left: `${Math.random() * 100}%`,
                                        width: '10px',
                                        height: '10px',
                                        background: ['#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#feca57'][Math.floor(Math.random() * 5)],
                                        animation: `fall ${Math.random() * 3 + 2}s linear infinite`,
                                        borderRadius: '50%'
                                    }}
                                />
                            ))}
                        </div>
                    )}
                </div>
            );
        };

        ReactDOM.render(<UltraModernPayment />, document.getElementById('root'));
    </script>

    <style>
        @keyframes fall {
            to {
                transform: translateY(100vh) rotate(360deg);
            }
        }
    </style>
</body>
</html>