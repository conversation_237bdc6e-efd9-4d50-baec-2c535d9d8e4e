<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Enhanced Payment System - Add/Remove Cards</title>
    <script src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
            color: #000000;
            padding: 20px;
        }

        .demo-container {
            max-width: 800px;
            margin: 0 auto;
            background: #ffffff;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .demo-header {
            background: #000000;
            color: #ffffff;
            padding: 20px;
            text-align: center;
        }

        .demo-content {
            padding: 30px;
        }

        .section {
            margin-bottom: 30px;
        }

        .section-title {
            font-size: 18px;
            font-weight: 600;
            color: #000000;
            margin-bottom: 16px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .saved-methods {
            display: flex;
            flex-direction: column;
            gap: 12px;
            margin-bottom: 16px;
        }

        .saved-method {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 16px;
            background: #f8f9fa;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
        }

        .method-info {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .method-icon {
            width: 40px;
            height: 40px;
            background: #f0f0f0;
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
        }

        .method-details h4 {
            font-size: 16px;
            font-weight: 600;
            color: #000000;
            margin-bottom: 4px;
        }

        .method-meta {
            display: flex;
            gap: 12px;
            font-size: 12px;
            color: #666666;
        }

        .method-actions {
            display: flex;
            gap: 8px;
            align-items: center;
        }

        .btn {
            padding: 8px 16px;
            border: 1px solid #e0e0e0;
            background: #ffffff;
            color: #000000;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.2s ease;
        }

        .btn:hover {
            border-color: #000000;
        }

        .btn-danger {
            color: #dc3545;
            border-color: #dc3545;
        }

        .btn-danger:hover {
            background: #dc3545;
            color: #ffffff;
        }

        .add-method-btn {
            background: none;
            border: 2px dashed #e0e0e0;
            color: #666666;
            padding: 16px;
            border-radius: 6px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            transition: all 0.2s ease;
        }

        .add-method-btn:hover {
            border-color: #000000;
            color: #000000;
        }

        .card-form {
            background: #f8f9fa;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 24px;
            margin-top: 16px;
        }

        .form-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 16px;
            margin-bottom: 16px;
        }

        .form-group {
            display: flex;
            flex-direction: column;
            gap: 6px;
        }

        .form-group.full-width {
            grid-column: 1 / -1;
        }

        .form-label {
            font-size: 14px;
            font-weight: 500;
            color: #000000;
        }

        .form-input {
            padding: 12px;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
            font-size: 14px;
            outline: none;
            transition: border-color 0.2s ease;
        }

        .form-input:focus {
            border-color: #000000;
            box-shadow: 0 0 0 2px rgba(0, 0, 0, 0.1);
        }

        .checkbox-group {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 20px;
        }

        .form-actions {
            display: flex;
            gap: 12px;
        }

        .btn-primary {
            background: #000000;
            color: #ffffff;
            border: none;
            padding: 12px 20px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
        }

        .btn-primary:hover {
            background: #333333;
        }

        .btn-secondary {
            background: none;
            color: #666666;
            border: 1px solid #e0e0e0;
            padding: 12px 20px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
        }

        .btn-secondary:hover {
            border-color: #000000;
            color: #000000;
        }

        .demo-note {
            background: #e8f4fd;
            border: 1px solid #bee5eb;
            border-radius: 6px;
            padding: 16px;
            margin-bottom: 20px;
            font-size: 14px;
            color: #0c5460;
        }

        @media (max-width: 768px) {
            .form-grid {
                grid-template-columns: 1fr;
            }
            
            .method-actions {
                flex-direction: column;
                gap: 4px;
            }
        }
    </style>
</head>
<body>
    <div id="root"></div>

    <script type="text/babel">
        const { useState } = React;

        const EnhancedPaymentDemo = () => {
            const [savedMethods, setSavedMethods] = useState([
                {
                    id: 1,
                    type: 'VISA',
                    details: '**** 1234',
                    icon: '💳',
                    lastUsed: '2 days ago',
                    expiryDate: '12/26'
                },
                {
                    id: 2,
                    type: 'UPI',
                    details: 'user@upi',
                    icon: '📱',
                    lastUsed: 'Yesterday'
                },
                {
                    id: 3,
                    type: 'Mastercard',
                    details: '**** 5678',
                    icon: '💳',
                    lastUsed: '1 week ago',
                    expiryDate: '08/25'
                }
            ]);

            const [showCardForm, setShowCardForm] = useState(false);
            const [cardDetails, setCardDetails] = useState({
                cardNumber: '',
                expiryDate: '',
                cvv: '',
                cardholderName: '',
                saveCard: false
            });

            const handleCardInputChange = (field, value) => {
                setCardDetails(prev => ({
                    ...prev,
                    [field]: value
                }));
            };

            const formatCardNumber = (value) => {
                const v = value.replace(/\s+/g, '').replace(/[^0-9]/gi, '');
                const matches = v.match(/\d{4,16}/g);
                const match = matches && matches[0] || '';
                const parts = [];
                for (let i = 0, len = match.length; i < len; i += 4) {
                    parts.push(match.substring(i, i + 4));
                }
                if (parts.length) {
                    return parts.join(' ');
                } else {
                    return v;
                }
            };

            const formatExpiryDate = (value) => {
                const v = value.replace(/\s+/g, '').replace(/[^0-9]/gi, '');
                if (v.length >= 2) {
                    return v.substring(0, 2) + '/' + v.substring(2, 4);
                }
                return v;
            };

            const removeMethod = (methodId) => {
                if (confirm('Are you sure you want to remove this payment method?')) {
                    setSavedMethods(prev => prev.filter(method => method.id !== methodId));
                }
            };

            const addNewCard = () => {
                if (cardDetails.cardNumber && cardDetails.expiryDate && cardDetails.cvv && cardDetails.cardholderName) {
                    const newCard = {
                        id: Date.now(),
                        type: cardDetails.cardNumber.startsWith('4') ? 'VISA' : 'Mastercard',
                        details: '**** ' + cardDetails.cardNumber.slice(-4),
                        icon: '💳',
                        lastUsed: 'Just now',
                        expiryDate: cardDetails.expiryDate
                    };
                    
                    setSavedMethods(prev => [newCard, ...prev]);
                    
                    setCardDetails({
                        cardNumber: '',
                        expiryDate: '',
                        cvv: '',
                        cardholderName: '',
                        saveCard: false
                    });
                    
                    setShowCardForm(false);
                    alert('✅ Card added successfully!');
                } else {
                    alert('Please fill all card details');
                }
            };

            return (
                <div className="demo-container">
                    <div className="demo-header">
                        <h1>Enhanced Payment System</h1>
                        <p>Add/Remove Payment Methods & Card Details Form</p>
                    </div>

                    <div className="demo-content">
                        <div className="demo-note">
                            <strong>Demo Features:</strong> Click "Add New Card" to see the card form, use the "×" button to remove saved methods, and test the card number formatting (try typing ****************).
                        </div>

                        <div className="section">
                            <h2 className="section-title">
                                <span>💳</span>
                                Saved Payment Methods
                            </h2>

                            <div className="saved-methods">
                                {savedMethods.map((method) => (
                                    <div key={method.id} className="saved-method">
                                        <div className="method-info">
                                            <div className="method-icon">{method.icon}</div>
                                            <div className="method-details">
                                                <h4>{method.type} {method.details}</h4>
                                                <div className="method-meta">
                                                    <span>Last used: {method.lastUsed}</span>
                                                    {method.expiryDate && <span>Expires: {method.expiryDate}</span>}
                                                </div>
                                            </div>
                                        </div>
                                        <div className="method-actions">
                                            <button className="btn">Use</button>
                                            <button 
                                                className="btn btn-danger"
                                                onClick={() => removeMethod(method.id)}
                                                title="Remove payment method"
                                            >
                                                ×
                                            </button>
                                        </div>
                                    </div>
                                ))}
                            </div>

                            <button 
                                className="add-method-btn"
                                onClick={() => setShowCardForm(true)}
                            >
                                <span style={{fontSize: '16px', fontWeight: 'bold'}}>+</span>
                                Add New Card
                            </button>

                            {showCardForm && (
                                <div className="card-form">
                                    <h3 style={{marginBottom: '20px', display: 'flex', alignItems: 'center', gap: '8px'}}>
                                        <span>💳</span>
                                        Add New Card
                                    </h3>

                                    <div className="form-grid">
                                        <div className="form-group full-width">
                                            <label className="form-label">Cardholder Name</label>
                                            <input
                                                type="text"
                                                className="form-input"
                                                placeholder="John Doe"
                                                value={cardDetails.cardholderName}
                                                onChange={(e) => handleCardInputChange('cardholderName', e.target.value)}
                                            />
                                        </div>

                                        <div className="form-group full-width">
                                            <label className="form-label">Card Number</label>
                                            <input
                                                type="text"
                                                className="form-input"
                                                placeholder="1234 5678 9012 3456"
                                                value={cardDetails.cardNumber}
                                                onChange={(e) => handleCardInputChange('cardNumber', formatCardNumber(e.target.value))}
                                                maxLength="19"
                                                style={{letterSpacing: '1px'}}
                                            />
                                        </div>

                                        <div className="form-group">
                                            <label className="form-label">Expiry Date</label>
                                            <input
                                                type="text"
                                                className="form-input"
                                                placeholder="MM/YY"
                                                value={cardDetails.expiryDate}
                                                onChange={(e) => handleCardInputChange('expiryDate', formatExpiryDate(e.target.value))}
                                                maxLength="5"
                                            />
                                        </div>

                                        <div className="form-group">
                                            <label className="form-label">CVV</label>
                                            <input
                                                type="text"
                                                className="form-input"
                                                placeholder="123"
                                                value={cardDetails.cvv}
                                                onChange={(e) => handleCardInputChange('cvv', e.target.value.replace(/\D/g, '').slice(0, 3))}
                                                maxLength="3"
                                            />
                                        </div>
                                    </div>

                                    <div className="checkbox-group">
                                        <input
                                            type="checkbox"
                                            id="saveCard"
                                            checked={cardDetails.saveCard}
                                            onChange={(e) => handleCardInputChange('saveCard', e.target.checked)}
                                        />
                                        <label htmlFor="saveCard" style={{fontSize: '14px', color: '#666666', cursor: 'pointer'}}>
                                            Save this card for future payments
                                        </label>
                                    </div>

                                    <div className="form-actions">
                                        <button className="btn-primary" onClick={addNewCard}>
                                            Add Card
                                        </button>
                                        <button className="btn-secondary" onClick={() => setShowCardForm(false)}>
                                            Cancel
                                        </button>
                                    </div>
                                </div>
                            )}
                        </div>
                    </div>
                </div>
            );
        };

        ReactDOM.render(<EnhancedPaymentDemo />, document.getElementById('root'));
    </script>
</body>
</html>
