{"version": 3, "file": "valibot.modern.mjs", "sources": ["../src/valibot.ts"], "sourcesContent": ["import { toNestErrors } from '@hookform/resolvers';\nimport { FieldError, FieldValues, appendErrors } from 'react-hook-form';\nimport { getDotPath, safeParseAsync } from 'valibot';\nimport type { Resolver } from './types';\n\nexport const valibotResolver: Resolver =\n  (schema, schemaOptions, resolverOptions = {}) =>\n  async (values, _, options) => {\n    // Check if we should validate all field criteria\n    const validateAllFieldCriteria =\n      !options.shouldUseNativeValidation && options.criteriaMode === 'all';\n\n    // Parse values with Valibot schema\n    const result = await safeParseAsync(\n      schema,\n      values,\n      Object.assign({}, schemaOptions, {\n        abortPipeEarly: !validateAllFieldCriteria,\n      }),\n    );\n\n    // If there are issues, return them as errors\n    if (result.issues) {\n      // Create errors object\n      const errors: Record<string, FieldError> = {};\n\n      // Iterate over issues to add them to errors object\n      for (; result.issues.length; ) {\n        const issue = result.issues[0];\n        // Create dot path from issue\n        const path = getDotPath(issue);\n\n        if (path) {\n          // Add first error of path to errors object\n          if (!errors[path]) {\n            errors[path] = { message: issue.message, type: issue.type };\n          }\n\n          // If configured, add all errors of path to errors object\n          if (validateAllFieldCriteria) {\n            const types = errors[path].types;\n            const messages = types && types[issue.type];\n            errors[path] = appendErrors(\n              path,\n              validateAllFieldCriteria,\n              errors,\n              issue.type,\n              messages\n                ? ([] as string[]).concat(\n                    messages as string | string[],\n                    issue.message,\n                  )\n                : issue.message,\n            ) as FieldError;\n          }\n        }\n\n        result.issues.shift();\n      }\n\n      // Return resolver result with errors\n      return {\n        values: {},\n        errors: toNestErrors(errors, options),\n      } as const;\n    }\n\n    // Otherwise, return resolver result with values\n    return {\n      values: resolverOptions.raw ? values : (result.output as FieldValues),\n      errors: {},\n    };\n  };\n"], "names": ["valibotResolver", "schema", "schemaOptions", "resolverOptions", "async", "values", "_", "options", "validateAllFieldCriteria", "shouldUseNativeValidation", "criteriaMode", "result", "safeParseAsync", "Object", "assign", "abort<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "issues", "errors", "length", "issue", "path", "getDotPath", "message", "type", "types", "messages", "appendErrors", "concat", "shift", "toNestErrors", "raw", "output"], "mappings": "2JAKa,MAAAA,EACXA,CAACC,EAAQC,EAAeC,EAAkB,CAAA,IAC1CC,MAAOC,EAAQC,EAAGC,KAEhB,MAAMC,GACHD,EAAQE,2BAAsD,QAAzBF,EAAQG,aAG1CC,QAAeC,EACnBX,EACAI,EACAQ,OAAOC,OAAO,CAAA,EAAIZ,EAAe,CAC/Ba,gBAAiBP,KAKrB,GAAIG,EAAOK,OAAQ,CAEjB,MAAMC,EAAqC,CAAA,EAG3C,KAAON,EAAOK,OAAOE,QAAU,CAC7B,MAAMC,EAAQR,EAAOK,OAAO,GAEtBI,EAAOC,EAAWF,GAExB,GAAIC,IAEGH,EAAOG,KACVH,EAAOG,GAAQ,CAAEE,QAASH,EAAMG,QAASC,KAAMJ,EAAMI,OAInDf,GAA0B,CAC5B,MAAMgB,EAAQP,EAAOG,GAAMI,MACrBC,EAAWD,GAASA,EAAML,EAAMI,MACtCN,EAAOG,GAAQM,EACbN,EACAZ,EACAS,EACAE,EAAMI,KACNE,EACK,GAAgBE,OACfF,EACAN,EAAMG,SAERH,EAAMG,QAEd,CAGFX,EAAOK,OAAOY,OAChB,CAGA,MAAO,CACLvB,OAAQ,CAAA,EACRY,OAAQY,EAAaZ,EAAQV,GAEjC,CAGA,MAAO,CACLF,OAAQF,EAAgB2B,IAAMzB,EAAUM,EAAOoB,OAC/Cd,OAAQ,CAAA"}