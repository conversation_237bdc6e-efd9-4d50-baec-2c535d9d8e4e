"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = _objectSpread;
var _defineProperty = require("./defineProperty.js");
function _objectSpread(target) {
  for (var i = 1; i < arguments.length; i++) {
    var source = arguments[i] != null ? Object(arguments[i]) : {};
    var ownKeys = Object.keys(source);
    if (typeof Object.getOwnPropertySymbols === "function") {
      ownKeys.push.apply(ownKeys, Object.getOwnPropertySymbols(source).filter(function (sym) {
        return Object.getOwnPropertyDescriptor(source, sym).enumerable;
      }));
    }
    ownKeys.forEach(function (key) {
      (0, _defineProperty.default)(target, key, source[key]);
    });
  }
  return target;
}

//# sourceMappingURL=objectSpread.js.map
