/**
 * lucide-react v0.0.1 - ISC
 */

import createLucideIcon from '../createLucideIcon.mjs';

const DatabaseBackup = createLucideIcon("DatabaseBackup", [
  ["ellipse", { cx: "12", cy: "5", rx: "9", ry: "3", key: "msslwz" }],
  ["path", { d: "M3 5v14c0 1.4 3 2.7 7 3", key: "jgylly" }],
  ["path", { d: "M3 12c0 1.2 2 2.5 5 3", key: "vxrdms" }],
  ["path", { d: "M21 5v4", key: "1vq2e7" }],
  [
    "path",
    {
      d: "M13 20a5 5 0 0 0 9-3 4.5 4.5 0 0 0-4.5-4.5c-1.33 0-2.54.54-3.41 1.41L12 16",
      key: "1f4ei9"
    }
  ],
  ["path", { d: "M12 12v4h4", key: "1bxaet" }]
]);

export { DatabaseBackup as default };
//# sourceMappingURL=database-backup.mjs.map
