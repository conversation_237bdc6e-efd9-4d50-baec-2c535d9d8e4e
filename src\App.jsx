import React, { useState } from 'react'
import { Toaster } from 'react-hot-toast'
import KIPLLayout from './components/Layout/KIPLLayout'
import PaymentSystem from './components/Payment/PaymentSystem'
import PaymentPage from './components/PaymentPage/PaymentPage'
import './App.css'

function App() {
  const [currentPage, setCurrentPage] = useState('payment')

  const handleNavigation = (page) => {
    setCurrentPage(page)
  }

  const renderContent = () => {
    switch (currentPage) {
      case 'payment':
        return <PaymentPage />
      case 'payment-system':
        return <PaymentSystem />
      case 'dashboard':
        return <div className="coming-soon">Dashboard - Coming Soon</div>
      case 'production-plan':
        return <div className="coming-soon">Production Plan - Coming Soon</div>
      default:
        return <PaymentPage />
    }
  }

  return (
    <div className="App react-payment-container">
      <KIPLLayout 
        currentPage={currentPage} 
        onNavigate={handleNavigation}
      >
        {renderContent()}
      </KIPLLayout>
      <Toaster 
        position="top-right"
        toastOptions={{
          duration: 4000,
          style: {
            background: 'var(--surface-color)',
            color: 'var(--text-primary)',
            border: '1px solid var(--border-color)',
          },
        }}
      />
    </div>
  )
}

export default App
