{"version": 3, "file": "errors.d.ts", "sourceRoot": "", "sources": ["../../src/types/errors.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,WAAW,EAAE,iBAAiB,EAAE,GAAG,EAAE,MAAM,UAAU,CAAC;AAC/D,OAAO,EAAE,mBAAmB,EAAE,KAAK,EAAE,YAAY,EAAE,KAAK,EAAE,MAAM,SAAS,CAAC;AAC1E,OAAO,EAAE,eAAe,EAAE,cAAc,EAAE,MAAM,aAAa,CAAC;AAE9D,MAAM,MAAM,OAAO,GAAG,MAAM,CAAC;AAE7B,MAAM,MAAM,mBAAmB,GAAG;KAC/B,CAAC,IAAI,MAAM,eAAe,CAAC,CAAC,EAAE,cAAc;CAC9C,GAAG;IACF,CAAC,GAAG,EAAE,MAAM,GAAG,cAAc,CAAC;CAC/B,CAAC;AAEF,MAAM,MAAM,UAAU,GAAG;IACvB,IAAI,EAAE,YAAY,CAAC,MAAM,eAAe,EAAE,MAAM,CAAC,CAAC;IAClD,IAAI,CAAC,EAAE,UAAU,CAAC;IAClB,GAAG,CAAC,EAAE,GAAG,CAAC;IACV,KAAK,CAAC,EAAE,mBAAmB,CAAC;IAC5B,OAAO,CAAC,EAAE,OAAO,CAAC;CACnB,CAAC;AAEF,MAAM,MAAM,WAAW,GAAG;IACxB,OAAO,CAAC,EAAE,OAAO,CAAC;IAClB,IAAI,CAAC,EAAE,YAAY,CAAC,MAAM,eAAe,EAAE,MAAM,CAAC,CAAC;IACnD,KAAK,CAAC,EAAE,mBAAmB,CAAC;CAC7B,CAAC;AAEF,MAAM,MAAM,YAAY,CAAC,CAAC,IAAI,CAAC,SAAS,mBAAmB,GAAG,IAAI,GAC9D,CAAC,GACD;KACG,CAAC,IAAI,MAAM,CAAC,CAAC,CAAC,GAAG,WAAW,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;CAClD,CAAC;AAEN,MAAM,MAAM,eAAe,CAAC,CAAC,SAAS,WAAW,GAAG,WAAW,IAAI;KAChE,CAAC,IAAI,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,SAAS,mBAAmB,GAAG,IAAI,GACpD,UAAU,GACV,CAAC,SAAS,MAAM,GAAG,QAAQ,MAAM,EAAE,GACjC,WAAW,GACX,CAAC,CAAC,CAAC,CAAC,SAAS,MAAM,GACjB,KAAK,CAAC,UAAU,EAAE,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GACxC,UAAU;CACnB,CAAC;AAEF,MAAM,MAAM,WAAW,GAAG,OAAO,CAAC;IAChC,IAAI,EAAE,MAAM,GAAG,MAAM,CAAC;IACtB,OAAO,EAAE,OAAO,CAAC;CAClB,CAAC,CAAC;AAEH,MAAM,MAAM,WAAW,CAAC,CAAC,SAAS,WAAW,GAAG,WAAW,IAAI,OAAO,CACpE,WAAW,SAAS,KAAK,CAAC,WAAW,CAAC,GAClC,GAAG,GACH,eAAe,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CACrC,GAAG;IACF,IAAI,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,WAAW,CAAC,GAAG,WAAW,CAAC;CAClD,CAAC;AAEF,MAAM,MAAM,mBAAmB,GAAG,OAAO,CACvC,MAAM,CAAC,iBAAiB,EAAE,UAAU,CAAC,CACtC,CAAC"}