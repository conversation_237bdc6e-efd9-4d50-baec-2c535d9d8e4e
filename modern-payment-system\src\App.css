.app {
  height: 100vh;
  position: relative;
  overflow: hidden;
}

.app-background {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
  background: #ffffff;
}

.app-content {
  position: relative;
  z-index: 1;
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.main-content {
  flex: 1;
  display: flex;
  align-items: stretch;
  justify-content: center;
  padding: 0;
  overflow: hidden;
}

/* Dark theme adjustments */
[data-theme="dark"] .app-background {
  background: #000000;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .payment-container {
    flex-direction: column;
  }

  .step-indicator-section {
    width: 100%;
    border-right: none;
    border-bottom: 1px solid var(--border);
  }

  .payment-content {
    width: 100%;
  }
}

/* Performance optimizations */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .app-background {
    background: #000000;
  }
}
