.payment-flow {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.payment-container {
  background: var(--surface);
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* Clean top progress bar */
.step-indicator-section {
  background: var(--surface);
  border-bottom: 1px solid var(--border);
  padding: var(--spacing-4) var(--spacing-6);
  flex-shrink: 0;
}

.payment-content {
  padding: var(--spacing-6);
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  background: var(--surface);
}

.step-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  margin-bottom: var(--spacing-6);
  padding-right: var(--spacing-4);
}

.payment-navigation {
  display: flex;
  align-items: center;
  gap: var(--spacing-6);
  padding: var(--spacing-4) 0;
  border-top: 1px solid var(--border);
  flex-shrink: 0;
}

.nav-spacer {
  flex: 1;
}

.nav-button {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-3);
  padding: var(--spacing-4) var(--spacing-6);
  border-radius: var(--radius-lg);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-semibold);
  transition: all var(--transition-normal);
  cursor: pointer;
  border: 1px solid var(--border);
  min-height: 48px;
  position: relative;
  overflow: hidden;
  background: var(--surface);
  color: var(--text-primary);
  min-width: 120px;
  justify-content: center;
}

.nav-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.nav-button:hover::before {
  left: 100%;
}

.nav-button.primary {
  background: var(--text-primary);
  color: var(--surface);
  border-color: var(--text-primary);
}

.nav-button.primary:hover:not(.disabled) {
  background: var(--text-secondary);
  border-color: var(--text-secondary);
}

.nav-button.secondary {
  background: var(--surface);
  color: var(--text-primary);
  border: 1px solid var(--border);
}

.nav-button.secondary:hover {
  background: var(--text-primary);
  color: var(--surface);
}

.nav-button.success {
  background: var(--text-primary);
  color: var(--surface);
  border-color: var(--text-primary);
  font-size: var(--font-size-sm);
  padding: var(--spacing-3) var(--spacing-5);
}

.nav-button.success:hover {
  background: var(--text-secondary);
  border-color: var(--text-secondary);
}

.nav-button.disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none !important;
}

.nav-button .material-icons {
  font-size: 20px;
}

/* Dark theme adjustments */
[data-theme="dark"] .payment-container {
  background: rgba(31, 41, 55, 0.95);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

[data-theme="dark"] .nav-button.secondary {
  background: var(--gray-800);
  color: var(--white);
  border: 1px solid var(--gray-700);
}

[data-theme="dark"] .nav-button.secondary:hover {
  background: var(--gray-700);
}

/* Responsive design */
@media (max-width: 768px) {
  .payment-container {
    border-radius: var(--radius-2xl);
    margin: var(--spacing-4);
  }
  
  .step-indicator-section {
    padding: var(--spacing-6) var(--spacing-4);
  }
  
  .payment-content {
    padding: var(--spacing-6) var(--spacing-4);
  }
  
  .payment-navigation {
    flex-direction: column;
    gap: var(--spacing-3);
  }
  
  .nav-spacer {
    display: none;
  }
  
  .nav-button {
    width: 100%;
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .payment-container {
    margin: var(--spacing-2);
    border-radius: var(--radius-xl);
  }
  
  .step-indicator-section {
    padding: var(--spacing-4);
  }
  
  .payment-content {
    padding: var(--spacing-4);
  }
  
  .nav-button {
    padding: var(--spacing-3) var(--spacing-4);
    font-size: var(--font-size-sm);
  }
  
  .nav-button.success {
    font-size: var(--font-size-base);
    padding: var(--spacing-4) var(--spacing-6);
  }
}

/* Animation enhancements */
@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.nav-button.primary:hover:not(.disabled) {
  background: linear-gradient(135deg, var(--primary-blue-light), var(--primary-blue));
}

.nav-button.success:hover {
  background: linear-gradient(135deg, #10b981, var(--success-green));
}

/* Focus states for accessibility */
.nav-button:focus-visible {
  outline: 2px solid var(--primary-blue);
  outline-offset: 2px;
}

/* Loading state */
.nav-button.loading {
  pointer-events: none;
}

.nav-button.loading::after {
  content: '';
  position: absolute;
  width: 16px;
  height: 16px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
