<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Next-Gen Payment System - Voice & Gesture Enabled</title>
    <script src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: radial-gradient(ellipse at top, #667eea 0%, #764ba2 50%, #1a1a2e 100%);
            min-height: 100vh;
            padding: 20px;
            overflow-x: hidden;
            position: relative;
        }

        /* Animated Background Particles */
        .particles {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 1;
        }

        .particle {
            position: absolute;
            width: 4px;
            height: 4px;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            animation: float 6s infinite linear;
        }

        @keyframes float {
            0% {
                transform: translateY(100vh) rotate(0deg);
                opacity: 0;
            }
            10% {
                opacity: 1;
            }
            90% {
                opacity: 1;
            }
            100% {
                transform: translateY(-100vh) rotate(360deg);
                opacity: 0;
            }
        }

        .payment-container {
            max-width: 1400px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(30px);
            border-radius: 32px;
            box-shadow: 0 40px 80px rgba(0, 0, 0, 0.3);
            overflow: hidden;
            position: relative;
            z-index: 10;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .holographic-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
            color: white;
            padding: 50px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .holographic-header::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: conic-gradient(from 0deg, transparent, rgba(255,255,255,0.1), transparent);
            animation: rotate 10s linear infinite;
        }

        @keyframes rotate {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .header-content {
            position: relative;
            z-index: 2;
        }

        .main-title {
            font-size: 42px;
            font-weight: 800;
            margin-bottom: 16px;
            background: linear-gradient(45deg, #fff, #a8edea, #fed6e3);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-shadow: 0 0 30px rgba(255,255,255,0.5);
        }

        .subtitle {
            font-size: 18px;
            opacity: 0.9;
            margin-bottom: 24px;
        }

        .voice-indicator {
            display: inline-flex;
            align-items: center;
            gap: 12px;
            background: rgba(255, 255, 255, 0.15);
            padding: 12px 24px;
            border-radius: 30px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            backdrop-filter: blur(10px);
        }

        .voice-pulse {
            width: 12px;
            height: 12px;
            background: #4ecdc4;
            border-radius: 50%;
            animation: voicePulse 1.5s infinite;
        }

        @keyframes voicePulse {
            0%, 100% { transform: scale(1); opacity: 1; }
            50% { transform: scale(1.5); opacity: 0.7; }
        }

        .main-layout {
            display: grid;
            grid-template-columns: 1fr 450px;
            gap: 40px;
            padding: 50px;
        }

        .payment-area {
            display: flex;
            flex-direction: column;
            gap: 40px;
        }

        .ai-command-center {
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
            color: white;
            padding: 30px;
            border-radius: 24px;
            position: relative;
            overflow: hidden;
        }

        .ai-command-center::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
            animation: scan 4s infinite;
        }

        @keyframes scan {
            0% { left: -100%; }
            100% { left: 100%; }
        }

        .command-header {
            display: flex;
            align-items: center;
            gap: 16px;
            margin-bottom: 20px;
        }

        .ai-avatar {
            width: 48px;
            height: 48px;
            background: linear-gradient(135deg, #4ecdc4, #44a08d);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            position: relative;
        }

        .ai-avatar::after {
            content: '';
            position: absolute;
            inset: -4px;
            border-radius: 50%;
            background: linear-gradient(45deg, #4ecdc4, #44a08d, #4ecdc4);
            z-index: -1;
            animation: glow 2s infinite;
        }

        @keyframes glow {
            0%, 100% { opacity: 0.5; }
            50% { opacity: 1; }
        }

        .voice-commands {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 16px;
            margin-top: 20px;
        }

        .voice-command {
            background: rgba(255, 255, 255, 0.1);
            padding: 16px;
            border-radius: 16px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
        }

        .voice-command:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
        }

        .command-icon {
            font-size: 24px;
            margin-bottom: 8px;
        }

        .command-text {
            font-size: 14px;
            font-weight: 500;
        }

        .gesture-zone {
            background: white;
            border-radius: 24px;
            padding: 40px;
            box-shadow: 0 16px 40px rgba(0, 0, 0, 0.1);
            position: relative;
            overflow: hidden;
        }

        .gesture-zone::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #667eea, #764ba2, #f093fb);
            background-size: 200% 100%;
            animation: gradientMove 3s ease-in-out infinite;
        }

        @keyframes gradientMove {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }

        .section-title {
            font-size: 28px;
            font-weight: 700;
            color: #1a1a2e;
            margin-bottom: 24px;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .payment-methods-3d {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 24px;
            perspective: 1000px;
        }

        .method-card-3d {
            background: white;
            border-radius: 20px;
            padding: 30px;
            cursor: pointer;
            transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
            transform-style: preserve-3d;
            border: 2px solid transparent;
            position: relative;
            overflow: hidden;
        }

        .method-card-3d::before {
            content: '';
            position: absolute;
            inset: 0;
            border-radius: 20px;
            padding: 2px;
            background: linear-gradient(45deg, #667eea, #764ba2, #f093fb);
            mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
            mask-composite: exclude;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .method-card-3d:hover::before {
            opacity: 1;
        }

        .method-card-3d:hover {
            transform: rotateY(10deg) rotateX(5deg) translateZ(20px);
            box-shadow: 0 25px 50px rgba(102, 126, 234, 0.3);
        }

        .method-card-3d.selected {
            transform: rotateY(-5deg) rotateX(-2deg) translateZ(10px);
            background: linear-gradient(135deg, #f8f9ff 0%, #ffffff 100%);
            border-color: #667eea;
            box-shadow: 0 20px 40px rgba(102, 126, 234, 0.4);
        }

        .card-icon-3d {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 32px;
            margin-bottom: 20px;
            position: relative;
            transform-style: preserve-3d;
        }

        .card-icon-3d::after {
            content: '';
            position: absolute;
            inset: 4px;
            border-radius: 16px;
            background: linear-gradient(135deg, rgba(255,255,255,0.9), rgba(255,255,255,0.5));
            transform: translateZ(-1px);
        }

        .method-title {
            font-size: 22px;
            font-weight: 700;
            color: #1a1a2e;
            margin-bottom: 12px;
        }

        .method-description {
            font-size: 14px;
            color: #6c757d;
            line-height: 1.5;
            margin-bottom: 20px;
        }

        .method-stats {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 16px;
            margin-bottom: 20px;
        }

        .stat-box {
            background: #f8f9fa;
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            transition: all 0.3s ease;
        }

        .method-card-3d:hover .stat-box {
            background: rgba(102, 126, 234, 0.1);
        }

        .stat-value {
            font-size: 18px;
            font-weight: 700;
            color: #1a1a2e;
            margin-bottom: 4px;
        }

        .stat-label {
            font-size: 12px;
            color: #6c757d;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .biometric-scanner {
            background: white;
            border-radius: 24px;
            padding: 30px;
            box-shadow: 0 16px 40px rgba(0, 0, 0, 0.1);
            text-align: center;
            margin-bottom: 30px;
        }

        .scanner-area {
            width: 120px;
            height: 120px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 50%;
            margin: 0 auto 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            cursor: pointer;
        }

        .scanner-area::before {
            content: '';
            position: absolute;
            inset: -4px;
            border-radius: 50%;
            background: linear-gradient(45deg, #667eea, #764ba2, #f093fb);
            z-index: -1;
            animation: scannerGlow 2s infinite;
        }

        @keyframes scannerGlow {
            0%, 100% { opacity: 0.5; transform: scale(1); }
            50% { opacity: 1; transform: scale(1.05); }
        }

        .scanner-icon {
            font-size: 48px;
            color: white;
        }

        .scanner-text {
            font-size: 16px;
            font-weight: 600;
            color: #1a1a2e;
            margin-bottom: 8px;
        }

        .scanner-subtitle {
            font-size: 14px;
            color: #6c757d;
        }

        .smart-summary {
            background: white;
            border-radius: 24px;
            padding: 30px;
            box-shadow: 0 16px 40px rgba(0, 0, 0, 0.1);
            position: sticky;
            top: 20px;
        }

        .summary-header {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 24px;
        }

        .summary-title {
            font-size: 24px;
            font-weight: 700;
            color: #1a1a2e;
        }

        .price-breakdown {
            display: flex;
            flex-direction: column;
            gap: 16px;
            margin-bottom: 24px;
        }

        .price-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 16px;
        }

        .price-item.total {
            border-top: 2px solid #e9ecef;
            padding-top: 16px;
            font-size: 24px;
            font-weight: 700;
            color: #1a1a2e;
        }

        .ai-pay-button {
            width: 100%;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
            color: white;
            border: none;
            border-radius: 20px;
            font-size: 20px;
            font-weight: 700;
            cursor: pointer;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
            margin-bottom: 20px;
        }

        .ai-pay-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            transition: left 0.6s ease;
        }

        .ai-pay-button:hover::before {
            left: 100%;
        }

        .ai-pay-button:hover {
            transform: translateY(-4px) scale(1.02);
            box-shadow: 0 20px 40px rgba(102, 126, 234, 0.5);
        }

        .ai-pay-button:active {
            transform: translateY(-2px) scale(1.01);
        }

        .security-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 12px;
            margin-top: 20px;
        }

        .security-item {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 12px;
            color: #6c757d;
        }

        @media (max-width: 768px) {
            .main-layout {
                grid-template-columns: 1fr;
                gap: 20px;
                padding: 20px;
            }
            
            .payment-methods-3d {
                grid-template-columns: 1fr;
            }
            
            .voice-commands {
                grid-template-columns: 1fr;
            }
            
            .main-title {
                font-size: 28px;
            }
        }
    </style>
</head>
<body>
    <!-- Animated Background Particles -->
    <div class="particles" id="particles"></div>

    <div id="root"></div>

    <script type="text/babel">
        const { useState, useEffect, useRef } = React;

        const NextGenPayment = () => {
            const [selectedMethod, setSelectedMethod] = useState('');
            const [isProcessing, setIsProcessing] = useState(false);
            const [voiceActive, setVoiceActive] = useState(false);
            const [biometricScanned, setBiometricScanned] = useState(false);
            const [gestureDetected, setGestureDetected] = useState('');
            const [aiStatus, setAiStatus] = useState('Ready');

            // Create animated particles
            useEffect(() => {
                const particlesContainer = document.getElementById('particles');
                const createParticle = () => {
                    const particle = document.createElement('div');
                    particle.className = 'particle';
                    particle.style.left = Math.random() * 100 + '%';
                    particle.style.animationDelay = Math.random() * 6 + 's';
                    particle.style.animationDuration = (Math.random() * 4 + 4) + 's';
                    particlesContainer.appendChild(particle);

                    setTimeout(() => {
                        particle.remove();
                    }, 8000);
                };

                const interval = setInterval(createParticle, 200);
                return () => clearInterval(interval);
            }, []);

            const paymentMethods = [
                {
                    id: 'neural-upi',
                    name: 'Neural UPI',
                    icon: '🧠',
                    description: 'AI-powered UPI with predictive fraud detection and neural network optimization',
                    speed: '0.5s',
                    security: '99.9%',
                    aiScore: 98
                },
                {
                    id: 'quantum-card',
                    name: 'Quantum Card',
                    icon: '⚛️',
                    description: 'Quantum-encrypted card payments with molecular-level security',
                    speed: '1.2s',
                    security: '99.8%',
                    aiScore: 95
                },
                {
                    id: 'biometric-wallet',
                    name: 'Biometric Wallet',
                    icon: '👁️',
                    description: 'Multi-modal biometric authentication with iris and voice recognition',
                    speed: '0.8s',
                    security: '99.95%',
                    aiScore: 97
                },
                {
                    id: 'voice-pay',
                    name: 'Voice Pay',
                    icon: '🎤',
                    description: 'Natural language payment processing with voice biometrics',
                    speed: '1.0s',
                    security: '99.7%',
                    aiScore: 92
                },
                {
                    id: 'gesture-pay',
                    name: 'Gesture Pay',
                    icon: '👋',
                    description: 'Hand gesture recognition with 3D spatial authentication',
                    speed: '0.7s',
                    security: '99.6%',
                    aiScore: 89
                },
                {
                    id: 'crypto-neural',
                    name: 'Neural Crypto',
                    icon: '🔮',
                    description: 'AI-optimized cryptocurrency with smart contract automation',
                    speed: '2.5s',
                    security: '99.9%',
                    aiScore: 94
                }
            ];

            const voiceCommands = [
                { icon: '💳', text: 'Pay with card', command: 'quantum-card' },
                { icon: '📱', text: 'Use UPI', command: 'neural-upi' },
                { icon: '👁️', text: 'Biometric scan', command: 'biometric-wallet' },
                { icon: '🎤', text: 'Voice payment', command: 'voice-pay' }
            ];

            const handleVoiceCommand = (command) => {
                setVoiceActive(true);
                setSelectedMethod(command);
                setTimeout(() => setVoiceActive(false), 2000);
            };

            const handleBiometricScan = () => {
                setBiometricScanned(true);
                setAiStatus('Scanning...');
                setTimeout(() => {
                    setBiometricScanned(false);
                    setAiStatus('Verified ✓');
                    setSelectedMethod('biometric-wallet');
                }, 3000);
            };

            const handleGestureDetection = () => {
                setGestureDetected('Wave detected');
                setSelectedMethod('gesture-pay');
                setTimeout(() => setGestureDetected(''), 2000);
            };

            const handlePayment = () => {
                setIsProcessing(true);
                setAiStatus('Processing with AI...');

                setTimeout(() => {
                    setIsProcessing(false);
                    setAiStatus('Payment Successful! 🎉');
                    alert('🚀 Next-Gen Payment Complete! Your transaction has been processed using advanced AI algorithms.');
                }, 4000);
            };

            return (
                <div className="payment-container">
                    {/* Holographic Header */}
                    <div className="holographic-header">
                        <div className="header-content">
                            <h1 className="main-title">Next-Gen Payment Portal</h1>
                            <p className="subtitle">
                                Experience the future of payments with AI, biometrics, and voice control
                            </p>
                            <div className="voice-indicator">
                                <div className={`voice-pulse ${voiceActive ? 'active' : ''}`}></div>
                                <span>Voice Commands Active</span>
                            </div>
                        </div>
                    </div>

                    <div className="main-layout">
                        <div className="payment-area">
                            {/* AI Command Center */}
                            <div className="ai-command-center">
                                <div className="command-header">
                                    <div className="ai-avatar">🤖</div>
                                    <div>
                                        <h3>AI Assistant</h3>
                                        <p>Status: {aiStatus}</p>
                                    </div>
                                </div>

                                <p>Try voice commands or gestures to control your payment experience</p>

                                <div className="voice-commands">
                                    {voiceCommands.map((cmd, index) => (
                                        <div
                                            key={index}
                                            className="voice-command"
                                            onClick={() => handleVoiceCommand(cmd.command)}
                                        >
                                            <div className="command-icon">{cmd.icon}</div>
                                            <div className="command-text">{cmd.text}</div>
                                        </div>
                                    ))}
                                </div>
                            </div>

                            {/* Payment Methods with 3D Effects */}
                            <div className="gesture-zone">
                                <h2 className="section-title">
                                    <span>🚀</span>
                                    Choose Your Payment Method
                                </h2>

                                <div className="payment-methods-3d">
                                    {paymentMethods.map((method) => (
                                        <div
                                            key={method.id}
                                            className={`method-card-3d ${selectedMethod === method.id ? 'selected' : ''}`}
                                            onClick={() => setSelectedMethod(method.id)}
                                        >
                                            <div className="card-icon-3d">
                                                <span>{method.icon}</span>
                                            </div>
                                            <h3 className="method-title">{method.name}</h3>
                                            <p className="method-description">{method.description}</p>

                                            <div className="method-stats">
                                                <div className="stat-box">
                                                    <div className="stat-value">{method.speed}</div>
                                                    <div className="stat-label">Speed</div>
                                                </div>
                                                <div className="stat-box">
                                                    <div className="stat-value">{method.security}</div>
                                                    <div className="stat-label">Security</div>
                                                </div>
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            </div>
                        </div>

                        {/* Advanced Sidebar */}
                        <div>
                            {/* Biometric Scanner */}
                            <div className="biometric-scanner">
                                <div
                                    className="scanner-area"
                                    onClick={handleBiometricScan}
                                >
                                    <div className="scanner-icon">
                                        {biometricScanned ? '🔄' : '👁️'}
                                    </div>
                                </div>
                                <div className="scanner-text">Biometric Authentication</div>
                                <div className="scanner-subtitle">
                                    {biometricScanned ? 'Scanning...' : 'Tap to scan'}
                                </div>
                            </div>

                            {/* Smart Summary */}
                            <div className="smart-summary">
                                <div className="summary-header">
                                    <span>🧮</span>
                                    <h3 className="summary-title">AI Summary</h3>
                                </div>

                                <div className="price-breakdown">
                                    <div className="price-item">
                                        <span>Base Amount</span>
                                        <span>€39.99</span>
                                    </div>
                                    <div className="price-item">
                                        <span>AI Optimization</span>
                                        <span style={{color: '#28a745'}}>-€3.00</span>
                                    </div>
                                    <div className="price-item">
                                        <span>Neural Processing</span>
                                        <span>€2.50</span>
                                    </div>
                                    <div className="price-item">
                                        <span>Quantum Security</span>
                                        <span>€3.50</span>
                                    </div>
                                    <div className="price-item total">
                                        <span>Total</span>
                                        <span>€42.99</span>
                                    </div>
                                </div>

                                {selectedMethod && (
                                    <div style={{
                                        background: 'linear-gradient(135deg, #f8f9ff 0%, #e8f2ff 100%)',
                                        padding: '20px',
                                        borderRadius: '16px',
                                        marginBottom: '20px',
                                        border: '1px solid rgba(102, 126, 234, 0.2)'
                                    }}>
                                        <div style={{fontSize: '16px', fontWeight: '600', marginBottom: '8px'}}>
                                            Selected: {paymentMethods.find(m => m.id === selectedMethod)?.name}
                                        </div>
                                        <div style={{fontSize: '14px', color: '#6c757d'}}>
                                            AI Score: {paymentMethods.find(m => m.id === selectedMethod)?.aiScore}/100
                                        </div>
                                    </div>
                                )}

                                <button
                                    className="ai-pay-button"
                                    onClick={handlePayment}
                                    disabled={isProcessing || !selectedMethod}
                                >
                                    {isProcessing ? (
                                        <>🔄 AI Processing...</>
                                    ) : (
                                        <>🚀 Pay €42.99 with AI</>
                                    )}
                                </button>

                                <div className="security-grid">
                                    <div className="security-item">
                                        <span>🛡️</span>
                                        <span>Quantum Encrypted</span>
                                    </div>
                                    <div className="security-item">
                                        <span>🧠</span>
                                        <span>AI Protected</span>
                                    </div>
                                    <div className="security-item">
                                        <span>👁️</span>
                                        <span>Biometric Secured</span>
                                    </div>
                                    <div className="security-item">
                                        <span>🎤</span>
                                        <span>Voice Verified</span>
                                    </div>
                                </div>
                            </div>

                            {/* Gesture Detection */}
                            <div style={{
                                background: 'white',
                                borderRadius: '20px',
                                padding: '20px',
                                textAlign: 'center',
                                boxShadow: '0 8px 24px rgba(0, 0, 0, 0.1)'
                            }}>
                                <h4 style={{marginBottom: '12px'}}>Gesture Control</h4>
                                <button
                                    onClick={handleGestureDetection}
                                    style={{
                                        background: 'linear-gradient(135deg, #4ecdc4, #44a08d)',
                                        color: 'white',
                                        border: 'none',
                                        padding: '12px 24px',
                                        borderRadius: '12px',
                                        cursor: 'pointer',
                                        fontSize: '14px',
                                        fontWeight: '600'
                                    }}
                                >
                                    👋 Wave to Pay
                                </button>
                                {gestureDetected && (
                                    <div style={{
                                        marginTop: '12px',
                                        color: '#28a745',
                                        fontSize: '14px',
                                        fontWeight: '500'
                                    }}>
                                        {gestureDetected}
                                    </div>
                                )}
                            </div>
                        </div>
                    </div>
                </div>
            );
        };

        ReactDOM.render(<NextGenPayment />, document.getElementById('root'));
    </script>
</body>
</html>
