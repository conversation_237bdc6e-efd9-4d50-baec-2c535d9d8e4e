import React, { useState, useEffect } from 'react'
import { Toaster } from 'react-hot-toast'
import { motion, AnimatePresence } from 'framer-motion'
import PaymentFlow from './components/PaymentFlow/PaymentFlow'
import Header from './components/Layout/Header'
import Footer from './components/Layout/Footer'
import LoadingScreen from './components/UI/LoadingScreen'
import './App.css'

function App() {
  const [isLoading, setIsLoading] = useState(true)
  const [currentTheme, setCurrentTheme] = useState('light')

  useEffect(() => {
    // Simulate initial loading
    const timer = setTimeout(() => {
      setIsLoading(false)
    }, 1500)

    // Check for saved theme preference
    const savedTheme = localStorage.getItem('payment-theme')
    if (savedTheme) {
      setCurrentTheme(savedTheme)
      document.documentElement.setAttribute('data-theme', savedTheme)
    }

    return () => clearTimeout(timer)
  }, [])

  const toggleTheme = () => {
    const newTheme = currentTheme === 'light' ? 'dark' : 'light'
    setCurrentTheme(newTheme)
    localStorage.setItem('payment-theme', newTheme)
    document.documentElement.setAttribute('data-theme', newTheme)
  }

  if (isLoading) {
    return <LoadingScreen />
  }

  return (
    <div className="app" data-theme={currentTheme}>
      <div className="app-background">
        <div className="gradient-orb orb-1"></div>
        <div className="gradient-orb orb-2"></div>
        <div className="gradient-orb orb-3"></div>
      </div>
      
      <div className="app-content">
        <Header currentTheme={currentTheme} onThemeToggle={toggleTheme} />
        
        <main className="main-content">
          <AnimatePresence mode="wait">
            <motion.div
              key="payment-flow"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.5, ease: "easeInOut" }}
            >
              <PaymentFlow />
            </motion.div>
          </AnimatePresence>
        </main>
        
        <Footer />
      </div>

      {/* Toast notifications */}
      <Toaster
        position="top-right"
        toastOptions={{
          duration: 4000,
          style: {
            background: 'var(--surface-elevated)',
            color: 'var(--text-primary)',
            border: '1px solid var(--border)',
            borderRadius: 'var(--radius-lg)',
            boxShadow: 'var(--shadow-lg)',
            fontSize: 'var(--font-size-sm)',
            fontFamily: 'var(--font-family)',
          },
          success: {
            iconTheme: {
              primary: 'var(--success-green)',
              secondary: 'var(--white)',
            },
          },
          error: {
            iconTheme: {
              primary: 'var(--error-red)',
              secondary: 'var(--white)',
            },
          },
          loading: {
            iconTheme: {
              primary: 'var(--primary-blue)',
              secondary: 'var(--white)',
            },
          },
        }}
      />
    </div>
  )
}

export default App
