<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Compact Payment System - Minimal Clicks & Scroll</title>
    <script src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8f9fa;
            color: #000000;
            line-height: 1.5;
        }

        .payment-container {
            max-width: 1200px;
            margin: 20px auto;
            background: #ffffff;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .header {
            background: #000000;
            color: #ffffff;
            padding: 20px 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header-left h1 {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 4px;
        }

        .header-left p {
            font-size: 14px;
            opacity: 0.9;
        }

        .progress-mini {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 12px;
        }

        .progress-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #666666;
        }

        .progress-dot.active {
            background: #ffffff;
        }

        .main-layout {
            display: grid;
            grid-template-columns: 1fr 350px;
            gap: 30px;
            padding: 30px;
            max-height: 80vh;
        }

        .payment-area {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .quick-section {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
        }

        .section-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 16px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .payment-tabs {
            display: flex;
            background: #e9ecef;
            border-radius: 6px;
            padding: 4px;
            margin-bottom: 16px;
        }

        .tab-btn {
            flex: 1;
            padding: 8px 12px;
            background: none;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            font-weight: 500;
            transition: all 0.2s ease;
        }

        .tab-btn.active {
            background: #ffffff;
            color: #000000;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .saved-methods {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 12px;
            margin-bottom: 16px;
        }

        .saved-method {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 12px;
            background: #ffffff;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.2s ease;
            position: relative;
        }

        .saved-method:hover {
            border-color: #000000;
            transform: translateY(-1px);
        }

        .saved-method.selected {
            border-color: #000000;
            background: #f0f8ff;
        }

        .method-icon-small {
            width: 32px;
            height: 32px;
            background: #f0f0f0;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
        }

        .method-info-compact {
            flex: 1;
        }

        .method-name {
            font-size: 13px;
            font-weight: 600;
            margin-bottom: 2px;
        }

        .method-detail {
            font-size: 11px;
            color: #666666;
        }

        .remove-x {
            position: absolute;
            top: -6px;
            right: -6px;
            width: 16px;
            height: 16px;
            background: #dc3545;
            color: #ffffff;
            border: none;
            border-radius: 50%;
            font-size: 10px;
            cursor: pointer;
            display: none;
        }

        .saved-method:hover .remove-x {
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .new-methods {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 12px;
        }

        .method-card-compact {
            padding: 16px;
            background: #ffffff;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.2s ease;
            text-align: center;
        }

        .method-card-compact:hover {
            border-color: #000000;
            transform: translateY(-1px);
        }

        .method-card-compact.selected {
            border-color: #000000;
            background: #f0f8ff;
        }

        .method-icon-large {
            width: 40px;
            height: 40px;
            background: #f0f0f0;
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            margin: 0 auto 8px;
        }

        .method-title {
            font-size: 12px;
            font-weight: 600;
            margin-bottom: 4px;
        }

        .method-meta {
            font-size: 10px;
            color: #666666;
        }

        .card-form-inline {
            background: #ffffff;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
            padding: 16px;
            margin-top: 12px;
            animation: slideDown 0.2s ease;
        }

        @keyframes slideDown {
            from { opacity: 0; height: 0; }
            to { opacity: 1; height: auto; }
        }

        .form-row {
            display: grid;
            grid-template-columns: 2fr 1fr 1fr;
            gap: 12px;
            margin-bottom: 12px;
        }

        .form-input-compact {
            padding: 8px 10px;
            border: 1px solid #e0e0e0;
            border-radius: 4px;
            font-size: 12px;
            outline: none;
        }

        .form-input-compact:focus {
            border-color: #000000;
        }

        .form-input-compact::placeholder {
            color: #999999;
            font-size: 11px;
        }

        .sidebar {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            height: fit-content;
            position: sticky;
            top: 20px;
        }

        .order-summary-compact {
            margin-bottom: 20px;
        }

        .summary-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            font-size: 14px;
        }

        .summary-total {
            border-top: 1px solid #e0e0e0;
            padding-top: 12px;
            font-size: 18px;
            font-weight: 700;
        }

        .pay-btn-large {
            width: 100%;
            padding: 16px;
            background: #000000;
            color: #ffffff;
            border: none;
            border-radius: 6px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            margin-bottom: 16px;
            transition: all 0.2s ease;
        }

        .pay-btn-large:hover:not(:disabled) {
            background: #333333;
            transform: translateY(-1px);
        }

        .pay-btn-large:disabled {
            background: #cccccc;
            cursor: not-allowed;
        }

        .security-badges {
            display: flex;
            justify-content: center;
            gap: 12px;
            font-size: 11px;
            color: #666666;
            margin-bottom: 16px;
        }

        .info-compact {
            background: #ffffff;
            border-radius: 6px;
            padding: 12px;
            margin-bottom: 12px;
        }

        .info-title {
            font-size: 12px;
            font-weight: 600;
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .info-list {
            font-size: 11px;
            color: #666666;
            line-height: 1.4;
        }

        .info-item {
            margin-bottom: 4px;
        }

        .add-card-btn {
            grid-column: 1 / -1;
            background: none;
            border: 1px dashed #cccccc;
            color: #666666;
            padding: 12px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.2s ease;
        }

        .add-card-btn:hover {
            border-color: #000000;
            color: #000000;
        }

        @media (max-width: 768px) {
            .main-layout {
                grid-template-columns: 1fr;
                gap: 20px;
                padding: 20px;
            }
            
            .saved-methods {
                grid-template-columns: 1fr;
            }
            
            .new-methods {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .form-row {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div id="root"></div>

    <script type="text/babel">
        const { useState } = React;

        const CompactPaymentPage = () => {
            const [activeTab, setActiveTab] = useState('saved');
            const [selectedMethod, setSelectedMethod] = useState('');
            const [showCardForm, setShowCardForm] = useState(false);
            const [isProcessing, setIsProcessing] = useState(false);
            const [cardDetails, setCardDetails] = useState({
                cardNumber: '',
                expiryDate: '',
                cvv: '',
                cardholderName: ''
            });

            const [savedMethods, setSavedMethods] = useState([
                {
                    id: 1,
                    type: 'VISA',
                    details: '**** 1234',
                    icon: '💳',
                    lastUsed: '2 days ago'
                },
                {
                    id: 2,
                    type: 'UPI',
                    details: 'user@upi',
                    icon: '📱',
                    lastUsed: 'Yesterday'
                },
                {
                    id: 3,
                    type: 'Mastercard',
                    details: '**** 5678',
                    icon: '💳',
                    lastUsed: '1 week ago'
                },
                {
                    id: 4,
                    type: 'Wallet',
                    details: 'Paytm',
                    icon: '👛',
                    lastUsed: '3 days ago'
                }
            ]);

            const newMethods = [
                { id: 'card', name: 'Card', icon: '💳', meta: 'Visa, Master' },
                { id: 'upi', name: 'UPI', icon: '📱', meta: 'Instant' },
                { id: 'wallet', name: 'Wallet', icon: '👛', meta: 'Quick pay' },
                { id: 'netbanking', name: 'Banking', icon: '🏦', meta: 'All banks' },
                { id: 'cod', name: 'COD', icon: '💵', meta: '₹40 fee' },
                { id: 'bnpl', name: 'BNPL', icon: '📅', meta: 'Pay later' }
            ];

            const handleMethodSelect = (method, isNew = false) => {
                if (isNew) {
                    setSelectedMethod(method.id);
                    if (method.id === 'card') {
                        setShowCardForm(true);
                        setCardDetails({
                            cardNumber: '',
                            expiryDate: '',
                            cvv: '',
                            cardholderName: ''
                        });
                    } else {
                        setShowCardForm(false);
                    }
                } else {
                    setSelectedMethod(method.id);
                    if (method.type === 'VISA' || method.type === 'Mastercard') {
                        setShowCardForm(true);
                        setCardDetails({
                            cardNumber: '',
                            expiryDate: '',
                            cvv: '',
                            cardholderName: ''
                        });
                    } else {
                        setShowCardForm(false);
                    }
                }
            };

            const handleCardInputChange = (field, value) => {
                setCardDetails(prev => ({
                    ...prev,
                    [field]: value
                }));
            };

            const formatCardNumber = (value) => {
                const v = value.replace(/\s+/g, '').replace(/[^0-9]/gi, '');
                const matches = v.match(/\d{4,16}/g);
                const match = matches && matches[0] || '';
                const parts = [];
                for (let i = 0, len = match.length; i < len; i += 4) {
                    parts.push(match.substring(i, i + 4));
                }
                return parts.length ? parts.join(' ') : v;
            };

            const formatExpiryDate = (value) => {
                const v = value.replace(/\s+/g, '').replace(/[^0-9]/gi, '');
                if (v.length >= 2) {
                    return v.substring(0, 2) + '/' + v.substring(2, 4);
                }
                return v;
            };

            const removeMethod = (methodId) => {
                setSavedMethods(prev => prev.filter(method => method.id !== methodId));
            };

            const handlePayment = () => {
                if (!selectedMethod) {
                    alert('Please select a payment method');
                    return;
                }

                if (showCardForm && (!cardDetails.cardNumber || !cardDetails.expiryDate || !cardDetails.cvv || !cardDetails.cardholderName)) {
                    alert('Please fill all card details');
                    return;
                }

                setIsProcessing(true);
                setTimeout(() => {
                    setIsProcessing(false);
                    alert('🎉 Payment successful!');
                    setShowCardForm(false);
                    setSelectedMethod('');
                }, 2000);
            };

            return (
                <div className="payment-container">
                    {/* Compact Header */}
                    <div className="header">
                        <div className="header-left">
                            <h1>Secure Payment</h1>
                            <p>Choose your payment method</p>
                        </div>
                        <div className="progress-mini">
                            <div className="progress-dot"></div>
                            <div className="progress-dot"></div>
                            <div className="progress-dot active"></div>
                            <span>Payment</span>
                        </div>
                    </div>

                    <div className="main-layout">
                        <div className="payment-area">
                            {/* Payment Methods Section */}
                            <div className="quick-section">
                                <h2 className="section-title">
                                    <span>💳</span>
                                    Payment Methods
                                </h2>

                                {/* Tabs */}
                                <div className="payment-tabs">
                                    <button
                                        className={`tab-btn ${activeTab === 'saved' ? 'active' : ''}`}
                                        onClick={() => setActiveTab('saved')}
                                    >
                                        Saved ({savedMethods.length})
                                    </button>
                                    <button
                                        className={`tab-btn ${activeTab === 'new' ? 'active' : ''}`}
                                        onClick={() => setActiveTab('new')}
                                    >
                                        All Methods
                                    </button>
                                </div>

                                {/* Saved Methods */}
                                {activeTab === 'saved' && (
                                    <div className="saved-methods">
                                        {savedMethods.map((method) => (
                                            <div
                                                key={method.id}
                                                className={`saved-method ${selectedMethod === method.id ? 'selected' : ''}`}
                                                onClick={() => handleMethodSelect(method)}
                                            >
                                                <div className="method-icon-small">{method.icon}</div>
                                                <div className="method-info-compact">
                                                    <div className="method-name">{method.type} {method.details}</div>
                                                    <div className="method-detail">{method.lastUsed}</div>
                                                </div>
                                                <button
                                                    className="remove-x"
                                                    onClick={(e) => {
                                                        e.stopPropagation();
                                                        removeMethod(method.id);
                                                    }}
                                                >
                                                    ×
                                                </button>
                                            </div>
                                        ))}
                                        <button
                                            className="add-card-btn"
                                            onClick={() => {
                                                setActiveTab('new');
                                                setSelectedMethod('card');
                                                setShowCardForm(true);
                                            }}
                                        >
                                            + Add New Card
                                        </button>
                                    </div>
                                )}

                                {/* New Methods */}
                                {activeTab === 'new' && (
                                    <div className="new-methods">
                                        {newMethods.map((method) => (
                                            <div
                                                key={method.id}
                                                className={`method-card-compact ${selectedMethod === method.id ? 'selected' : ''}`}
                                                onClick={() => handleMethodSelect(method, true)}
                                            >
                                                <div className="method-icon-large">{method.icon}</div>
                                                <div className="method-title">{method.name}</div>
                                                <div className="method-meta">{method.meta}</div>
                                            </div>
                                        ))}
                                    </div>
                                )}

                                {/* Inline Card Form */}
                                {showCardForm && (
                                    <div className="card-form-inline">
                                        <div className="form-row">
                                            <input
                                                type="text"
                                                className="form-input-compact"
                                                placeholder="Cardholder Name"
                                                value={cardDetails.cardholderName}
                                                onChange={(e) => handleCardInputChange('cardholderName', e.target.value)}
                                            />
                                            <input
                                                type="text"
                                                className="form-input-compact"
                                                placeholder="MM/YY"
                                                value={cardDetails.expiryDate}
                                                onChange={(e) => handleCardInputChange('expiryDate', formatExpiryDate(e.target.value))}
                                                maxLength="5"
                                            />
                                            <input
                                                type="text"
                                                className="form-input-compact"
                                                placeholder="CVV"
                                                value={cardDetails.cvv}
                                                onChange={(e) => handleCardInputChange('cvv', e.target.value.replace(/\D/g, '').slice(0, 3))}
                                                maxLength="3"
                                            />
                                        </div>
                                        <input
                                            type="text"
                                            className="form-input-compact"
                                            placeholder="Card Number (1234 5678 9012 3456)"
                                            value={cardDetails.cardNumber}
                                            onChange={(e) => handleCardInputChange('cardNumber', formatCardNumber(e.target.value))}
                                            maxLength="19"
                                            style={{width: '100%'}}
                                        />
                                    </div>
                                )}
                            </div>
                        </div>

                        {/* Compact Sidebar */}
                        <div className="sidebar">
                            {/* Order Summary */}
                            <div className="order-summary-compact">
                                <h3 className="section-title">
                                    <span>📋</span>
                                    Order Total
                                </h3>
                                <div className="summary-row">
                                    <span>Subtotal</span>
                                    <span>€39.99</span>
                                </div>
                                <div className="summary-row">
                                    <span>Shipping</span>
                                    <span>€2.99</span>
                                </div>
                                <div className="summary-row">
                                    <span>Tax</span>
                                    <span>€3.20</span>
                                </div>
                                <div className="summary-row" style={{color: '#28a745'}}>
                                    <span>Discount</span>
                                    <span>-€3.19</span>
                                </div>
                                <div className="summary-row summary-total">
                                    <span>Total</span>
                                    <span>€42.99</span>
                                </div>
                            </div>

                            <button
                                className="pay-btn-large"
                                onClick={handlePayment}
                                disabled={isProcessing || !selectedMethod}
                            >
                                {isProcessing ? 'Processing...' : 'Pay €42.99'}
                            </button>

                            <div className="security-badges">
                                <span>🔒 SSL</span>
                                <span>🛡️ PCI</span>
                                <span>✓ Verified</span>
                            </div>

                            {/* Compact Info */}
                            <div className="info-compact">
                                <div className="info-title">
                                    <span>💡</span>
                                    Quick Tips
                                </div>
                                <div className="info-list">
                                    <div className="info-item">• UPI: Instant & Free</div>
                                    <div className="info-item">• COD: ₹40 handling fee</div>
                                    <div className="info-item">• Cards: EMI available</div>
                                </div>
                            </div>

                            <div className="info-compact">
                                <div className="info-title">
                                    <span>🎧</span>
                                    Support
                                </div>
                                <div className="info-list">
                                    <div className="info-item">📞 1800-123-4567</div>
                                    <div className="info-item">💬 Live Chat Available</div>
                                </div>
                            </div>

                            <div className="info-compact">
                                <div className="info-title">
                                    <span>🏆</span>
                                    Trust
                                </div>
                                <div className="info-list">
                                    <div className="info-item">10M+ Happy Customers</div>
                                    <div className="info-item">99.9% Success Rate</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            );
        };

        ReactDOM.render(<CompactPaymentPage />, document.getElementById('root'));
    </script>
</body>
</html>
