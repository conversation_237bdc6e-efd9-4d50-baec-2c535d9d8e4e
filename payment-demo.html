<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>KIPL Payment System Demo</title>
  
  <!-- Material Icons -->
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
  
  <!-- KIPL Styles -->
  <link rel="stylesheet" href="styles.css">
  
  <style>
    .demo-container {
      max-width: 1200px;
      margin: 0 auto;
      padding: var(--spacing-xl);
      background: var(--background-color);
      min-height: 100vh;
    }
    
    .demo-header {
      text-align: center;
      margin-bottom: var(--spacing-2xl);
      background: var(--surface-color);
      padding: var(--spacing-2xl);
      border-radius: var(--radius-lg);
      box-shadow: var(--shadow-lg);
    }
    
    .demo-title {
      font-size: 2.5rem;
      font-weight: 700;
      color: var(--kipl-sidebar-blue);
      margin-bottom: var(--spacing-md);
    }
    
    .demo-subtitle {
      font-size: var(--font-size-lg);
      color: var(--text-secondary);
      margin-bottom: var(--spacing-xl);
    }
    
    .demo-features {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: var(--spacing-xl);
      margin-bottom: var(--spacing-2xl);
    }
    
    .feature-card {
      background: var(--surface-color);
      border-radius: var(--radius-lg);
      padding: var(--spacing-xl);
      box-shadow: var(--shadow-md);
      border: 1px solid var(--border-light);
      transition: all var(--transition-fast);
    }
    
    .feature-card:hover {
      transform: translateY(-4px);
      box-shadow: var(--shadow-xl);
    }
    
    .feature-icon {
      width: 60px;
      height: 60px;
      background: linear-gradient(135deg, var(--kipl-sidebar-blue), var(--kipl-header-orange));
      border-radius: var(--radius-lg);
      display: flex;
      align-items: center;
      justify-content: center;
      color: var(--text-white);
      margin-bottom: var(--spacing-lg);
      font-size: 24px;
    }
    
    .feature-title {
      font-size: var(--font-size-xl);
      font-weight: 600;
      color: var(--text-primary);
      margin-bottom: var(--spacing-md);
    }
    
    .feature-description {
      color: var(--text-secondary);
      line-height: 1.6;
      margin-bottom: var(--spacing-lg);
    }
    
    .feature-list {
      list-style: none;
      padding: 0;
    }
    
    .feature-list li {
      display: flex;
      align-items: center;
      gap: var(--spacing-sm);
      margin-bottom: var(--spacing-sm);
      font-size: var(--font-size-sm);
      color: var(--text-primary);
    }
    
    .feature-list li::before {
      content: '✓';
      color: var(--status-completed);
      font-weight: 600;
    }
    
    .demo-actions {
      display: flex;
      justify-content: center;
      gap: var(--spacing-lg);
      margin-bottom: var(--spacing-2xl);
    }
    
    .demo-btn {
      display: inline-flex;
      align-items: center;
      gap: var(--spacing-sm);
      padding: var(--spacing-lg) var(--spacing-2xl);
      border: none;
      border-radius: var(--radius-md);
      font-size: var(--font-size-base);
      font-weight: 600;
      cursor: pointer;
      transition: all var(--transition-fast);
      text-decoration: none;
      min-height: 50px;
    }
    
    .demo-btn.primary {
      background: var(--kipl-sidebar-blue);
      color: var(--text-white);
    }
    
    .demo-btn.primary:hover {
      background: #1e3a6f;
      transform: translateY(-2px);
      box-shadow: var(--shadow-lg);
    }
    
    .demo-btn.secondary {
      background: var(--kipl-header-orange);
      color: var(--text-white);
    }
    
    .demo-btn.secondary:hover {
      background: #e69500;
      transform: translateY(-2px);
      box-shadow: var(--shadow-lg);
    }
    
    .tech-stack {
      background: var(--surface-color);
      border-radius: var(--radius-lg);
      padding: var(--spacing-xl);
      box-shadow: var(--shadow-md);
      border: 1px solid var(--border-light);
    }
    
    .tech-title {
      font-size: var(--font-size-xl);
      font-weight: 600;
      color: var(--text-primary);
      margin-bottom: var(--spacing-lg);
      text-align: center;
    }
    
    .tech-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: var(--spacing-lg);
    }
    
    .tech-item {
      display: flex;
      align-items: center;
      gap: var(--spacing-md);
      padding: var(--spacing-md);
      background: var(--kipl-table-header);
      border-radius: var(--radius-md);
    }
    
    .tech-icon {
      width: 40px;
      height: 40px;
      background: var(--kipl-sidebar-blue);
      border-radius: var(--radius-sm);
      display: flex;
      align-items: center;
      justify-content: center;
      color: var(--text-white);
    }
    
    .tech-info h4 {
      font-size: var(--font-size-base);
      font-weight: 600;
      color: var(--text-primary);
      margin-bottom: var(--spacing-xs);
    }
    
    .tech-info p {
      font-size: var(--font-size-sm);
      color: var(--text-secondary);
    }
    
    @media (max-width: 768px) {
      .demo-actions {
        flex-direction: column;
        align-items: center;
      }
      
      .demo-btn {
        width: 100%;
        max-width: 300px;
        justify-content: center;
      }
    }
  </style>
</head>
<body>
  <div class="demo-container">
    <div class="demo-header">
      <h1 class="demo-title">KIPL Payment System</h1>
      <p class="demo-subtitle">Modern React.js Payment Interface with Advanced UX Features</p>
      
      <div class="demo-actions">
        <a href="index.html" class="demo-btn primary">
          <span class="material-icons">payment</span>
          Launch React Payment System
        </a>
        <a href="Komatsu.html" class="demo-btn secondary">
          <span class="material-icons">dashboard</span>
          View Original KIPL System
        </a>
      </div>
    </div>

    <div class="demo-features">
      <div class="feature-card">
        <div class="feature-icon">
          <span class="material-icons">payment</span>
        </div>
        <h3 class="feature-title">Smart Payment Flow</h3>
        <p class="feature-description">
          4-step guided payment process with real-time validation and progress tracking.
        </p>
        <ul class="feature-list">
          <li>Multiple payment methods (UPI, Cards, Wallets, COD)</li>
          <li>Saved payment methods with quick access</li>
          <li>Real-time fees and processing time display</li>
          <li>Method recommendations based on popularity</li>
        </ul>
      </div>

      <div class="feature-card">
        <div class="feature-icon">
          <span class="material-icons">local_shipping</span>
        </div>
        <h3 class="feature-title">Dynamic Delivery Options</h3>
        <p class="feature-description">
          Flexible delivery options with real-time pricing and offer integration.
        </p>
        <ul class="feature-list">
          <li>Same day, next day, and standard delivery</li>
          <li>Dynamic pricing based on delivery speed</li>
          <li>Automatic offer detection and application</li>
          <li>Real-time cost calculation and breakdown</li>
        </ul>
      </div>

      <div class="feature-card">
        <div class="feature-icon">
          <span class="material-icons">inventory</span>
        </div>
        <h3 class="feature-title">Product-Level Configuration</h3>
        <p class="feature-description">
          Configure payment methods individually for each product in your order.
        </p>
        <ul class="feature-list">
          <li>Mixed payment options per product</li>
          <li>Product-specific pricing and discounts</li>
          <li>Compatibility checks and recommendations</li>
          <li>Real-time total calculation</li>
        </ul>
      </div>

      <div class="feature-card">
        <div class="feature-icon">
          <span class="material-icons">security</span>
        </div>
        <h3 class="feature-title">Security & Integration</h3>
        <p class="feature-description">
          Enterprise-grade security with seamless KIPL system integration.
        </p>
        <ul class="feature-list">
          <li>Encrypted payment processing</li>
          <li>Invoice generation and download</li>
          <li>Notification preferences management</li>
          <li>Finance system integration (Tally/SAP)</li>
        </ul>
      </div>
    </div>

    <div class="tech-stack">
      <h3 class="tech-title">Technology Stack</h3>
      <div class="tech-grid">
        <div class="tech-item">
          <div class="tech-icon">
            <span class="material-icons">code</span>
          </div>
          <div class="tech-info">
            <h4>React 18.2.0</h4>
            <p>Modern React with hooks</p>
          </div>
        </div>
        
        <div class="tech-item">
          <div class="tech-icon">
            <span class="material-icons">animation</span>
          </div>
          <div class="tech-info">
            <h4>Framer Motion</h4>
            <p>Smooth animations</p>
          </div>
        </div>
        
        <div class="tech-item">
          <div class="tech-icon">
            <span class="material-icons">palette</span>
          </div>
          <div class="tech-info">
            <h4>KIPL Design System</h4>
            <p>Consistent branding</p>
          </div>
        </div>
        
        <div class="tech-item">
          <div class="tech-icon">
            <span class="material-icons">devices</span>
          </div>
          <div class="tech-info">
            <h4>Responsive Design</h4>
            <p>Mobile-first approach</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</body>
</html>
