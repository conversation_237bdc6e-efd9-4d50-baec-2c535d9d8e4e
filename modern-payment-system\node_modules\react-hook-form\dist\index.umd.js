!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports,require("react")):"function"==typeof define&&define.amd?define(["exports","react"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).ReactHookForm={},e.React)}(this,(function(e,t){"use strict";function r(e){var t=Object.create(null);return e&&Object.keys(e).forEach((function(r){if("default"!==r){var s=Object.getOwnPropertyDescriptor(e,r);Object.defineProperty(t,r,s.get?s:{enumerable:!0,get:function(){return e[r]}})}})),t.default=e,Object.freeze(t)}var s=r(t),a=e=>"checkbox"===e.type,i=e=>e instanceof Date,n=e=>null==e;const o=e=>"object"==typeof e;var l=e=>!n(e)&&!Array.isArray(e)&&o(e)&&!i(e),u=e=>l(e)&&e.target?a(e.target)?e.target.checked:e.target.value:e,d=(e,t)=>e.has((e=>e.substring(0,e.search(/\.\d+(\.|$)/))||e)(t)),c="undefined"!=typeof window&&void 0!==window.HTMLElement&&"undefined"!=typeof document;function f(e){let t;const r=Array.isArray(e),s="undefined"!=typeof FileList&&e instanceof FileList;if(e instanceof Date)t=new Date(e);else if(e instanceof Set)t=new Set(e);else{if(c&&(e instanceof Blob||s)||!r&&!l(e))return e;if(t=r?[]:{},r||(e=>{const t=e.constructor&&e.constructor.prototype;return l(t)&&t.hasOwnProperty("isPrototypeOf")})(e))for(const r in e)e.hasOwnProperty(r)&&(t[r]=f(e[r]));else t=e}return t}var m=e=>Array.isArray(e)?e.filter(Boolean):[],y=e=>void 0===e,b=(e,t,r)=>{if(!t||!l(e))return r;const s=m(t.split(/[,[\].]+?/)).reduce(((e,t)=>n(e)?e:e[t]),e);return y(s)||s===e?y(e[t])?r:e[t]:s},g=e=>"boolean"==typeof e,_=e=>/^\w*$/.test(e),p=e=>m(e.replace(/["|']|\]/g,"").split(/\.|\[/)),h=(e,t,r)=>{let s=-1;const a=_(t)?[t]:p(t),i=a.length,n=i-1;for(;++s<i;){const t=a[s];let i=r;if(s!==n){const r=e[t];i=l(r)||Array.isArray(r)?r:isNaN(+a[s+1])?{}:[]}if("__proto__"===t||"constructor"===t||"prototype"===t)return;e[t]=i,e=e[t]}};const v="blur",V="focusout",F="change",A="onBlur",x="onChange",S="onSubmit",w="onTouched",k="all",D="max",E="min",C="maxLength",O="minLength",j="pattern",M="required",T="validate",N=t.createContext(null),R=()=>t.useContext(N);var B=(e,t,r,s=!0)=>{const a={defaultValues:t._defaultValues};for(const i in e)Object.defineProperty(a,i,{get:()=>{const a=i;return t._proxyFormState[a]!==k&&(t._proxyFormState[a]=!s||k),r&&(r[a]=!0),e[a]}});return a};const L="undefined"!=typeof window?s.useLayoutEffect:s.useEffect;function U(e){const r=R(),{control:s=r.control,disabled:a,name:i,exact:n}=e||{},[o,l]=t.useState(s._formState),u=t.useRef({isDirty:!1,isLoading:!1,dirtyFields:!1,touchedFields:!1,validatingFields:!1,isValidating:!1,isValid:!1,errors:!1});return L((()=>s._subscribe({name:i,formState:u.current,exact:n,callback:e=>{!a&&l({...s._formState,...e})}})),[i,a,n]),t.useEffect((()=>{u.current.isValid&&s._setValid(!0)}),[s]),t.useMemo((()=>B(o,s,u.current,!1)),[o,s])}var P=e=>"string"==typeof e,q=(e,t,r,s,a)=>P(e)?(s&&t.watch.add(e),b(r,e,a)):Array.isArray(e)?e.map((e=>(s&&t.watch.add(e),b(r,e)))):(s&&(t.watchAll=!0),r);function W(e){const r=R(),{control:s=r.control,name:a,defaultValue:i,disabled:n,exact:o}=e||{},l=t.useRef(i),[u,d]=t.useState(s._getWatch(a,l.current));return L((()=>s._subscribe({name:a,formState:{values:!0},exact:o,callback:e=>!n&&d(q(a,s._names,e.values||s._formValues,!1,l.current))})),[a,s,n,o]),t.useEffect((()=>s._removeUnmounted())),u}function I(e){const r=R(),{name:s,disabled:a,control:i=r.control,shouldUnregister:n}=e,o=d(i._names.array,s),l=W({control:i,name:s,defaultValue:b(i._formValues,s,b(i._defaultValues,s,e.defaultValue)),exact:!0}),c=U({control:i,name:s,exact:!0}),m=t.useRef(e),_=t.useRef(i.register(s,{...e.rules,value:l,...g(e.disabled)?{disabled:e.disabled}:{}})),p=t.useMemo((()=>Object.defineProperties({},{invalid:{enumerable:!0,get:()=>!!b(c.errors,s)},isDirty:{enumerable:!0,get:()=>!!b(c.dirtyFields,s)},isTouched:{enumerable:!0,get:()=>!!b(c.touchedFields,s)},isValidating:{enumerable:!0,get:()=>!!b(c.validatingFields,s)},error:{enumerable:!0,get:()=>b(c.errors,s)}})),[c,s]),V=t.useCallback((e=>_.current.onChange({target:{value:u(e),name:s},type:F})),[s]),A=t.useCallback((()=>_.current.onBlur({target:{value:b(i._formValues,s),name:s},type:v})),[s,i._formValues]),x=t.useCallback((e=>{const t=b(i._fields,s);t&&e&&(t._f.ref={focus:()=>e.focus&&e.focus(),select:()=>e.select&&e.select(),setCustomValidity:t=>e.setCustomValidity(t),reportValidity:()=>e.reportValidity()})}),[i._fields,s]),S=t.useMemo((()=>({name:s,value:l,...g(a)||c.disabled?{disabled:c.disabled||a}:{},onChange:V,onBlur:A,ref:x})),[s,a,c.disabled,V,A,x,l]);return t.useEffect((()=>{const e=i._options.shouldUnregister||n;i.register(s,{...m.current.rules,...g(m.current.disabled)?{disabled:m.current.disabled}:{}});const t=(e,t)=>{const r=b(i._fields,e);r&&r._f&&(r._f.mount=t)};if(t(s,!0),e){const e=f(b(i._options.defaultValues,s));h(i._defaultValues,s,e),y(b(i._formValues,s))&&h(i._formValues,s,e)}return!o&&i.register(s),()=>{(o?e&&!i._state.action:e)?i.unregister(s):t(s,!1)}}),[s,i,o,n]),t.useEffect((()=>{i._setDisabledField({disabled:a,name:s})}),[a,s,i]),t.useMemo((()=>({field:S,formState:c,fieldState:p})),[S,c,p])}const $=e=>{const t={};for(const r of Object.keys(e))if(o(e[r])&&null!==e[r]){const s=$(e[r]);for(const e of Object.keys(s))t[`${r}.${e}`]=s[e]}else t[r]=e[r];return t},H="post";var z=(e,t,r,s,a)=>t?{...r[e],types:{...r[e]&&r[e].types?r[e].types:{},[s]:a||!0}}:{},J=e=>Array.isArray(e)?e:[e],G=()=>{let e=[];return{get observers(){return e},next:t=>{for(const r of e)r.next&&r.next(t)},subscribe:t=>(e.push(t),{unsubscribe:()=>{e=e.filter((e=>e!==t))}}),unsubscribe:()=>{e=[]}}},K=e=>n(e)||!o(e);function Q(e,t){if(K(e)||K(t))return e===t;if(i(e)&&i(t))return e.getTime()===t.getTime();const r=Object.keys(e),s=Object.keys(t);if(r.length!==s.length)return!1;for(const a of r){const r=e[a];if(!s.includes(a))return!1;if("ref"!==a){const e=t[a];if(i(r)&&i(e)||l(r)&&l(e)||Array.isArray(r)&&Array.isArray(e)?!Q(r,e):r!==e)return!1}}return!0}var X=e=>l(e)&&!Object.keys(e).length,Y=e=>"file"===e.type,Z=e=>"function"==typeof e,ee=e=>{if(!c)return!1;const t=e?e.ownerDocument:0;return e instanceof(t&&t.defaultView?t.defaultView.HTMLElement:HTMLElement)},te=e=>"select-multiple"===e.type,re=e=>"radio"===e.type,se=e=>ee(e)&&e.isConnected;function ae(e,t){const r=Array.isArray(t)?t:_(t)?[t]:p(t),s=1===r.length?e:function(e,t){const r=t.slice(0,-1).length;let s=0;for(;s<r;)e=y(e)?s++:e[t[s++]];return e}(e,r),a=r.length-1,i=r[a];return s&&delete s[i],0!==a&&(l(s)&&X(s)||Array.isArray(s)&&function(e){for(const t in e)if(e.hasOwnProperty(t)&&!y(e[t]))return!1;return!0}(s))&&ae(e,r.slice(0,-1)),e}var ie=e=>{for(const t in e)if(Z(e[t]))return!0;return!1};function ne(e,t={}){const r=Array.isArray(e);if(l(e)||r)for(const r in e)Array.isArray(e[r])||l(e[r])&&!ie(e[r])?(t[r]=Array.isArray(e[r])?[]:{},ne(e[r],t[r])):n(e[r])||(t[r]=!0);return t}function oe(e,t,r){const s=Array.isArray(e);if(l(e)||s)for(const s in e)Array.isArray(e[s])||l(e[s])&&!ie(e[s])?y(t)||K(r[s])?r[s]=Array.isArray(e[s])?ne(e[s],[]):{...ne(e[s])}:oe(e[s],n(t)?{}:t[s],r[s]):r[s]=!Q(e[s],t[s]);return r}var le=(e,t)=>oe(e,t,ne(t));const ue={value:!1,isValid:!1},de={value:!0,isValid:!0};var ce=e=>{if(Array.isArray(e)){if(e.length>1){const t=e.filter((e=>e&&e.checked&&!e.disabled)).map((e=>e.value));return{value:t,isValid:!!t.length}}return e[0].checked&&!e[0].disabled?e[0].attributes&&!y(e[0].attributes.value)?y(e[0].value)||""===e[0].value?de:{value:e[0].value,isValid:!0}:de:ue}return ue},fe=(e,{valueAsNumber:t,valueAsDate:r,setValueAs:s})=>y(e)?e:t?""===e?NaN:e?+e:e:r&&P(e)?new Date(e):s?s(e):e;const me={isValid:!1,value:null};var ye=e=>Array.isArray(e)?e.reduce(((e,t)=>t&&t.checked&&!t.disabled?{isValid:!0,value:t.value}:e),me):me;function be(e){const t=e.ref;return Y(t)?t.files:re(t)?ye(e.refs).value:te(t)?[...t.selectedOptions].map((({value:e})=>e)):a(t)?ce(e.refs).value:fe(y(t.value)?e.ref.value:t.value,e)}var ge=e=>e instanceof RegExp,_e=e=>y(e)?e:ge(e)?e.source:l(e)?ge(e.value)?e.value.source:e.value:e,pe=e=>({isOnSubmit:!e||e===S,isOnBlur:e===A,isOnChange:e===x,isOnAll:e===k,isOnTouch:e===w});const he="AsyncFunction";var ve=(e,t,r)=>!r&&(t.watchAll||t.watch.has(e)||[...t.watch].some((t=>e.startsWith(t)&&/^\.\w+/.test(e.slice(t.length)))));const Ve=(e,t,r,s)=>{for(const a of r||Object.keys(e)){const r=b(e,a);if(r){const{_f:e,...i}=r;if(e){if(e.refs&&e.refs[0]&&t(e.refs[0],a)&&!s)return!0;if(e.ref&&t(e.ref,e.name)&&!s)return!0;if(Ve(i,t))break}else if(l(i)&&Ve(i,t))break}}};function Fe(e,t,r){const s=b(e,r);if(s||_(r))return{error:s,name:r};const a=r.split(".");for(;a.length;){const s=a.join("."),i=b(t,s),n=b(e,s);if(i&&!Array.isArray(i)&&r!==s)return{name:r};if(n&&n.type)return{name:s,error:n};if(n&&n.root&&n.root.type)return{name:`${s}.root`,error:n.root};a.pop()}return{name:r}}var Ae=(e,t,r)=>{const s=J(b(e,r));return h(s,"root",t[r]),h(e,r,s),e},xe=e=>P(e);function Se(e,t,r="validate"){if(xe(e)||Array.isArray(e)&&e.every(xe)||g(e)&&!e)return{type:r,message:xe(e)?e:"",ref:t}}var we=e=>l(e)&&!ge(e)?e:{value:e,message:""},ke=async(e,t,r,s,i,o)=>{const{ref:u,refs:d,required:c,maxLength:f,minLength:m,min:_,max:p,pattern:h,validate:v,name:V,valueAsNumber:F,mount:A}=e._f,x=b(r,V);if(!A||t.has(V))return{};const S=d?d[0]:u,w=e=>{i&&S.reportValidity&&(S.setCustomValidity(g(e)?"":e||""),S.reportValidity())},k={},N=re(u),R=a(u),B=N||R,L=(F||Y(u))&&y(u.value)&&y(x)||ee(u)&&""===u.value||""===x||Array.isArray(x)&&!x.length,U=z.bind(null,V,s,k),q=(e,t,r,s=C,a=O)=>{const i=e?t:r;k[V]={type:e?s:a,message:i,ref:u,...U(e?s:a,i)}};if(o?!Array.isArray(x)||!x.length:c&&(!B&&(L||n(x))||g(x)&&!x||R&&!ce(d).isValid||N&&!ye(d).isValid)){const{value:e,message:t}=xe(c)?{value:!!c,message:c}:we(c);if(e&&(k[V]={type:M,message:t,ref:S,...U(M,t)},!s))return w(t),k}if(!(L||n(_)&&n(p))){let e,t;const r=we(p),a=we(_);if(n(x)||isNaN(x)){const s=u.valueAsDate||new Date(x),i=e=>new Date((new Date).toDateString()+" "+e),n="time"==u.type,o="week"==u.type;P(r.value)&&x&&(e=n?i(x)>i(r.value):o?x>r.value:s>new Date(r.value)),P(a.value)&&x&&(t=n?i(x)<i(a.value):o?x<a.value:s<new Date(a.value))}else{const s=u.valueAsNumber||(x?+x:x);n(r.value)||(e=s>r.value),n(a.value)||(t=s<a.value)}if((e||t)&&(q(!!e,r.message,a.message,D,E),!s))return w(k[V].message),k}if((f||m)&&!L&&(P(x)||o&&Array.isArray(x))){const e=we(f),t=we(m),r=!n(e.value)&&x.length>+e.value,a=!n(t.value)&&x.length<+t.value;if((r||a)&&(q(r,e.message,t.message),!s))return w(k[V].message),k}if(h&&!L&&P(x)){const{value:e,message:t}=we(h);if(ge(e)&&!x.match(e)&&(k[V]={type:j,message:t,ref:u,...U(j,t)},!s))return w(t),k}if(v)if(Z(v)){const e=Se(await v(x,r),S);if(e&&(k[V]={...e,...U(T,e.message)},!s))return w(e.message),k}else if(l(v)){let e={};for(const t in v){if(!X(e)&&!s)break;const a=Se(await v[t](x,r),S,t);a&&(e={...a,...U(t,a.message)},w(a.message),s&&(k[V]=e))}if(!X(e)&&(k[V]={ref:S,...e},!s))return k}return w(!0),k};const De={mode:S,reValidateMode:x,shouldFocusError:!0};function Ee(e={}){let t={...De,...e},r={submitCount:0,isDirty:!1,isReady:!1,isLoading:Z(t.defaultValues),isValidating:!1,isSubmitted:!1,isSubmitting:!1,isSubmitSuccessful:!1,isValid:!1,touchedFields:{},dirtyFields:{},validatingFields:{},errors:t.errors||{},disabled:t.disabled||!1};const s={};let o,_=(l(t.defaultValues)||l(t.values))&&f(t.defaultValues||t.values)||{},p=t.shouldUnregister?{}:f(_),F={action:!1,mount:!1,watch:!1},A={mount:new Set,disabled:new Set,unMount:new Set,array:new Set,watch:new Set},x=0;const S={isDirty:!1,dirtyFields:!1,validatingFields:!1,touchedFields:!1,isValidating:!1,isValid:!1,errors:!1};let w={...S};const D={array:G(),state:G()},E=t.criteriaMode===k,C=async e=>{if(!t.disabled&&(S.isValid||w.isValid||e)){const e=t.resolver?X((await N()).errors):await R(s,!0);e!==r.isValid&&D.state.next({isValid:e})}},O=(e,s)=>{!t.disabled&&(S.isValidating||S.validatingFields||w.isValidating||w.validatingFields)&&((e||Array.from(A.mount)).forEach((e=>{e&&(s?h(r.validatingFields,e,s):ae(r.validatingFields,e))})),D.state.next({validatingFields:r.validatingFields,isValidating:!X(r.validatingFields)}))},j=(e,t,r,a)=>{const i=b(s,e);if(i){const s=b(p,e,y(r)?b(_,e):r);y(s)||a&&a.defaultChecked||t?h(p,e,t?s:be(i._f)):U(e,s),F.mount&&C()}},M=(e,s,a,i,n)=>{let o=!1,l=!1;const u={name:e};if(!t.disabled){if(!a||i){(S.isDirty||w.isDirty)&&(l=r.isDirty,r.isDirty=u.isDirty=B(),o=l!==u.isDirty);const t=Q(b(_,e),s);l=!!b(r.dirtyFields,e),t?ae(r.dirtyFields,e):h(r.dirtyFields,e,!0),u.dirtyFields=r.dirtyFields,o=o||(S.dirtyFields||w.dirtyFields)&&l!==!t}if(a){const t=b(r.touchedFields,e);t||(h(r.touchedFields,e,a),u.touchedFields=r.touchedFields,o=o||(S.touchedFields||w.touchedFields)&&t!==a)}o&&n&&D.state.next(u)}return o?u:{}},T=(e,s,a,i)=>{const n=b(r.errors,e),l=(S.isValid||w.isValid)&&g(s)&&r.isValid!==s;var u;if(t.delayError&&a?(u=()=>((e,t)=>{h(r.errors,e,t),D.state.next({errors:r.errors})})(e,a),o=e=>{clearTimeout(x),x=setTimeout(u,e)},o(t.delayError)):(clearTimeout(x),o=null,a?h(r.errors,e,a):ae(r.errors,e)),(a?!Q(n,a):n)||!X(i)||l){const t={...i,...l&&g(s)?{isValid:s}:{},errors:r.errors,name:e};r={...r,...t},D.state.next(t)}},N=async e=>{O(e,!0);const r=await t.resolver(p,t.context,((e,t,r,s)=>{const a={};for(const r of e){const e=b(t,r);e&&h(a,r,e._f)}return{criteriaMode:r,names:[...e],fields:a,shouldUseNativeValidation:s}})(e||A.mount,s,t.criteriaMode,t.shouldUseNativeValidation));return O(e),r},R=async(e,s,a={valid:!0})=>{for(const n in e){const o=e[n];if(o){const{_f:e,...u}=o;if(e){const u=A.array.has(e.name),d=o._f&&(!!(i=o._f)&&!!i.validate&&!!(Z(i.validate)&&i.validate.constructor.name===he||l(i.validate)&&Object.values(i.validate).find((e=>e.constructor.name===he))));d&&S.validatingFields&&O([n],!0);const c=await ke(o,A.disabled,p,E,t.shouldUseNativeValidation&&!s,u);if(d&&S.validatingFields&&O([n]),c[e.name]&&(a.valid=!1,s))break;!s&&(b(c,e.name)?u?Ae(r.errors,c,e.name):h(r.errors,e.name,c[e.name]):ae(r.errors,e.name))}!X(u)&&await R(u,s,a)}}var i;return a.valid},B=(e,r)=>!t.disabled&&(e&&r&&h(p,e,r),!Q(K(),_)),L=(e,t,r)=>q(e,A,{...F.mount?p:y(t)?_:P(e)?{[e]:t}:t},r,t),U=(e,t,r={})=>{const i=b(s,e);let o=t;if(i){const r=i._f;r&&(!r.disabled&&h(p,e,fe(t,r)),o=ee(r.ref)&&n(t)?"":t,te(r.ref)?[...r.ref.options].forEach((e=>e.selected=o.includes(e.value))):r.refs?a(r.ref)?r.refs.forEach((e=>{e.defaultChecked&&e.disabled||(Array.isArray(o)?e.checked=!!o.find((t=>t===e.value)):e.checked=o===e.value||!!o)})):r.refs.forEach((e=>e.checked=e.value===o)):Y(r.ref)?r.ref.value="":(r.ref.value=o,r.ref.type||D.state.next({name:e,values:f(p)})))}(r.shouldDirty||r.shouldTouch)&&M(e,o,r.shouldTouch,r.shouldDirty,!0),r.shouldValidate&&z(e)},W=(e,t,r)=>{for(const a in t){if(!t.hasOwnProperty(a))return;const n=t[a],o=e+"."+a,u=b(s,o);(A.array.has(e)||l(n)||u&&!u._f)&&!i(n)?W(o,n,r):U(o,n,r)}},I=(e,t,a={})=>{const i=b(s,e),o=A.array.has(e),l=f(t);h(p,e,l),o?(D.array.next({name:e,values:f(p)}),(S.isDirty||S.dirtyFields||w.isDirty||w.dirtyFields)&&a.shouldDirty&&D.state.next({name:e,dirtyFields:le(_,p),isDirty:B(e,l)})):!i||i._f||n(l)?U(e,l,a):W(e,l,a),ve(e,A)&&D.state.next({...r}),D.state.next({name:F.mount?e:void 0,values:f(p)})},$=async e=>{F.mount=!0;const a=e.target;let n=a.name,l=!0;const d=b(s,n),c=e=>{l=Number.isNaN(e)||i(e)&&isNaN(e.getTime())||Q(e,b(p,n,e))},m=pe(t.mode),y=pe(t.reValidateMode);if(d){let i,_;const F=a.type?be(d._f):u(e),x=e.type===v||e.type===V,k=!((g=d._f).mount&&(g.required||g.min||g.max||g.maxLength||g.minLength||g.pattern||g.validate)||t.resolver||b(r.errors,n)||d._f.deps)||((e,t,r,s,a)=>!a.isOnAll&&(!r&&a.isOnTouch?!(t||e):(r?s.isOnBlur:a.isOnBlur)?!e:!(r?s.isOnChange:a.isOnChange)||e))(x,b(r.touchedFields,n),r.isSubmitted,y,m),j=ve(n,A,x);h(p,n,F),x?(d._f.onBlur&&d._f.onBlur(e),o&&o(0)):d._f.onChange&&d._f.onChange(e);const B=M(n,F,x),L=!X(B)||j;if(!x&&D.state.next({name:n,type:e.type,values:f(p)}),k)return(S.isValid||w.isValid)&&("onBlur"===t.mode?x&&C():x||C()),L&&D.state.next({name:n,...j?{}:B});if(!x&&j&&D.state.next({...r}),t.resolver){const{errors:e}=await N([n]);if(c(F),l){const t=Fe(r.errors,s,n),a=Fe(e,s,t.name||n);i=a.error,n=a.name,_=X(e)}}else O([n],!0),i=(await ke(d,A.disabled,p,E,t.shouldUseNativeValidation))[n],O([n]),c(F),l&&(i?_=!1:(S.isValid||w.isValid)&&(_=await R(s,!0)));l&&(d._f.deps&&z(d._f.deps),T(n,_,i,B))}var g},H=(e,t)=>{if(b(r.errors,t)&&e.focus)return e.focus(),1},z=async(e,a={})=>{let i,n;const o=J(e);if(t.resolver){const t=await(async e=>{const{errors:t}=await N(e);if(e)for(const s of e){const e=b(t,s);e?h(r.errors,s,e):ae(r.errors,s)}else r.errors=t;return t})(y(e)?e:o);i=X(t),n=e?!o.some((e=>b(t,e))):i}else e?(n=(await Promise.all(o.map((async e=>{const t=b(s,e);return await R(t&&t._f?{[e]:t}:t)})))).every(Boolean),(n||r.isValid)&&C()):n=i=await R(s);return D.state.next({...!P(e)||(S.isValid||w.isValid)&&i!==r.isValid?{}:{name:e},...t.resolver||!e?{isValid:i}:{},errors:r.errors}),a.shouldFocus&&!n&&Ve(s,H,e?o:A.mount),n},K=e=>{const t={...F.mount?p:_};return y(e)?t:P(e)?b(t,e):e.map((e=>b(t,e)))},ie=(e,t)=>({invalid:!!b((t||r).errors,e),isDirty:!!b((t||r).dirtyFields,e),error:b((t||r).errors,e),isValidating:!!b(r.validatingFields,e),isTouched:!!b((t||r).touchedFields,e)}),ne=(e,t,a)=>{const i=(b(s,e,{_f:{}})._f||{}).ref,n=b(r.errors,e)||{},{ref:o,message:l,type:u,...d}=n;h(r.errors,e,{...d,...t,ref:i}),D.state.next({name:e,errors:r.errors,isValid:!1}),a&&a.shouldFocus&&i&&i.focus&&i.focus()},oe=e=>D.state.subscribe({next:t=>{var s,a,i;s=e.name,a=t.name,i=e.exact,s&&a&&s!==a&&!J(s).some((e=>e&&(i?e===a:e.startsWith(a)||a.startsWith(e))))||!((e,t,r,s)=>{r(e);const{name:a,...i}=e;return X(i)||Object.keys(i).length>=Object.keys(t).length||Object.keys(i).find((e=>t[e]===(!s||k)))})(t,e.formState||S,Se,e.reRenderRoot)||e.callback({values:{...p},...r,...t})}}).unsubscribe,ue=(e,a={})=>{for(const i of e?J(e):A.mount)A.mount.delete(i),A.array.delete(i),a.keepValue||(ae(s,i),ae(p,i)),!a.keepError&&ae(r.errors,i),!a.keepDirty&&ae(r.dirtyFields,i),!a.keepTouched&&ae(r.touchedFields,i),!a.keepIsValidating&&ae(r.validatingFields,i),!t.shouldUnregister&&!a.keepDefaultValue&&ae(_,i);D.state.next({values:f(p)}),D.state.next({...r,...a.keepDirty?{isDirty:B()}:{}}),!a.keepIsValid&&C()},de=({disabled:e,name:t})=>{(g(e)&&F.mount||e||A.disabled.has(t))&&(e?A.disabled.add(t):A.disabled.delete(t))},ce=(e,r={})=>{let i=b(s,e);const n=g(r.disabled)||g(t.disabled);return h(s,e,{...i||{},_f:{...i&&i._f?i._f:{ref:{name:e}},name:e,mount:!0,...r}}),A.mount.add(e),i?de({disabled:g(r.disabled)?r.disabled:t.disabled,name:e}):j(e,!0,r.value),{...n?{disabled:r.disabled||t.disabled}:{},...t.progressive?{required:!!r.required,min:_e(r.min),max:_e(r.max),minLength:_e(r.minLength),maxLength:_e(r.maxLength),pattern:_e(r.pattern)}:{},name:e,onChange:$,onBlur:$,ref:n=>{if(n){ce(e,r),i=b(s,e);const t=y(n.value)&&n.querySelectorAll&&n.querySelectorAll("input,select,textarea")[0]||n,o=(e=>re(e)||a(e))(t),l=i._f.refs||[];if(o?l.find((e=>e===t)):t===i._f.ref)return;h(s,e,{_f:{...i._f,...o?{refs:[...l.filter(se),t,...Array.isArray(b(_,e))?[{}]:[]],ref:{type:t.type,name:e}}:{ref:t}}}),j(e,!1,void 0,t)}else i=b(s,e,{}),i._f&&(i._f.mount=!1),(t.shouldUnregister||r.shouldUnregister)&&(!d(A.array,e)||!F.action)&&A.unMount.add(e)}}},me=()=>t.shouldFocusError&&Ve(s,H,A.mount),ye=(e,a)=>async i=>{let n;i&&(i.preventDefault&&i.preventDefault(),i.persist&&i.persist());let o=f(p);if(D.state.next({isSubmitting:!0}),t.resolver){const{errors:e,values:t}=await N();r.errors=e,o=t}else await R(s);if(A.disabled.size)for(const e of A.disabled)h(o,e,void 0);if(ae(r.errors,"root"),X(r.errors)){D.state.next({errors:{}});try{await e(o,i)}catch(e){n=e}}else a&&await a({...r.errors},i),me(),setTimeout(me);if(D.state.next({isSubmitted:!0,isSubmitting:!1,isSubmitSuccessful:X(r.errors)&&!n,submitCount:r.submitCount+1,errors:r.errors}),n)throw n},ge=(e,a={})=>{const i=e?f(e):_,n=f(i),o=X(e),l=o?_:n;if(a.keepDefaultValues||(_=i),!a.keepValues){if(a.keepDirtyValues){const e=new Set([...A.mount,...Object.keys(le(_,p))]);for(const t of Array.from(e))b(r.dirtyFields,t)?h(l,t,b(p,t)):I(t,b(l,t))}else{if(c&&y(e))for(const e of A.mount){const t=b(s,e);if(t&&t._f){const e=Array.isArray(t._f.refs)?t._f.refs[0]:t._f.ref;if(ee(e)){const t=e.closest("form");if(t){t.reset();break}}}}for(const e of A.mount)I(e,b(l,e))}p=f(l),D.array.next({values:{...l}}),D.state.next({values:{...l}})}A={mount:a.keepDirtyValues?A.mount:new Set,unMount:new Set,array:new Set,disabled:new Set,watch:new Set,watchAll:!1,focus:""},F.mount=!S.isValid||!!a.keepIsValid||!!a.keepDirtyValues,F.watch=!!t.shouldUnregister,D.state.next({submitCount:a.keepSubmitCount?r.submitCount:0,isDirty:!o&&(a.keepDirty?r.isDirty:!(!a.keepDefaultValues||Q(e,_))),isSubmitted:!!a.keepIsSubmitted&&r.isSubmitted,dirtyFields:o?{}:a.keepDirtyValues?a.keepDefaultValues&&p?le(_,p):r.dirtyFields:a.keepDefaultValues&&e?le(_,e):a.keepDirty?r.dirtyFields:{},touchedFields:a.keepTouched?r.touchedFields:{},errors:a.keepErrors?r.errors:{},isSubmitSuccessful:!!a.keepIsSubmitSuccessful&&r.isSubmitSuccessful,isSubmitting:!1})},xe=(e,t)=>ge(Z(e)?e(p):e,t),Se=e=>{r={...r,...e}},we={control:{register:ce,unregister:ue,getFieldState:ie,handleSubmit:ye,setError:ne,_subscribe:oe,_runSchema:N,_focusError:me,_getWatch:L,_getDirty:B,_setValid:C,_setFieldArray:(e,a=[],i,n,o=!0,l=!0)=>{if(n&&i&&!t.disabled){if(F.action=!0,l&&Array.isArray(b(s,e))){const t=i(b(s,e),n.argA,n.argB);o&&h(s,e,t)}if(l&&Array.isArray(b(r.errors,e))){const t=i(b(r.errors,e),n.argA,n.argB);o&&h(r.errors,e,t),((e,t)=>{!m(b(e,t)).length&&ae(e,t)})(r.errors,e)}if((S.touchedFields||w.touchedFields)&&l&&Array.isArray(b(r.touchedFields,e))){const t=i(b(r.touchedFields,e),n.argA,n.argB);o&&h(r.touchedFields,e,t)}(S.dirtyFields||w.dirtyFields)&&(r.dirtyFields=le(_,p)),D.state.next({name:e,isDirty:B(e,a),dirtyFields:r.dirtyFields,errors:r.errors,isValid:r.isValid})}else h(p,e,a)},_setDisabledField:de,_setErrors:e=>{r.errors=e,D.state.next({errors:r.errors,isValid:!1})},_getFieldArray:e=>m(b(F.mount?p:_,e,t.shouldUnregister?b(_,e,[]):[])),_reset:ge,_resetDefaultValues:()=>Z(t.defaultValues)&&t.defaultValues().then((e=>{xe(e,t.resetOptions),D.state.next({isLoading:!1})})),_removeUnmounted:()=>{for(const e of A.unMount){const t=b(s,e);t&&(t._f.refs?t._f.refs.every((e=>!se(e))):!se(t._f.ref))&&ue(e)}A.unMount=new Set},_disableForm:e=>{g(e)&&(D.state.next({disabled:e}),Ve(s,((t,r)=>{const a=b(s,r);a&&(t.disabled=a._f.disabled||e,Array.isArray(a._f.refs)&&a._f.refs.forEach((t=>{t.disabled=a._f.disabled||e})))}),0,!1))},_subjects:D,_proxyFormState:S,get _fields(){return s},get _formValues(){return p},get _state(){return F},set _state(e){F=e},get _defaultValues(){return _},get _names(){return A},set _names(e){A=e},get _formState(){return r},get _options(){return t},set _options(e){t={...t,...e}}},subscribe:e=>(F.mount=!0,w={...w,...e.formState},oe({...e,formState:w})),trigger:z,register:ce,handleSubmit:ye,watch:(e,t)=>Z(e)?D.state.subscribe({next:r=>e(L(void 0,t),r)}):L(e,t,!0),setValue:I,getValues:K,reset:xe,resetField:(e,t={})=>{b(s,e)&&(y(t.defaultValue)?I(e,f(b(_,e))):(I(e,t.defaultValue),h(_,e,f(t.defaultValue))),t.keepTouched||ae(r.touchedFields,e),t.keepDirty||(ae(r.dirtyFields,e),r.isDirty=t.defaultValue?B(e,f(b(_,e))):B()),t.keepError||(ae(r.errors,e),S.isValid&&C()),D.state.next({...r}))},clearErrors:e=>{e&&J(e).forEach((e=>ae(r.errors,e))),D.state.next({errors:e?r.errors:{}})},unregister:ue,setError:ne,setFocus:(e,t={})=>{const r=b(s,e),a=r&&r._f;if(a){const e=a.refs?a.refs[0]:a.ref;e.focus&&(e.focus(),t.shouldSelect&&Z(e.select)&&e.select())}},getFieldState:ie};return{...we,formControl:we}}var Ce=()=>{const e="undefined"==typeof performance?Date.now():1e3*performance.now();return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,(t=>{const r=(16*Math.random()+e)%16|0;return("x"==t?r:3&r|8).toString(16)}))},Oe=(e,t,r={})=>r.shouldFocus||y(r.shouldFocus)?r.focusName||`${e}.${y(r.focusIndex)?t:r.focusIndex}.`:"",je=(e,t)=>[...e,...J(t)],Me=e=>Array.isArray(e)?e.map((()=>{})):void 0;function Te(e,t,r){return[...e.slice(0,t),...J(r),...e.slice(t)]}var Ne=(e,t,r)=>Array.isArray(e)?(y(e[r])&&(e[r]=void 0),e.splice(r,0,e.splice(t,1)[0]),e):[],Re=(e,t)=>[...J(t),...J(e)];var Be=(e,t)=>y(t)?[]:function(e,t){let r=0;const s=[...e];for(const e of t)s.splice(e-r,1),r++;return m(s).length?s:[]}(e,J(t).sort(((e,t)=>e-t))),Le=(e,t,r)=>{[e[t],e[r]]=[e[r],e[t]]},Ue=(e,t,r)=>(e[t]=r,e);e.Controller=e=>e.render(I(e)),e.Form=function(e){const r=R(),[s,a]=t.useState(!1),{control:i=r.control,onSubmit:n,children:o,action:l,method:u=H,headers:d,encType:c,onError:f,render:m,onSuccess:y,validateStatus:b,...g}=e,_=async t=>{let r=!1,s="";await i.handleSubmit((async e=>{const a=new FormData;let o="";try{o=JSON.stringify(e)}catch(e){}const m=$(i._formValues);for(const e in m)a.append(e,m[e]);if(n&&await n({data:e,event:t,method:u,formData:a,formDataJson:o}),l)try{const e=[d&&d["Content-Type"],c].some((e=>e&&e.includes("json"))),t=await fetch(String(l),{method:u,headers:{...d,...c?{"Content-Type":c}:{}},body:e?o:a});t&&(b?!b(t.status):t.status<200||t.status>=300)?(r=!0,f&&f({response:t}),s=String(t.status)):y&&y({response:t})}catch(e){r=!0,f&&f({error:e})}}))(t),r&&e.control&&(e.control._subjects.state.next({isSubmitSuccessful:!1}),e.control.setError("root.server",{type:s}))};return t.useEffect((()=>{a(!0)}),[]),m?t.createElement(t.Fragment,null,m({submit:_})):t.createElement("form",{noValidate:s,action:l,method:u,encType:c,onSubmit:_,...g},o)},e.FormProvider=e=>{const{children:r,...s}=e;return t.createElement(N.Provider,{value:s},r)},e.appendErrors=z,e.createFormControl=Ee,e.get=b,e.set=h,e.useController=I,e.useFieldArray=function(e){const r=R(),{control:s=r.control,name:a,keyName:i="id",shouldUnregister:n,rules:o}=e,[l,u]=t.useState(s._getFieldArray(a)),d=t.useRef(s._getFieldArray(a).map(Ce)),c=t.useRef(l),m=t.useRef(a),y=t.useRef(!1);m.current=a,c.current=l,s._names.array.add(a),o&&s.register(a,o),t.useEffect((()=>s._subjects.array.subscribe({next:({values:e,name:t})=>{if(t===m.current||!t){const t=b(e,m.current);Array.isArray(t)&&(u(t),d.current=t.map(Ce))}}}).unsubscribe),[s]);const g=t.useCallback((e=>{y.current=!0,s._setFieldArray(a,e)}),[s,a]);return t.useEffect((()=>{if(s._state.action=!1,ve(a,s._names)&&s._subjects.state.next({...s._formState}),y.current&&(!pe(s._options.mode).isOnSubmit||s._formState.isSubmitted)&&!pe(s._options.reValidateMode).isOnSubmit)if(s._options.resolver)s._runSchema([a]).then((e=>{const t=b(e.errors,a),r=b(s._formState.errors,a);(r?!t&&r.type||t&&(r.type!==t.type||r.message!==t.message):t&&t.type)&&(t?h(s._formState.errors,a,t):ae(s._formState.errors,a),s._subjects.state.next({errors:s._formState.errors}))}));else{const e=b(s._fields,a);!e||!e._f||pe(s._options.reValidateMode).isOnSubmit&&pe(s._options.mode).isOnSubmit||ke(e,s._names.disabled,s._formValues,s._options.criteriaMode===k,s._options.shouldUseNativeValidation,!0).then((e=>!X(e)&&s._subjects.state.next({errors:Ae(s._formState.errors,e,a)})))}s._subjects.state.next({name:a,values:f(s._formValues)}),s._names.focus&&Ve(s._fields,((e,t)=>{if(s._names.focus&&t.startsWith(s._names.focus)&&e.focus)return e.focus(),1})),s._names.focus="",s._setValid(),y.current=!1}),[l,a,s]),t.useEffect((()=>(!b(s._formValues,a)&&s._setFieldArray(a),()=>{s._options.shouldUnregister||n?s.unregister(a):((e,t)=>{const r=b(s._fields,e);r&&r._f&&(r._f.mount=t)})(a,!1)})),[a,s,i,n]),{swap:t.useCallback(((e,t)=>{const r=s._getFieldArray(a);Le(r,e,t),Le(d.current,e,t),g(r),u(r),s._setFieldArray(a,r,Le,{argA:e,argB:t},!1)}),[g,a,s]),move:t.useCallback(((e,t)=>{const r=s._getFieldArray(a);Ne(r,e,t),Ne(d.current,e,t),g(r),u(r),s._setFieldArray(a,r,Ne,{argA:e,argB:t},!1)}),[g,a,s]),prepend:t.useCallback(((e,t)=>{const r=J(f(e)),i=Re(s._getFieldArray(a),r);s._names.focus=Oe(a,0,t),d.current=Re(d.current,r.map(Ce)),g(i),u(i),s._setFieldArray(a,i,Re,{argA:Me(e)})}),[g,a,s]),append:t.useCallback(((e,t)=>{const r=J(f(e)),i=je(s._getFieldArray(a),r);s._names.focus=Oe(a,i.length-1,t),d.current=je(d.current,r.map(Ce)),g(i),u(i),s._setFieldArray(a,i,je,{argA:Me(e)})}),[g,a,s]),remove:t.useCallback((e=>{const t=Be(s._getFieldArray(a),e);d.current=Be(d.current,e),g(t),u(t),!Array.isArray(b(s._fields,a))&&h(s._fields,a,void 0),s._setFieldArray(a,t,Be,{argA:e})}),[g,a,s]),insert:t.useCallback(((e,t,r)=>{const i=J(f(t)),n=Te(s._getFieldArray(a),e,i);s._names.focus=Oe(a,e,r),d.current=Te(d.current,e,i.map(Ce)),g(n),u(n),s._setFieldArray(a,n,Te,{argA:e,argB:Me(t)})}),[g,a,s]),update:t.useCallback(((e,t)=>{const r=f(t),i=Ue(s._getFieldArray(a),e,r);d.current=[...i].map(((t,r)=>t&&r!==e?d.current[r]:Ce())),g(i),u([...i]),s._setFieldArray(a,i,Ue,{argA:e,argB:r},!0,!1)}),[g,a,s]),replace:t.useCallback((e=>{const t=J(f(e));d.current=t.map(Ce),g([...t]),u([...t]),s._setFieldArray(a,[...t],(e=>e),{},!0,!1)}),[g,a,s]),fields:t.useMemo((()=>l.map(((e,t)=>({...e,[i]:d.current[t]||Ce()})))),[l,i])}},e.useForm=function(e={}){const r=t.useRef(void 0),s=t.useRef(void 0),[a,i]=t.useState({isDirty:!1,isValidating:!1,isLoading:Z(e.defaultValues),isSubmitted:!1,isSubmitting:!1,isSubmitSuccessful:!1,isValid:!1,submitCount:0,dirtyFields:{},touchedFields:{},validatingFields:{},errors:e.errors||{},disabled:e.disabled||!1,isReady:!1,defaultValues:Z(e.defaultValues)?void 0:e.defaultValues});r.current||(r.current={...e.formControl?e.formControl:Ee(e),formState:a},e.formControl&&e.defaultValues&&!Z(e.defaultValues)&&e.formControl.reset(e.defaultValues,e.resetOptions));const n=r.current.control;return n._options=e,L((()=>{const e=n._subscribe({formState:n._proxyFormState,callback:()=>i({...n._formState}),reRenderRoot:!0});return i((e=>({...e,isReady:!0}))),n._formState.isReady=!0,e}),[n]),t.useEffect((()=>n._disableForm(e.disabled)),[n,e.disabled]),t.useEffect((()=>{e.mode&&(n._options.mode=e.mode),e.reValidateMode&&(n._options.reValidateMode=e.reValidateMode)}),[n,e.mode,e.reValidateMode]),t.useEffect((()=>{e.errors&&(n._setErrors(e.errors),n._focusError())}),[n,e.errors]),t.useEffect((()=>{e.shouldUnregister&&n._subjects.state.next({values:n._getWatch()})}),[n,e.shouldUnregister]),t.useEffect((()=>{if(n._proxyFormState.isDirty){const e=n._getDirty();e!==a.isDirty&&n._subjects.state.next({isDirty:e})}}),[n,a.isDirty]),t.useEffect((()=>{e.values&&!Q(e.values,s.current)?(n._reset(e.values,n._options.resetOptions),s.current=e.values,i((e=>({...e})))):n._resetDefaultValues()}),[n,e.values]),t.useEffect((()=>{n._state.mount||(n._setValid(),n._state.mount=!0),n._state.watch&&(n._state.watch=!1,n._subjects.state.next({...n._formState})),n._removeUnmounted()})),r.current.formState=B(a,n),r.current},e.useFormContext=R,e.useFormState=U,e.useWatch=W}));
//# sourceMappingURL=index.umd.js.map
