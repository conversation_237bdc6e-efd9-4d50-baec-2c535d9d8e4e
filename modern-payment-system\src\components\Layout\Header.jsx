import React from 'react'
import { motion } from 'framer-motion'
import { Moon, Sun, CreditCard, User } from 'lucide-react'
import './Header.css'

const Header = ({ currentTheme, onThemeToggle }) => {

  return (
    <motion.header
      className="app-header"
      initial={{ opacity: 0, y: -20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <div className="header-content">
        {/* Left Section with Logo */}
        <div className="header-left">
          <div className="logo">
            <div className="logo-icon">
              <CreditCard size={20} />
            </div>
            <div className="logo-text">
              <h1>PayFlow</h1>
              <span>Payment System</span>
            </div>
          </div>
        </div>

        {/* Right Section with Theme and User */}
        <div className="header-right">
          {/* Theme Toggle */}
          <button
            className="theme-toggle"
            onClick={onThemeToggle}
            aria-label={`Switch to ${currentTheme === 'light' ? 'dark' : 'light'} theme`}
          >
            <motion.div
              key={currentTheme}
              initial={{ rotate: -180, opacity: 0 }}
              animate={{ rotate: 0, opacity: 1 }}
              transition={{ duration: 0.3 }}
            >
              {currentTheme === 'light' ? <Moon size={16} /> : <Sun size={16} />}
            </motion.div>
          </button>

          {/* User Account */}
          <div className="user-account">
            <button className="user-profile-btn">
              <User size={16} />
              <span>Account</span>
            </button>
          </div>
        </div>
      </div>
    </motion.header>
  )
}

export default Header
