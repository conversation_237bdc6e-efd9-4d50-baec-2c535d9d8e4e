import React from 'react'
import { motion } from 'framer-motion'
import { CreditCard, Shield, Zap } from 'lucide-react'
import './LoadingScreen.css'

const LoadingScreen = () => {
  const features = [
    { icon: <CreditCard size={20} />, text: 'Secure Payments' },
    { icon: <Shield size={20} />, text: 'Bank-level Security' },
    { icon: <Zap size={20} />, text: 'Lightning Fast' }
  ]

  return (
    <div className="loading-screen">
      <div className="loading-content">
        <motion.div
          className="loading-logo"
          initial={{ scale: 0, rotate: -180 }}
          animate={{ scale: 1, rotate: 0 }}
          transition={{ duration: 0.8, type: "spring", stiffness: 100 }}
        >
          <div className="logo-circle">
            <CreditCard size={32} />
          </div>
        </motion.div>

        <motion.h1
          className="loading-title"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.3 }}
        >
          PayFlow
        </motion.h1>

        <motion.p
          className="loading-subtitle"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.5 }}
        >
          Modern Payment Experience
        </motion.p>

        <motion.div
          className="loading-spinner"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.4, delay: 0.7 }}
        >
          <div className="spinner-ring"></div>
          <div className="spinner-ring"></div>
          <div className="spinner-ring"></div>
        </motion.div>

        <motion.div
          className="loading-features"
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.9 }}
        >
          {features.map((feature, index) => (
            <motion.div
              key={index}
              className="feature-item"
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.4, delay: 1.1 + index * 0.1 }}
            >
              <div className="feature-icon">
                {feature.icon}
              </div>
              <span>{feature.text}</span>
            </motion.div>
          ))}
        </motion.div>

        <motion.div
          className="loading-progress"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.4, delay: 1.5 }}
        >
          <div className="progress-bar">
            <motion.div
              className="progress-fill"
              initial={{ width: 0 }}
              animate={{ width: "100%" }}
              transition={{ duration: 1.5, delay: 0.5 }}
            />
          </div>
          <p className="progress-text">Initializing secure payment environment...</p>
        </motion.div>
      </div>
    </div>
  )
}

export default LoadingScreen
