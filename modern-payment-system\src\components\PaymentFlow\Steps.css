/* Common styles for all payment steps */

.step-header {
  text-align: center;
  margin-bottom: var(--spacing-8);
}

.step-title {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  margin-bottom: var(--spacing-3);
}

.step-description {
  font-size: var(--font-size-lg);
  color: var(--text-secondary);
  max-width: 600px;
  margin: 0 auto;
  line-height: var(--line-height-relaxed);
}

/* Delivery Options Step */
.delivery-options-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--spacing-6);
  margin-bottom: var(--spacing-8);
}

.delivery-option-card {
  background: var(--surface);
  border: 2px solid var(--border);
  border-radius: var(--radius-xl);
  padding: var(--spacing-6);
  text-align: center;
  cursor: pointer;
  transition: all var(--transition-normal);
}

.delivery-option-card:hover {
  border-color: var(--primary-blue);
  box-shadow: var(--shadow-lg);
}

.delivery-option-card.selected {
  border-color: var(--primary-blue);
  background: linear-gradient(135deg, rgba(37, 99, 235, 0.05), var(--surface));
}

.option-icon {
  width: 60px;
  height: 60px;
  background: var(--gray-100);
  border-radius: var(--radius-xl);
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto var(--spacing-4);
  color: var(--primary-blue);
}

.option-name {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  margin-bottom: var(--spacing-2);
}

.option-description {
  color: var(--text-secondary);
  margin-bottom: var(--spacing-4);
}

.option-details {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.option-price {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-bold);
  color: var(--primary-blue);
}

.option-time {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
}

.offers-section {
  background: var(--gray-50);
  border-radius: var(--radius-xl);
  padding: var(--spacing-6);
}

.offer-card {
  display: flex;
  align-items: center;
  gap: var(--spacing-4);
  background: var(--surface);
  border-radius: var(--radius-lg);
  padding: var(--spacing-4);
}

.apply-btn {
  background: var(--primary-blue);
  color: var(--white);
  border: none;
  padding: var(--spacing-2) var(--spacing-4);
  border-radius: var(--radius-lg);
  font-weight: var(--font-weight-semibold);
  cursor: pointer;
}

/* Product Config Step */
.products-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-4);
}

.product-card {
  background: var(--surface);
  border: 1px solid var(--border);
  border-radius: var(--radius-xl);
  padding: var(--spacing-6);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.product-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-4);
}

.payment-options {
  display: flex;
  gap: var(--spacing-2);
}

.option-btn {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  padding: var(--spacing-2) var(--spacing-4);
  border: 1px solid var(--border);
  border-radius: var(--radius-lg);
  background: var(--surface);
  cursor: pointer;
  transition: all var(--transition-fast);
}

.option-btn:hover {
  border-color: var(--primary-blue);
}

.option-btn.selected {
  background: var(--primary-blue);
  color: var(--white);
  border-color: var(--primary-blue);
}

/* Review Step */
.review-sections {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-6);
}

.review-section {
  background: var(--surface);
  border: 1px solid var(--border);
  border-radius: var(--radius-xl);
  padding: var(--spacing-6);
}

.review-section h3 {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  margin-bottom: var(--spacing-4);
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
}

.order-item {
  display: flex;
  justify-content: space-between;
  padding: var(--spacing-2) 0;
  border-bottom: 1px solid var(--border);
}

.order-item:last-child {
  border-bottom: none;
}

.price-breakdown {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2);
}

.price-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.price-item.total {
  border-top: 2px solid var(--border);
  padding-top: var(--spacing-3);
  margin-top: var(--spacing-2);
  font-weight: var(--font-weight-bold);
  font-size: var(--font-size-lg);
}

/* Success Step */
.success-step {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 400px;
}

.success-content {
  text-align: center;
  max-width: 500px;
}

.success-icon {
  color: var(--success-green);
  margin-bottom: var(--spacing-6);
}

.success-title {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  margin-bottom: var(--spacing-3);
}

.success-description {
  font-size: var(--font-size-lg);
  color: var(--text-secondary);
  margin-bottom: var(--spacing-8);
}

.success-details {
  background: var(--gray-50);
  border-radius: var(--radius-xl);
  padding: var(--spacing-6);
  margin-bottom: var(--spacing-8);
}

.detail-item {
  display: flex;
  justify-content: space-between;
  padding: var(--spacing-2) 0;
  border-bottom: 1px solid var(--border);
}

.detail-item:last-child {
  border-bottom: none;
}

.success-actions {
  display: flex;
  gap: var(--spacing-4);
  justify-content: center;
  flex-wrap: wrap;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  padding: var(--spacing-3) var(--spacing-6);
  border-radius: var(--radius-xl);
  font-weight: var(--font-weight-semibold);
  cursor: pointer;
  transition: all var(--transition-normal);
}

.action-btn.primary {
  background: var(--primary-blue);
  color: var(--white);
  border: none;
}

.action-btn.secondary {
  background: var(--surface);
  color: var(--text-primary);
  border: 1px solid var(--border);
}

.action-btn:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

/* Responsive design */
@media (max-width: 768px) {
  .delivery-options-grid {
    grid-template-columns: 1fr;
  }
  
  .product-card {
    flex-direction: column;
    gap: var(--spacing-4);
    text-align: center;
  }
  
  .success-actions {
    flex-direction: column;
    align-items: center;
  }
  
  .action-btn {
    width: 100%;
    max-width: 200px;
    justify-content: center;
  }
}
