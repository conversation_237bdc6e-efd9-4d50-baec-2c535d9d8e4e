<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Complete Modern Payment System</title>
    <script src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .payment-container {
            max-width: 900px;
            margin: 0 auto;
            background: #ffffff;
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .payment-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .payment-header h1 {
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 8px;
        }

        .payment-header p {
            opacity: 0.9;
            font-size: 16px;
        }

        .payment-content {
            padding: 40px;
        }

        .progress-bar {
            display: flex;
            justify-content: center;
            align-items: center;
            margin-bottom: 40px;
            background: #f8f9fa;
            padding: 20px;
            border-radius: 15px;
        }

        .progress-step {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 8px;
            font-size: 12px;
            font-weight: 500;
            color: #666;
        }

        .progress-step.active {
            color: #000;
            font-weight: 600;
        }

        .progress-step.completed {
            color: #28a745;
        }

        .step-circle {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            background: #e9ecef;
            color: #666;
            transition: all 0.3s ease;
        }

        .progress-step.active .step-circle {
            background: #667eea;
            color: white;
            transform: scale(1.1);
        }

        .progress-step.completed .step-circle {
            background: #28a745;
            color: white;
        }

        .progress-line {
            width: 80px;
            height: 3px;
            background: #28a745;
            margin: 0 15px;
            border-radius: 2px;
        }

        .progress-line.inactive {
            background: #e0e0e0;
        }

        .section {
            margin-bottom: 40px;
        }

        .section-title {
            font-size: 20px;
            font-weight: 600;
            color: #000;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .payment-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }

        .payment-card {
            border: 2px solid #e0e0e0;
            border-radius: 15px;
            padding: 25px;
            background: white;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .payment-card:hover {
            border-color: #667eea;
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(102, 126, 234, 0.15);
        }

        .payment-card.selected {
            border-color: #667eea;
            background: linear-gradient(135deg, #f8f9ff 0%, #ffffff 100%);
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.2);
        }

        .payment-card.recommended::before {
            content: '⭐ Recommended';
            position: absolute;
            top: 15px;
            right: 15px;
            background: #28a745;
            color: white;
            padding: 5px 10px;
            border-radius: 20px;
            font-size: 11px;
            font-weight: 600;
        }

        .card-header {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 20px;
        }

        .payment-icon {
            font-size: 32px;
            width: 60px;
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 12px;
        }

        .card-info h3 {
            font-size: 18px;
            font-weight: 600;
            color: #000;
            margin-bottom: 5px;
        }

        .card-info p {
            font-size: 13px;
            color: #666;
            line-height: 1.4;
        }

        .card-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 15px;
        }

        .detail-item {
            text-align: center;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 8px;
        }

        .detail-label {
            font-size: 11px;
            color: #666;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-bottom: 5px;
        }

        .detail-value {
            font-size: 13px;
            font-weight: 600;
            color: #000;
        }

        .popularity-indicator {
            background: #e9ecef;
            height: 6px;
            border-radius: 3px;
            overflow: hidden;
            margin-bottom: 10px;
        }

        .popularity-fill {
            height: 100%;
            background: linear-gradient(90deg, #28a745 0%, #20c997 100%);
            border-radius: 3px;
            transition: width 0.5s ease;
        }

        .popularity-text {
            font-size: 11px;
            color: #666;
            text-align: center;
        }

        .benefits {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-top: 15px;
        }

        .benefit-tag {
            background: #e8f5e8;
            color: #28a745;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 500;
        }

        .order-summary {
            background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
            border: 2px solid #e0e0e0;
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
        }

        .summary-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            font-size: 16px;
        }

        .summary-total {
            border-top: 2px solid #e0e0e0;
            padding-top: 20px;
            font-size: 20px;
            font-weight: 700;
        }

        .pay-button {
            width: 100%;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 15px;
            font-size: 18px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }

        .pay-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
        }

        .pay-button:disabled {
            opacity: 0.7;
            cursor: not-allowed;
            transform: none;
        }

        .security-info {
            text-align: center;
            margin-top: 20px;
            font-size: 13px;
            color: #666;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        @media (max-width: 768px) {
            .payment-content {
                padding: 20px;
            }
            
            .payment-grid {
                grid-template-columns: 1fr;
            }
            
            .card-details {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div id="root"></div>

    <script type="text/babel">
        const { useState } = React;

        const CompletePaymentSystem = () => {
            const [selectedMethod, setSelectedMethod] = useState('upi');
            const [isProcessing, setIsProcessing] = useState(false);

            const paymentMethods = [
                {
                    id: 'upi',
                    name: 'UPI',
                    icon: '📱',
                    description: 'Pay instantly using UPI apps like Google Pay, PhonePe',
                    processingTime: 'Instant',
                    fees: 'Free',
                    popularity: 95,
                    recommended: true,
                    benefits: ['Instant transfer', 'No fees', '24/7 available', 'Secure']
                },
                {
                    id: 'card',
                    name: 'Credit/Debit Card',
                    icon: '💳',
                    description: 'Visa, Mastercard, RuPay and American Express accepted',
                    processingTime: '2-3 mins',
                    fees: '2.5%',
                    popularity: 88,
                    recommended: false,
                    benefits: ['EMI available', 'Reward points', 'Worldwide accepted']
                },
                {
                    id: 'wallet',
                    name: 'Digital Wallet',
                    icon: '👛',
                    description: 'Pay from Paytm, PhonePe, Amazon Pay wallets',
                    processingTime: 'Instant',
                    fees: 'Free',
                    popularity: 72,
                    recommended: false,
                    benefits: ['Quick checkout', 'Cashback offers', 'No card needed']
                },
                {
                    id: 'netbanking',
                    name: 'Net Banking',
                    icon: '🏦',
                    description: 'Direct payment from your bank account',
                    processingTime: '3-5 mins',
                    fees: 'Free',
                    popularity: 65,
                    recommended: false,
                    benefits: ['Direct transfer', 'High security', 'All banks supported']
                },
                {
                    id: 'cod',
                    name: 'Cash on Delivery',
                    icon: '💵',
                    description: 'Pay when your order is delivered',
                    processingTime: 'On delivery',
                    fees: '₹40',
                    popularity: 45,
                    recommended: false,
                    benefits: ['No advance payment', 'Inspect first', 'Cash or card']
                },
                {
                    id: 'bnpl',
                    name: 'Buy Now, Pay Later',
                    icon: '📅',
                    description: 'Split your payment into easy installments',
                    processingTime: 'Instant',
                    fees: 'Varies',
                    popularity: 38,
                    recommended: false,
                    benefits: ['No interest EMI', 'Flexible tenure', 'Quick approval']
                },
                {
                    id: 'crypto',
                    name: 'Cryptocurrency',
                    icon: '₿',
                    description: 'Pay with Bitcoin, Ethereum and other cryptocurrencies',
                    processingTime: '10-30 mins',
                    fees: '1%',
                    popularity: 12,
                    recommended: false,
                    benefits: ['Decentralized', 'Global payments', 'Future of money']
                },
                {
                    id: 'gift-card',
                    name: 'Gift Card',
                    icon: '🎁',
                    description: 'Redeem your gift cards and store credits',
                    processingTime: 'Instant',
                    fees: 'Free',
                    popularity: 25,
                    recommended: false,
                    benefits: ['Use existing balance', 'No expiry', 'Instant redemption']
                }
            ];

            const handlePayment = () => {
                setIsProcessing(true);
                setTimeout(() => {
                    setIsProcessing(false);
                    alert('🎉 Payment successful! Your order has been confirmed.');
                }, 3000);
            };

            return (
                <div className="payment-container">
                    <div className="payment-header">
                        <h1>Secure Payment</h1>
                        <p>Choose your preferred payment method and complete your order</p>
                    </div>

                    <div className="payment-content">
                        {/* Progress Bar */}
                        <div className="progress-bar">
                            <div className="progress-step completed">
                                <div className="step-circle">✓</div>
                                <span>Cart</span>
                            </div>
                            <div className="progress-line"></div>
                            <div className="progress-step completed">
                                <div className="step-circle">✓</div>
                                <span>Shipping</span>
                            </div>
                            <div className="progress-line"></div>
                            <div className="progress-step active">
                                <div className="step-circle">3</div>
                                <span>Payment</span>
                            </div>
                            <div className="progress-line inactive"></div>
                            <div className="progress-step">
                                <div className="step-circle">4</div>
                                <span>Confirm</span>
                            </div>
                        </div>

                        {/* Payment Methods */}
                        <div className="section">
                            <h2 className="section-title">
                                <span>💳</span>
                                Choose Payment Method
                            </h2>
                            
                            <div className="payment-grid">
                                {paymentMethods.map((method) => (
                                    <div
                                        key={method.id}
                                        className={`payment-card ${selectedMethod === method.id ? 'selected' : ''} ${method.recommended ? 'recommended' : ''}`}
                                        onClick={() => setSelectedMethod(method.id)}
                                    >
                                        <div className="card-header">
                                            <div className="payment-icon">{method.icon}</div>
                                            <div className="card-info">
                                                <h3>{method.name}</h3>
                                                <p>{method.description}</p>
                                            </div>
                                        </div>

                                        <div className="card-details">
                                            <div className="detail-item">
                                                <div className="detail-label">Processing</div>
                                                <div className="detail-value">{method.processingTime}</div>
                                            </div>
                                            <div className="detail-item">
                                                <div className="detail-label">Fees</div>
                                                <div className="detail-value">{method.fees}</div>
                                            </div>
                                        </div>

                                        <div className="popularity-indicator">
                                            <div 
                                                className="popularity-fill" 
                                                style={{width: `${method.popularity}%`}}
                                            ></div>
                                        </div>
                                        <div className="popularity-text">
                                            {method.popularity}% users prefer this method
                                        </div>

                                        <div className="benefits">
                                            {method.benefits.slice(0, 3).map((benefit, index) => (
                                                <span key={index} className="benefit-tag">
                                                    ✓ {benefit}
                                                </span>
                                            ))}
                                        </div>
                                    </div>
                                ))}
                            </div>
                        </div>

                        {/* Order Summary */}
                        <div className="section">
                            <div className="order-summary">
                                <h3 className="section-title">
                                    <span>📋</span>
                                    Order Summary
                                </h3>
                                
                                <div className="summary-row">
                                    <span>Subtotal</span>
                                    <span>€39.99</span>
                                </div>
                                <div className="summary-row">
                                    <span>Shipping</span>
                                    <span>€2.99</span>
                                </div>
                                <div className="summary-row">
                                    <span>Tax</span>
                                    <span>€3.20</span>
                                </div>
                                <div className="summary-row">
                                    <span style={{color: '#28a745'}}>Discount</span>
                                    <span style={{color: '#28a745'}}>-€3.19</span>
                                </div>
                                
                                <div className="summary-row summary-total">
                                    <span>Total</span>
                                    <span>€42.99</span>
                                </div>
                            </div>
                        </div>

                        {/* Payment Button */}
                        <button 
                            className="pay-button"
                            onClick={handlePayment}
                            disabled={isProcessing || !selectedMethod}
                        >
                            {isProcessing ? (
                                <>
                                    <span>⏳</span>
                                    Processing Payment...
                                </>
                            ) : (
                                <>
                                    <span>🔒</span>
                                    Pay €42.99 Securely
                                </>
                            )}
                        </button>

                        <div className="security-info">
                            <span>🛡️</span>
                            <span>Your payment is secured with 256-bit SSL encryption</span>
                        </div>
                    </div>
                </div>
            );
        };

        ReactDOM.render(<CompletePaymentSystem />, document.getElementById('root'));
    </script>
</body>
</html>
