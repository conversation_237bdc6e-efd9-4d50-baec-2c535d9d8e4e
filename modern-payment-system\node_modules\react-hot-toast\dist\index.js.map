{"version": 3, "sources": ["../src/index.ts", "../src/core/types.ts", "../src/core/utils.ts", "../src/core/store.ts", "../src/core/toast.ts", "../src/core/use-toaster.ts", "../src/components/toast-bar.tsx", "../src/components/toast-icon.tsx", "../src/components/error.tsx", "../src/components/loader.tsx", "../src/components/checkmark.tsx", "../src/components/toaster.tsx"], "sourcesContent": ["import { toast } from './core/toast';\n\nexport * from './headless';\n\nexport { ToastBar } from './components/toast-bar';\nexport { ToastIcon } from './components/toast-icon';\nexport { Toaster } from './components/toaster';\nexport { CheckmarkIcon } from './components/checkmark';\nexport { ErrorIcon } from './components/error';\nexport { LoaderIcon } from './components/loader';\n\nexport { toast };\nexport default toast;\n", "import { CSSProperties } from 'react';\n\nexport type ToastType = 'success' | 'error' | 'loading' | 'blank' | 'custom';\nexport type ToastPosition =\n  | 'top-left'\n  | 'top-center'\n  | 'top-right'\n  | 'bottom-left'\n  | 'bottom-center'\n  | 'bottom-right';\n\nexport type Renderable = React.ReactElement | string | null;\n\nexport interface IconTheme {\n  primary: string;\n  secondary: string;\n}\n\nexport type ValueFunction<TValue, TArg> = (arg: TArg) => TValue;\nexport type ValueOrFunction<TValue, TArg> =\n  | TValue\n  | ValueFunction<TValue, TArg>;\n\nconst isFunction = <TValue, TArg>(\n  valOrFunction: ValueOrFunction<TValue, TArg>\n): valOrFunction is ValueFunction<TValue, TArg> =>\n  typeof valOrFunction === 'function';\n\nexport const resolveValue = <TValue, TArg>(\n  valOrFunction: ValueOrFunction<TValue, TArg>,\n  arg: TArg\n): TValue => (isFunction(valOrFunction) ? valOrFunction(arg) : valOrFunction);\n\nexport interface Toast {\n  type: ToastType;\n  id: string;\n  message: ValueOrFunction<Renderable, Toast>;\n  icon?: Renderable;\n  duration?: number;\n  pauseDuration: number;\n  position?: ToastPosition;\n  removeDelay?: number;\n\n  ariaProps: {\n    role: 'status' | 'alert';\n    'aria-live': 'assertive' | 'off' | 'polite';\n  };\n\n  style?: CSSProperties;\n  className?: string;\n  iconTheme?: IconTheme;\n\n  createdAt: number;\n  visible: boolean;\n  dismissed: boolean;\n  height?: number;\n}\n\nexport type ToastOptions = Partial<\n  Pick<\n    Toast,\n    | 'id'\n    | 'icon'\n    | 'duration'\n    | 'ariaProps'\n    | 'className'\n    | 'style'\n    | 'position'\n    | 'iconTheme'\n    | 'removeDelay'\n  >\n>;\n\nexport type DefaultToastOptions = ToastOptions & {\n  [key in ToastType]?: ToastOptions;\n};\n\nexport interface ToasterProps {\n  position?: ToastPosition;\n  toastOptions?: DefaultToastOptions;\n  reverseOrder?: boolean;\n  gutter?: number;\n  containerStyle?: React.CSSProperties;\n  containerClassName?: string;\n  children?: (toast: Toast) => React.ReactElement;\n}\n\nexport interface ToastWrapperProps {\n  id: string;\n  className?: string;\n  style?: React.CSSProperties;\n  onHeightUpdate: (id: string, height: number) => void;\n  children?: React.ReactNode;\n}\n", "export const genId = (() => {\n  let count = 0;\n  return () => {\n    return (++count).toString();\n  };\n})();\n\nexport const prefersReducedMotion = (() => {\n  // Cache result\n  let shouldReduceMotion: boolean | undefined = undefined;\n\n  return () => {\n    if (shouldReduceMotion === undefined && typeof window !== 'undefined') {\n      const mediaQuery = matchMedia('(prefers-reduced-motion: reduce)');\n      shouldReduceMotion = !mediaQuery || mediaQuery.matches;\n    }\n    return shouldReduceMotion;\n  };\n})();\n", "import { useEffect, useState, useRef } from 'react';\nimport { DefaultToastOptions, Toast, ToastType } from './types';\n\nconst TOAST_LIMIT = 20;\n\nexport enum ActionType {\n  ADD_TOAST,\n  UPDATE_TOAST,\n  UPSERT_TOAST,\n  DISMISS_TOAST,\n  REMOVE_TOAST,\n  START_PAUSE,\n  END_PAUSE,\n}\n\ntype Action =\n  | {\n      type: ActionType.ADD_TOAST;\n      toast: Toast;\n    }\n  | {\n      type: ActionType.UPSERT_TOAST;\n      toast: Toast;\n    }\n  | {\n      type: ActionType.UPDATE_TOAST;\n      toast: Partial<Toast>;\n    }\n  | {\n      type: ActionType.DISMISS_TOAST;\n      toastId?: string;\n    }\n  | {\n      type: ActionType.REMOVE_TOAST;\n      toastId?: string;\n    }\n  | {\n      type: ActionType.START_PAUSE;\n      time: number;\n    }\n  | {\n      type: ActionType.END_PAUSE;\n      time: number;\n    };\n\ninterface State {\n  toasts: Toast[];\n  pausedAt: number | undefined;\n}\n\nexport const reducer = (state: State, action: Action): State => {\n  switch (action.type) {\n    case ActionType.ADD_TOAST:\n      return {\n        ...state,\n        toasts: [action.toast, ...state.toasts].slice(0, TOAST_LIMIT),\n      };\n\n    case ActionType.UPDATE_TOAST:\n      return {\n        ...state,\n        toasts: state.toasts.map((t) =>\n          t.id === action.toast.id ? { ...t, ...action.toast } : t\n        ),\n      };\n\n    case ActionType.UPSERT_TOAST:\n      const { toast } = action;\n      return reducer(state, {\n        type: state.toasts.find((t) => t.id === toast.id)\n          ? ActionType.UPDATE_TOAST\n          : ActionType.ADD_TOAST,\n        toast,\n      });\n\n    case ActionType.DISMISS_TOAST:\n      const { toastId } = action;\n\n      return {\n        ...state,\n        toasts: state.toasts.map((t) =>\n          t.id === toastId || toastId === undefined\n            ? {\n                ...t,\n                dismissed: true,\n                visible: false,\n              }\n            : t\n        ),\n      };\n    case ActionType.REMOVE_TOAST:\n      if (action.toastId === undefined) {\n        return {\n          ...state,\n          toasts: [],\n        };\n      }\n      return {\n        ...state,\n        toasts: state.toasts.filter((t) => t.id !== action.toastId),\n      };\n\n    case ActionType.START_PAUSE:\n      return {\n        ...state,\n        pausedAt: action.time,\n      };\n\n    case ActionType.END_PAUSE:\n      const diff = action.time - (state.pausedAt || 0);\n\n      return {\n        ...state,\n        pausedAt: undefined,\n        toasts: state.toasts.map((t) => ({\n          ...t,\n          pauseDuration: t.pauseDuration + diff,\n        })),\n      };\n  }\n};\n\nconst listeners: Array<(state: State) => void> = [];\n\nlet memoryState: State = { toasts: [], pausedAt: undefined };\n\nexport const dispatch = (action: Action) => {\n  memoryState = reducer(memoryState, action);\n  listeners.forEach((listener) => {\n    listener(memoryState);\n  });\n};\n\nexport const defaultTimeouts: {\n  [key in ToastType]: number;\n} = {\n  blank: 4000,\n  error: 4000,\n  success: 2000,\n  loading: Infinity,\n  custom: 4000,\n};\n\nexport const useStore = (toastOptions: DefaultToastOptions = {}): State => {\n  const [state, setState] = useState<State>(memoryState);\n  const initial = useRef(memoryState);\n\n  // TODO: Switch to useSyncExternalStore when targeting React 18+\n  useEffect(() => {\n    if (initial.current !== memoryState) {\n      setState(memoryState);\n    }\n    listeners.push(setState);\n    return () => {\n      const index = listeners.indexOf(setState);\n      if (index > -1) {\n        listeners.splice(index, 1);\n      }\n    };\n  }, []);\n\n  const mergedToasts = state.toasts.map((t) => ({\n    ...toastOptions,\n    ...toastOptions[t.type],\n    ...t,\n    removeDelay:\n      t.removeDelay ||\n      toastOptions[t.type]?.removeDelay ||\n      toastOptions?.removeDelay,\n    duration:\n      t.duration ||\n      toastOptions[t.type]?.duration ||\n      toastOptions?.duration ||\n      defaultTimeouts[t.type],\n    style: {\n      ...toastOptions.style,\n      ...toastOptions[t.type]?.style,\n      ...t.style,\n    },\n  }));\n\n  return {\n    ...state,\n    toasts: mergedToasts,\n  };\n};\n", "import {\n  Renderable,\n  Toast,\n  ToastOptions,\n  ToastType,\n  DefaultToastOptions,\n  ValueOrFunction,\n  resolveValue,\n} from './types';\nimport { genId } from './utils';\nimport { dispatch, ActionType } from './store';\n\ntype Message = ValueOrFunction<Renderable, Toast>;\n\ntype ToastHandler = (message: Message, options?: ToastOptions) => string;\n\nconst createToast = (\n  message: Message,\n  type: ToastType = 'blank',\n  opts?: ToastOptions\n): Toast => ({\n  createdAt: Date.now(),\n  visible: true,\n  dismissed: false,\n  type,\n  ariaProps: {\n    role: 'status',\n    'aria-live': 'polite',\n  },\n  message,\n  pauseDuration: 0,\n  ...opts,\n  id: opts?.id || genId(),\n});\n\nconst createHandler =\n  (type?: ToastType): ToastHandler =>\n  (message, options) => {\n    const toast = createToast(message, type, options);\n    dispatch({ type: ActionType.UPSERT_TOAST, toast });\n    return toast.id;\n  };\n\nconst toast = (message: Message, opts?: ToastOptions) =>\n  createHandler('blank')(message, opts);\n\ntoast.error = createHandler('error');\ntoast.success = createHandler('success');\ntoast.loading = createHandler('loading');\ntoast.custom = createHandler('custom');\n\ntoast.dismiss = (toastId?: string) => {\n  dispatch({\n    type: ActionType.DISMISS_TOAST,\n    toastId,\n  });\n};\n\ntoast.remove = (toastId?: string) =>\n  dispatch({ type: ActionType.REMOVE_TOAST, toastId });\n\ntoast.promise = <T>(\n  promise: Promise<T> | (() => Promise<T>),\n  msgs: {\n    loading: Renderable;\n    success?: ValueOrFunction<Renderable, T>;\n    error?: ValueOrFunction<Renderable, any>;\n  },\n  opts?: DefaultToastOptions\n) => {\n  const id = toast.loading(msgs.loading, { ...opts, ...opts?.loading });\n\n  if (typeof promise === 'function') {\n    promise = promise();\n  }\n\n  promise\n    .then((p) => {\n      const successMessage = msgs.success\n        ? resolveValue(msgs.success, p)\n        : undefined;\n\n      if (successMessage) {\n        toast.success(successMessage, {\n          id,\n          ...opts,\n          ...opts?.success,\n        });\n      } else {\n        toast.dismiss(id);\n      }\n      return p;\n    })\n    .catch((e) => {\n      const errorMessage = msgs.error ? resolveValue(msgs.error, e) : undefined;\n\n      if (errorMessage) {\n        toast.error(errorMessage, {\n          id,\n          ...opts,\n          ...opts?.error,\n        });\n      } else {\n        toast.dismiss(id);\n      }\n    });\n\n  return promise;\n};\n\nexport { toast };\n", "import { useEffect, useCallback } from 'react';\nimport { dispatch, ActionType, useStore } from './store';\nimport { toast } from './toast';\nimport { DefaultToastOptions, Toast, ToastPosition } from './types';\n\nconst updateHeight = (toastId: string, height: number) => {\n  dispatch({\n    type: ActionType.UPDATE_TOAST,\n    toast: { id: toastId, height },\n  });\n};\nconst startPause = () => {\n  dispatch({\n    type: ActionType.START_PAUSE,\n    time: Date.now(),\n  });\n};\n\nconst toastTimeouts = new Map<Toast['id'], ReturnType<typeof setTimeout>>();\n\nexport const REMOVE_DELAY = 1000;\n\nconst addToRemoveQueue = (toastId: string, removeDelay = REMOVE_DELAY) => {\n  if (toastTimeouts.has(toastId)) {\n    return;\n  }\n\n  const timeout = setTimeout(() => {\n    toastTimeouts.delete(toastId);\n    dispatch({\n      type: ActionType.REMOVE_TOAST,\n      toastId: toastId,\n    });\n  }, removeDelay);\n\n  toastTimeouts.set(toastId, timeout);\n};\n\nexport const useToaster = (toastOptions?: DefaultToastOptions) => {\n  const { toasts, pausedAt } = useStore(toastOptions);\n\n  useEffect(() => {\n    if (pausedAt) {\n      return;\n    }\n\n    const now = Date.now();\n    const timeouts = toasts.map((t) => {\n      if (t.duration === Infinity) {\n        return;\n      }\n\n      const durationLeft =\n        (t.duration || 0) + t.pauseDuration - (now - t.createdAt);\n\n      if (durationLeft < 0) {\n        if (t.visible) {\n          toast.dismiss(t.id);\n        }\n        return;\n      }\n      return setTimeout(() => toast.dismiss(t.id), durationLeft);\n    });\n\n    return () => {\n      timeouts.forEach((timeout) => timeout && clearTimeout(timeout));\n    };\n  }, [toasts, pausedAt]);\n\n  const endPause = useCallback(() => {\n    if (pausedAt) {\n      dispatch({ type: ActionType.END_PAUSE, time: Date.now() });\n    }\n  }, [pausedAt]);\n\n  const calculateOffset = useCallback(\n    (\n      toast: Toast,\n      opts?: {\n        reverseOrder?: boolean;\n        gutter?: number;\n        defaultPosition?: ToastPosition;\n      }\n    ) => {\n      const { reverseOrder = false, gutter = 8, defaultPosition } = opts || {};\n\n      const relevantToasts = toasts.filter(\n        (t) =>\n          (t.position || defaultPosition) ===\n            (toast.position || defaultPosition) && t.height\n      );\n      const toastIndex = relevantToasts.findIndex((t) => t.id === toast.id);\n      const toastsBefore = relevantToasts.filter(\n        (toast, i) => i < toastIndex && toast.visible\n      ).length;\n\n      const offset = relevantToasts\n        .filter((t) => t.visible)\n        .slice(...(reverseOrder ? [toastsBefore + 1] : [0, toastsBefore]))\n        .reduce((acc, t) => acc + (t.height || 0) + gutter, 0);\n\n      return offset;\n    },\n    [toasts]\n  );\n\n  useEffect(() => {\n    // Add dismissed toasts to remove queue\n    toasts.forEach((toast) => {\n      if (toast.dismissed) {\n        addToRemoveQueue(toast.id, toast.removeDelay);\n      } else {\n        // If toast becomes visible again, remove it from the queue\n        const timeout = toastTimeouts.get(toast.id);\n        if (timeout) {\n          clearTimeout(timeout);\n          toastTimeouts.delete(toast.id);\n        }\n      }\n    });\n  }, [toasts]);\n\n  return {\n    toasts,\n    handlers: {\n      updateHeight,\n      startPause,\n      endPause,\n      calculateOffset,\n    },\n  };\n};\n", "import * as React from 'react';\nimport { styled, keyframes } from 'goober';\n\nimport { Toast, ToastPosition, resolveValue, Renderable } from '../core/types';\nimport { ToastIcon } from './toast-icon';\nimport { prefersReducedMotion } from '../core/utils';\n\nconst enterAnimation = (factor: number) => `\n0% {transform: translate3d(0,${factor * -200}%,0) scale(.6); opacity:.5;}\n100% {transform: translate3d(0,0,0) scale(1); opacity:1;}\n`;\n\nconst exitAnimation = (factor: number) => `\n0% {transform: translate3d(0,0,-1px) scale(1); opacity:1;}\n100% {transform: translate3d(0,${factor * -150}%,-1px) scale(.6); opacity:0;}\n`;\n\nconst fadeInAnimation = `0%{opacity:0;} 100%{opacity:1;}`;\nconst fadeOutAnimation = `0%{opacity:1;} 100%{opacity:0;}`;\n\nconst ToastBarBase = styled('div')`\n  display: flex;\n  align-items: center;\n  background: #fff;\n  color: #363636;\n  line-height: 1.3;\n  will-change: transform;\n  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1), 0 3px 3px rgba(0, 0, 0, 0.05);\n  max-width: 350px;\n  pointer-events: auto;\n  padding: 8px 10px;\n  border-radius: 8px;\n`;\n\nconst Message = styled('div')`\n  display: flex;\n  justify-content: center;\n  margin: 4px 10px;\n  color: inherit;\n  flex: 1 1 auto;\n  white-space: pre-line;\n`;\n\ninterface ToastBarProps {\n  toast: Toast;\n  position?: ToastPosition;\n  style?: React.CSSProperties;\n  children?: (components: {\n    icon: Renderable;\n    message: Renderable;\n  }) => Renderable;\n}\n\nconst getAnimationStyle = (\n  position: ToastPosition,\n  visible: boolean\n): React.CSSProperties => {\n  const top = position.includes('top');\n  const factor = top ? 1 : -1;\n\n  const [enter, exit] = prefersReducedMotion()\n    ? [fadeInAnimation, fadeOutAnimation]\n    : [enterAnimation(factor), exitAnimation(factor)];\n\n  return {\n    animation: visible\n      ? `${keyframes(enter)} 0.35s cubic-bezier(.21,1.02,.73,1) forwards`\n      : `${keyframes(exit)} 0.4s forwards cubic-bezier(.06,.71,.55,1)`,\n  };\n};\n\nexport const ToastBar: React.FC<ToastBarProps> = React.memo(\n  ({ toast, position, style, children }) => {\n    const animationStyle: React.CSSProperties = toast.height\n      ? getAnimationStyle(\n          toast.position || position || 'top-center',\n          toast.visible\n        )\n      : { opacity: 0 };\n\n    const icon = <ToastIcon toast={toast} />;\n    const message = (\n      <Message {...toast.ariaProps}>\n        {resolveValue(toast.message, toast)}\n      </Message>\n    );\n\n    return (\n      <ToastBarBase\n        className={toast.className}\n        style={{\n          ...animationStyle,\n          ...style,\n          ...toast.style,\n        }}\n      >\n        {typeof children === 'function' ? (\n          children({\n            icon,\n            message,\n          })\n        ) : (\n          <>\n            {icon}\n            {message}\n          </>\n        )}\n      </ToastBarBase>\n    );\n  }\n);\n", "import * as React from 'react';\nimport { styled, keyframes } from 'goober';\n\nimport { Toast } from '../core/types';\nimport { ErrorIcon, ErrorTheme } from './error';\nimport { LoaderIcon, LoaderTheme } from './loader';\nimport { CheckmarkIcon, CheckmarkTheme } from './checkmark';\n\nconst StatusWrapper = styled('div')`\n  position: absolute;\n`;\n\nconst IndicatorWrapper = styled('div')`\n  position: relative;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  min-width: 20px;\n  min-height: 20px;\n`;\n\nconst enter = keyframes`\nfrom {\n  transform: scale(0.6);\n  opacity: 0.4;\n}\nto {\n  transform: scale(1);\n  opacity: 1;\n}`;\n\nexport const AnimatedIconWrapper = styled('div')`\n  position: relative;\n  transform: scale(0.6);\n  opacity: 0.4;\n  min-width: 20px;\n  animation: ${enter} 0.3s 0.12s cubic-bezier(0.175, 0.885, 0.32, 1.275)\n    forwards;\n`;\n\nexport type IconThemes = Partial<{\n  success: CheckmarkTheme;\n  error: ErrorTheme;\n  loading: LoaderTheme;\n}>;\n\nexport const ToastIcon: React.FC<{\n  toast: Toast;\n}> = ({ toast }) => {\n  const { icon, type, iconTheme } = toast;\n  if (icon !== undefined) {\n    if (typeof icon === 'string') {\n      return <AnimatedIconWrapper>{icon}</AnimatedIconWrapper>;\n    } else {\n      return icon;\n    }\n  }\n\n  if (type === 'blank') {\n    return null;\n  }\n\n  return (\n    <IndicatorWrapper>\n      <LoaderIcon {...iconTheme} />\n      {type !== 'loading' && (\n        <StatusWrapper>\n          {type === 'error' ? (\n            <ErrorIcon {...iconTheme} />\n          ) : (\n            <CheckmarkIcon {...iconTheme} />\n          )}\n        </StatusWrapper>\n      )}\n    </IndicatorWrapper>\n  );\n};\n", "import { styled, keyframes } from 'goober';\n\nconst circleAnimation = keyframes`\nfrom {\n  transform: scale(0) rotate(45deg);\n\topacity: 0;\n}\nto {\n transform: scale(1) rotate(45deg);\n  opacity: 1;\n}`;\n\nconst firstLineAnimation = keyframes`\nfrom {\n  transform: scale(0);\n  opacity: 0;\n}\nto {\n  transform: scale(1);\n  opacity: 1;\n}`;\n\nconst secondLineAnimation = keyframes`\nfrom {\n  transform: scale(0) rotate(90deg);\n\topacity: 0;\n}\nto {\n  transform: scale(1) rotate(90deg);\n\topacity: 1;\n}`;\n\nexport interface ErrorTheme {\n  primary?: string;\n  secondary?: string;\n}\n\nexport const ErrorIcon = styled('div')<ErrorTheme>`\n  width: 20px;\n  opacity: 0;\n  height: 20px;\n  border-radius: 10px;\n  background: ${(p) => p.primary || '#ff4b4b'};\n  position: relative;\n  transform: rotate(45deg);\n\n  animation: ${circleAnimation} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)\n    forwards;\n  animation-delay: 100ms;\n\n  &:after,\n  &:before {\n    content: '';\n    animation: ${firstLineAnimation} 0.15s ease-out forwards;\n    animation-delay: 150ms;\n    position: absolute;\n    border-radius: 3px;\n    opacity: 0;\n    background: ${(p) => p.secondary || '#fff'};\n    bottom: 9px;\n    left: 4px;\n    height: 2px;\n    width: 12px;\n  }\n\n  &:before {\n    animation: ${secondLineAnimation} 0.15s ease-out forwards;\n    animation-delay: 180ms;\n    transform: rotate(90deg);\n  }\n`;\n", "import { styled, keyframes } from 'goober';\n\nconst rotate = keyframes`\n  from {\n    transform: rotate(0deg);\n  }\n  to {\n    transform: rotate(360deg);\n  }\n`;\n\nexport interface LoaderTheme {\n  primary?: string;\n  secondary?: string;\n}\n\nexport const LoaderIcon = styled('div')<LoaderTheme>`\n  width: 12px;\n  height: 12px;\n  box-sizing: border-box;\n  border: 2px solid;\n  border-radius: 100%;\n  border-color: ${(p) => p.secondary || '#e0e0e0'};\n  border-right-color: ${(p) => p.primary || '#616161'};\n  animation: ${rotate} 1s linear infinite;\n`;\n", "import { styled, keyframes } from 'goober';\n\nconst circleAnimation = keyframes`\nfrom {\n  transform: scale(0) rotate(45deg);\n\topacity: 0;\n}\nto {\n  transform: scale(1) rotate(45deg);\n\topacity: 1;\n}`;\n\nconst checkmarkAnimation = keyframes`\n0% {\n\theight: 0;\n\twidth: 0;\n\topacity: 0;\n}\n40% {\n  height: 0;\n\twidth: 6px;\n\topacity: 1;\n}\n100% {\n  opacity: 1;\n  height: 10px;\n}`;\n\nexport interface CheckmarkTheme {\n  primary?: string;\n  secondary?: string;\n}\n\nexport const CheckmarkIcon = styled('div')<CheckmarkTheme>`\n  width: 20px;\n  opacity: 0;\n  height: 20px;\n  border-radius: 10px;\n  background: ${(p) => p.primary || '#61d345'};\n  position: relative;\n  transform: rotate(45deg);\n\n  animation: ${circleAnimation} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)\n    forwards;\n  animation-delay: 100ms;\n  &:after {\n    content: '';\n    box-sizing: border-box;\n    animation: ${checkmarkAnimation} 0.2s ease-out forwards;\n    opacity: 0;\n    animation-delay: 200ms;\n    position: absolute;\n    border-right: 2px solid;\n    border-bottom: 2px solid;\n    border-color: ${(p) => p.secondary || '#fff'};\n    bottom: 6px;\n    left: 6px;\n    height: 10px;\n    width: 6px;\n  }\n`;\n", "import { css, setup } from 'goober';\nimport * as React from 'react';\nimport {\n  resolveValue,\n  ToasterProps,\n  ToastPosition,\n  ToastWrapperProps,\n} from '../core/types';\nimport { useToaster } from '../core/use-toaster';\nimport { prefersReducedMotion } from '../core/utils';\nimport { ToastBar } from './toast-bar';\n\nsetup(React.createElement);\n\nconst ToastWrapper = ({\n  id,\n  className,\n  style,\n  onHeightUpdate,\n  children,\n}: ToastWrapperProps) => {\n  const ref = React.useCallback(\n    (el: HTMLElement | null) => {\n      if (el) {\n        const updateHeight = () => {\n          const height = el.getBoundingClientRect().height;\n          onHeightUpdate(id, height);\n        };\n        updateHeight();\n        new MutationObserver(updateHeight).observe(el, {\n          subtree: true,\n          childList: true,\n          characterData: true,\n        });\n      }\n    },\n    [id, onHeightUpdate]\n  );\n\n  return (\n    <div ref={ref} className={className} style={style}>\n      {children}\n    </div>\n  );\n};\n\nconst getPositionStyle = (\n  position: ToastPosition,\n  offset: number\n): React.CSSProperties => {\n  const top = position.includes('top');\n  const verticalStyle: React.CSSProperties = top ? { top: 0 } : { bottom: 0 };\n  const horizontalStyle: React.CSSProperties = position.includes('center')\n    ? {\n        justifyContent: 'center',\n      }\n    : position.includes('right')\n    ? {\n        justifyContent: 'flex-end',\n      }\n    : {};\n  return {\n    left: 0,\n    right: 0,\n    display: 'flex',\n    position: 'absolute',\n    transition: prefersReducedMotion()\n      ? undefined\n      : `all 230ms cubic-bezier(.21,1.02,.73,1)`,\n    transform: `translateY(${offset * (top ? 1 : -1)}px)`,\n    ...verticalStyle,\n    ...horizontalStyle,\n  };\n};\n\nconst activeClass = css`\n  z-index: 9999;\n  > * {\n    pointer-events: auto;\n  }\n`;\n\nconst DEFAULT_OFFSET = 16;\n\nexport const Toaster: React.FC<ToasterProps> = ({\n  reverseOrder,\n  position = 'top-center',\n  toastOptions,\n  gutter,\n  children,\n  containerStyle,\n  containerClassName,\n}) => {\n  const { toasts, handlers } = useToaster(toastOptions);\n\n  return (\n    <div\n      id=\"_rht_toaster\"\n      style={{\n        position: 'fixed',\n        zIndex: 9999,\n        top: DEFAULT_OFFSET,\n        left: DEFAULT_OFFSET,\n        right: DEFAULT_OFFSET,\n        bottom: DEFAULT_OFFSET,\n        pointerEvents: 'none',\n        ...containerStyle,\n      }}\n      className={containerClassName}\n      onMouseEnter={handlers.startPause}\n      onMouseLeave={handlers.endPause}\n    >\n      {toasts.map((t) => {\n        const toastPosition = t.position || position;\n        const offset = handlers.calculateOffset(t, {\n          reverseOrder,\n          gutter,\n          defaultPosition: position,\n        });\n        const positionStyle = getPositionStyle(toastPosition, offset);\n\n        return (\n          <ToastWrapper\n            id={t.id}\n            key={t.id}\n            onHeightUpdate={handlers.updateHeight}\n            className={t.visible ? activeClass : ''}\n            style={positionStyle}\n          >\n            {t.type === 'custom' ? (\n              resolveValue(t.message, t)\n            ) : children ? (\n              children(t)\n            ) : (\n              <ToastBar toast={t} position={toastPosition} />\n            )}\n          </ToastWrapper>\n        );\n      })}\n    </div>\n  );\n};\n"], "mappings": ";2jBAAA,IAAAA,GAAA,GAAAC,EAAAD,GAAA,mBAAAE,EAAA,cAAAC,EAAA,eAAAC,EAAA,aAAAC,EAAA,cAAAC,EAAA,YAAAC,EAAA,YAAAC,GAAA,iBAAAC,EAAA,UAAAC,EAAA,eAAAC,EAAA,oBAAAC,IAAA,eAAAC,GAAAb,ICuBA,IAAMc,GACJC,GAEA,OAAOA,GAAkB,WAEdC,EAAe,CAC1BD,EACAE,IACYH,GAAWC,CAAa,EAAIA,EAAcE,CAAG,EAAIF,EC/BxD,IAAMG,GAAS,IAAM,CAC1B,IAAIC,EAAQ,EACZ,MAAO,KACG,EAAEA,GAAO,SAAS,CAE9B,GAAG,EAEUC,GAAwB,IAAM,CAEzC,IAAIC,EAEJ,MAAO,IAAM,CACX,GAAIA,IAAuB,QAAa,OAAO,OAAW,IAAa,CACrE,IAAMC,EAAa,WAAW,kCAAkC,EAChED,EAAqB,CAACC,GAAcA,EAAW,QAEjD,OAAOD,CACT,CACF,GAAG,EClBH,IAAAE,EAA4C,iBAGtCC,GAAc,GA+Cb,IAAMC,EAAU,CAACC,EAAcC,IAA0B,CAC9D,OAAQA,EAAO,KAAM,CACnB,IAAK,GACH,MAAO,CACL,GAAGD,EACH,OAAQ,CAACC,EAAO,MAAO,GAAGD,EAAM,MAAM,EAAE,MAAM,EAAGE,EAAW,CAC9D,EAEF,IAAK,GACH,MAAO,CACL,GAAGF,EACH,OAAQA,EAAM,OAAO,IAAKG,GACxBA,EAAE,KAAOF,EAAO,MAAM,GAAK,CAAE,GAAGE,EAAG,GAAGF,EAAO,KAAM,EAAIE,CACzD,CACF,EAEF,IAAK,GACH,GAAM,CAAE,MAAAC,CAAM,EAAIH,EAClB,OAAOF,EAAQC,EAAO,CACpB,KAAMA,EAAM,OAAO,KAAMG,GAAMA,EAAE,KAAOC,EAAM,EAAE,EAC5C,EACA,EACJ,MAAAA,CACF,CAAC,EAEH,IAAK,GACH,GAAM,CAAE,QAAAC,CAAQ,EAAIJ,EAEpB,MAAO,CACL,GAAGD,EACH,OAAQA,EAAM,OAAO,IAAKG,GACxBA,EAAE,KAAOE,GAAWA,IAAY,OAC5B,CACE,GAAGF,EACH,UAAW,GACX,QAAS,EACX,EACAA,CACN,CACF,EACF,IAAK,GACH,OAAIF,EAAO,UAAY,OACd,CACL,GAAGD,EACH,OAAQ,CAAC,CACX,EAEK,CACL,GAAGA,EACH,OAAQA,EAAM,OAAO,OAAQG,GAAMA,EAAE,KAAOF,EAAO,OAAO,CAC5D,EAEF,IAAK,GACH,MAAO,CACL,GAAGD,EACH,SAAUC,EAAO,IACnB,EAEF,IAAK,GACH,IAAMK,EAAOL,EAAO,MAAQD,EAAM,UAAY,GAE9C,MAAO,CACL,GAAGA,EACH,SAAU,OACV,OAAQA,EAAM,OAAO,IAAKG,IAAO,CAC/B,GAAGA,EACH,cAAeA,EAAE,cAAgBG,CACnC,EAAE,CACJ,CACJ,CACF,EAEMC,EAA2C,CAAC,EAE9CC,EAAqB,CAAE,OAAQ,CAAC,EAAG,SAAU,MAAU,EAE9CC,EAAYR,GAAmB,CAC1CO,EAAcT,EAAQS,EAAaP,CAAM,EACzCM,EAAU,QAASG,GAAa,CAC9BA,EAASF,CAAW,CACtB,CAAC,CACH,EAEaG,GAET,CACF,MAAO,IACP,MAAO,IACP,QAAS,IACT,QAAS,IACT,OAAQ,GACV,EAEaC,EAAW,CAACC,EAAoC,CAAC,IAAa,CACzE,GAAM,CAACb,EAAOc,CAAQ,KAAI,YAAgBN,CAAW,EAC/CO,KAAU,UAAOP,CAAW,KAGlC,aAAU,KACJO,EAAQ,UAAYP,GACtBM,EAASN,CAAW,EAEtBD,EAAU,KAAKO,CAAQ,EAChB,IAAM,CACX,IAAME,EAAQT,EAAU,QAAQO,CAAQ,EACpCE,EAAQ,IACVT,EAAU,OAAOS,EAAO,CAAC,CAE7B,GACC,CAAC,CAAC,EAEL,IAAMC,EAAejB,EAAM,OAAO,IAAKG,GAAG,CAjK5C,IAAAe,EAAAC,EAAAC,EAiKgD,OAC5C,GAAGP,EACH,GAAGA,EAAaV,EAAE,IAAI,EACtB,GAAGA,EACH,YACEA,EAAE,eACFe,EAAAL,EAAaV,EAAE,IAAI,IAAnB,YAAAe,EAAsB,eACtBL,GAAA,YAAAA,EAAc,aAChB,SACEV,EAAE,YACFgB,EAAAN,EAAaV,EAAE,IAAI,IAAnB,YAAAgB,EAAsB,YACtBN,GAAA,YAAAA,EAAc,WACdF,GAAgBR,EAAE,IAAI,EACxB,MAAO,CACL,GAAGU,EAAa,MAChB,IAAGO,EAAAP,EAAaV,EAAE,IAAI,IAAnB,YAAAiB,EAAsB,MACzB,GAAGjB,EAAE,KACP,CACF,EAAE,EAEF,MAAO,CACL,GAAGH,EACH,OAAQiB,CACV,CACF,ECzKA,IAAMI,GAAc,CAClBC,EACAC,EAAkB,QAClBC,KACW,CACX,UAAW,KAAK,IAAI,EACpB,QAAS,GACT,UAAW,GACX,KAAAD,EACA,UAAW,CACT,KAAM,SACN,YAAa,QACf,EACA,QAAAD,EACA,cAAe,EACf,GAAGE,EACH,IAAIA,GAAA,YAAAA,EAAM,KAAMC,EAAM,CACxB,GAEMC,EACHH,GACD,CAACD,EAASK,IAAY,CACpB,IAAMC,EAAQP,GAAYC,EAASC,EAAMI,CAAO,EAChD,OAAAE,EAAS,CAAE,OAA+B,MAAAD,CAAM,CAAC,EAC1CA,EAAM,EACf,EAEIA,EAAQ,CAACN,EAAkBE,IAC/BE,EAAc,OAAO,EAAEJ,EAASE,CAAI,EAEtCI,EAAM,MAAQF,EAAc,OAAO,EACnCE,EAAM,QAAUF,EAAc,SAAS,EACvCE,EAAM,QAAUF,EAAc,SAAS,EACvCE,EAAM,OAASF,EAAc,QAAQ,EAErCE,EAAM,QAAWE,GAAqB,CACpCD,EAAS,CACP,OACA,QAAAC,CACF,CAAC,CACH,EAEAF,EAAM,OAAUE,GACdD,EAAS,CAAE,OAA+B,QAAAC,CAAQ,CAAC,EAErDF,EAAM,QAAU,CACdG,EACAC,EAKAR,IACG,CACH,IAAMS,EAAKL,EAAM,QAAQI,EAAK,QAAS,CAAE,GAAGR,EAAM,GAAGA,GAAA,YAAAA,EAAM,OAAQ,CAAC,EAEpE,OAAI,OAAOO,GAAY,aACrBA,EAAUA,EAAQ,GAGpBA,EACG,KAAMG,GAAM,CACX,IAAMC,EAAiBH,EAAK,QACxBI,EAAaJ,EAAK,QAASE,CAAC,EAC5B,OAEJ,OAAIC,EACFP,EAAM,QAAQO,EAAgB,CAC5B,GAAAF,EACA,GAAGT,EACH,GAAGA,GAAA,YAAAA,EAAM,OACX,CAAC,EAEDI,EAAM,QAAQK,CAAE,EAEXC,CACT,CAAC,EACA,MAAOG,GAAM,CACZ,IAAMC,EAAeN,EAAK,MAAQI,EAAaJ,EAAK,MAAOK,CAAC,EAAI,OAE5DC,EACFV,EAAM,MAAMU,EAAc,CACxB,GAAAL,EACA,GAAGT,EACH,GAAGA,GAAA,YAAAA,EAAM,KACX,CAAC,EAEDI,EAAM,QAAQK,CAAE,CAEpB,CAAC,EAEIF,CACT,EC5GA,IAAAQ,EAAuC,iBAKvC,IAAMC,GAAe,CAACC,EAAiBC,IAAmB,CACxDC,EAAS,CACP,OACA,MAAO,CAAE,GAAIF,EAAS,OAAAC,CAAO,CAC/B,CAAC,CACH,EACME,GAAa,IAAM,CACvBD,EAAS,CACP,OACA,KAAM,KAAK,IAAI,CACjB,CAAC,CACH,EAEME,EAAgB,IAAI,IAEbC,GAAe,IAEtBC,GAAmB,CAACN,EAAiBO,EAAcF,KAAiB,CACxE,GAAID,EAAc,IAAIJ,CAAO,EAC3B,OAGF,IAAMQ,EAAU,WAAW,IAAM,CAC/BJ,EAAc,OAAOJ,CAAO,EAC5BE,EAAS,CACP,OACA,QAASF,CACX,CAAC,CACH,EAAGO,CAAW,EAEdH,EAAc,IAAIJ,EAASQ,CAAO,CACpC,EAEaC,EAAcC,GAAuC,CAChE,GAAM,CAAE,OAAAC,EAAQ,SAAAC,CAAS,EAAIC,EAASH,CAAY,KAElD,aAAU,IAAM,CACd,GAAIE,EACF,OAGF,IAAME,EAAM,KAAK,IAAI,EACfC,EAAWJ,EAAO,IAAKK,GAAM,CACjC,GAAIA,EAAE,WAAa,IACjB,OAGF,IAAMC,GACHD,EAAE,UAAY,GAAKA,EAAE,eAAiBF,EAAME,EAAE,WAEjD,GAAIC,EAAe,EAAG,CAChBD,EAAE,SACJE,EAAM,QAAQF,EAAE,EAAE,EAEpB,OAEF,OAAO,WAAW,IAAME,EAAM,QAAQF,EAAE,EAAE,EAAGC,CAAY,CAC3D,CAAC,EAED,MAAO,IAAM,CACXF,EAAS,QAASP,GAAYA,GAAW,aAAaA,CAAO,CAAC,CAChE,CACF,EAAG,CAACG,EAAQC,CAAQ,CAAC,EAErB,IAAMO,KAAW,eAAY,IAAM,CAC7BP,GACFV,EAAS,CAAE,OAA4B,KAAM,KAAK,IAAI,CAAE,CAAC,CAE7D,EAAG,CAACU,CAAQ,CAAC,EAEPQ,KAAkB,eACtB,CACEF,EACAG,IAKG,CACH,GAAM,CAAE,aAAAC,EAAe,GAAO,OAAAC,EAAS,EAAG,gBAAAC,CAAgB,EAAIH,GAAQ,CAAC,EAEjEI,EAAiBd,EAAO,OAC3BK,IACEA,EAAE,UAAYQ,MACZN,EAAM,UAAYM,IAAoBR,EAAE,MAC/C,EACMU,EAAaD,EAAe,UAAWT,GAAMA,EAAE,KAAOE,EAAM,EAAE,EAC9DS,EAAeF,EAAe,OAClC,CAACP,EAAOU,IAAMA,EAAIF,GAAcR,EAAM,OACxC,EAAE,OAOF,OALeO,EACZ,OAAQT,GAAMA,EAAE,OAAO,EACvB,MAAM,GAAIM,EAAe,CAACK,EAAe,CAAC,EAAI,CAAC,EAAGA,CAAY,CAAE,EAChE,OAAO,CAACE,EAAKb,IAAMa,GAAOb,EAAE,QAAU,GAAKO,EAAQ,CAAC,CAGzD,EACA,CAACZ,CAAM,CACT,EAEA,sBAAU,IAAM,CAEdA,EAAO,QAASO,GAAU,CACxB,GAAIA,EAAM,UACRZ,GAAiBY,EAAM,GAAIA,EAAM,WAAW,MACvC,CAEL,IAAMV,EAAUJ,EAAc,IAAIc,EAAM,EAAE,EACtCV,IACF,aAAaA,CAAO,EACpBJ,EAAc,OAAOc,EAAM,EAAE,GAGnC,CAAC,CACH,EAAG,CAACP,CAAM,CAAC,EAEJ,CACL,OAAAA,EACA,SAAU,CACR,aAAAZ,GACA,WAAAI,GACA,SAAAgB,EACA,gBAAAC,CACF,CACF,CACF,ECnIA,IAAAU,EAAuB,oBACvBC,EAAkC,kBCDlC,IAAAC,EAAuB,oBACvBC,EAAkC,kBCDlC,IAAAC,EAAkC,kBAE5BC,GAAkB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,GAUlBC,GAAqB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,GAUrBC,GAAsB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,GAefC,KAAY,UAAO,KAAK;AAAA;AAAA;AAAA;AAAA;AAAA,gBAKpBC,GAAMA,EAAE,SAAW;AAAA;AAAA;AAAA;AAAA,eAIrBJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,iBAOEC;AAAA;AAAA;AAAA;AAAA;AAAA,kBAKEG,GAAMA,EAAE,WAAa;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,iBAQvBF;AAAA;AAAA;AAAA;EClEjB,IAAAG,EAAkC,kBAE5BC,GAAS;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAcFC,KAAa,UAAO,KAAK;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBAMnBC,GAAMA,EAAE,WAAa;AAAA,wBACfA,GAAMA,EAAE,SAAW;AAAA,eAC7BF;ECxBf,IAAAG,EAAkC,kBAE5BC,GAAkB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,GAUlBC,GAAqB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,GAqBdC,KAAgB,UAAO,KAAK;AAAA;AAAA;AAAA;AAAA;AAAA,gBAKxBC,GAAMA,EAAE,SAAW;AAAA;AAAA;AAAA;AAAA,eAIrBH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,iBAMEC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,oBAMIE,GAAMA,EAAE,WAAa;AAAA;AAAA;AAAA;AAAA;AAAA;EH9C1C,IAAMC,MAAgB,UAAO,KAAK;AAAA;AAAA,EAI5BC,MAAmB,UAAO,KAAK;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAS/BC,GAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,GAUDC,MAAsB,UAAO,KAAK;AAAA;AAAA;AAAA;AAAA;AAAA,eAKhCD;AAAA;AAAA,EAUFE,EAER,CAAC,CAAE,MAAAC,CAAM,IAAM,CAClB,GAAM,CAAE,KAAAC,EAAM,KAAAC,EAAM,UAAAC,CAAU,EAAIH,EAClC,OAAIC,IAAS,OACP,OAAOA,GAAS,SACX,gBAACH,GAAA,KAAqBG,CAAK,EAE3BA,EAIPC,IAAS,QACJ,KAIP,gBAACN,GAAA,KACC,gBAACQ,EAAA,CAAY,GAAGD,EAAW,EAC1BD,IAAS,WACR,gBAACP,GAAA,KACEO,IAAS,QACR,gBAACG,EAAA,CAAW,GAAGF,EAAW,EAE1B,gBAACG,EAAA,CAAe,GAAGH,EAAW,CAElC,CAEJ,CAEJ,EDrEA,IAAMI,GAAkBC,GAAmB;AAAA,+BACZA,EAAS;AAAA;AAAA,EAIlCC,GAAiBD,GAAmB;AAAA;AAAA,iCAETA,EAAS;AAAA,EAGpCE,GAAkB,kCAClBC,GAAmB,kCAEnBC,MAAe,UAAO,KAAK;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAc3BC,MAAU,UAAO,KAAK;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAmBtBC,GAAoB,CACxBC,EACAC,IACwB,CAExB,IAAMR,EADMO,EAAS,SAAS,KAAK,EACd,EAAI,GAEnB,CAACE,EAAOC,CAAI,EAAIC,EAAqB,EACvC,CAACT,GAAiBC,EAAgB,EAClC,CAACJ,GAAeC,CAAM,EAAGC,GAAcD,CAAM,CAAC,EAElD,MAAO,CACL,UAAWQ,EACP,MAAG,aAAUC,CAAK,gDAClB,MAAG,aAAUC,CAAI,6CACvB,CACF,EAEaE,EAA0C,OACrD,CAAC,CAAE,MAAAC,EAAO,SAAAN,EAAU,MAAAO,EAAO,SAAAC,CAAS,IAAM,CACxC,IAAMC,EAAsCH,EAAM,OAC9CP,GACEO,EAAM,UAAYN,GAAY,aAC9BM,EAAM,OACR,EACA,CAAE,QAAS,CAAE,EAEXI,EAAO,gBAACC,EAAA,CAAU,MAAOL,EAAO,EAChCM,EACJ,gBAACd,GAAA,CAAS,GAAGQ,EAAM,WAChBO,EAAaP,EAAM,QAASA,CAAK,CACpC,EAGF,OACE,gBAACT,GAAA,CACC,UAAWS,EAAM,UACjB,MAAO,CACL,GAAGG,EACH,GAAGF,EACH,GAAGD,EAAM,KACX,GAEC,OAAOE,GAAa,WACnBA,EAAS,CACP,KAAAE,EACA,QAAAE,CACF,CAAC,EAED,gCACGF,EACAE,CACH,CAEJ,CAEJ,CACF,EK9GA,IAAAE,EAA2B,kBAC3BC,EAAuB,uBAWvB,SAAY,eAAa,EAEzB,IAAMC,GAAe,CAAC,CACpB,GAAAC,EACA,UAAAC,EACA,MAAAC,EACA,eAAAC,EACA,SAAAC,CACF,IAAyB,CACvB,IAAMC,EAAY,cACfC,GAA2B,CAC1B,GAAIA,EAAI,CACN,IAAMC,EAAe,IAAM,CACzB,IAAMC,EAASF,EAAG,sBAAsB,EAAE,OAC1CH,EAAeH,EAAIQ,CAAM,CAC3B,EACAD,EAAa,EACb,IAAI,iBAAiBA,CAAY,EAAE,QAAQD,EAAI,CAC7C,QAAS,GACT,UAAW,GACX,cAAe,EACjB,CAAC,EAEL,EACA,CAACN,EAAIG,CAAc,CACrB,EAEA,OACE,gBAAC,OAAI,IAAKE,EAAK,UAAWJ,EAAW,MAAOC,GACzCE,CACH,CAEJ,EAEMK,GAAmB,CACvBC,EACAC,IACwB,CACxB,IAAMC,EAAMF,EAAS,SAAS,KAAK,EAC7BG,EAAqCD,EAAM,CAAE,IAAK,CAAE,EAAI,CAAE,OAAQ,CAAE,EACpEE,EAAuCJ,EAAS,SAAS,QAAQ,EACnE,CACE,eAAgB,QAClB,EACAA,EAAS,SAAS,OAAO,EACzB,CACE,eAAgB,UAClB,EACA,CAAC,EACL,MAAO,CACL,KAAM,EACN,MAAO,EACP,QAAS,OACT,SAAU,WACV,WAAYK,EAAqB,EAC7B,OACA,yCACJ,UAAW,cAAcJ,GAAUC,EAAM,EAAI,SAC7C,GAAGC,EACH,GAAGC,CACL,CACF,EAEME,GAAc;AAAA;AAAA;AAAA;AAAA;AAAA,EAOdC,EAAiB,GAEVC,EAAkC,CAAC,CAC9C,aAAAC,EACA,SAAAT,EAAW,aACX,aAAAU,EACA,OAAAC,EACA,SAAAjB,EACA,eAAAkB,EACA,mBAAAC,CACF,IAAM,CACJ,GAAM,CAAE,OAAAC,EAAQ,SAAAC,CAAS,EAAIC,EAAWN,CAAY,EAEpD,OACE,gBAAC,OACC,GAAG,eACH,MAAO,CACL,SAAU,QACV,OAAQ,KACR,IAAKH,EACL,KAAMA,EACN,MAAOA,EACP,OAAQA,EACR,cAAe,OACf,GAAGK,CACL,EACA,UAAWC,EACX,aAAcE,EAAS,WACvB,aAAcA,EAAS,UAEtBD,EAAO,IAAKG,GAAM,CACjB,IAAMC,EAAgBD,EAAE,UAAYjB,EAC9BC,EAASc,EAAS,gBAAgBE,EAAG,CACzC,aAAAR,EACA,OAAAE,EACA,gBAAiBX,CACnB,CAAC,EACKmB,EAAgBpB,GAAiBmB,EAAejB,CAAM,EAE5D,OACE,gBAACZ,GAAA,CACC,GAAI4B,EAAE,GACN,IAAKA,EAAE,GACP,eAAgBF,EAAS,aACzB,UAAWE,EAAE,QAAUX,GAAc,GACrC,MAAOa,GAENF,EAAE,OAAS,SACVG,EAAaH,EAAE,QAASA,CAAC,EACvBvB,EACFA,EAASuB,CAAC,EAEV,gBAACI,EAAA,CAAS,MAAOJ,EAAG,SAAUC,EAAe,CAEjD,CAEJ,CAAC,CACH,CAEJ,EXjIA,IAAOI,GAAQC", "names": ["src_exports", "__export", "CheckmarkIcon", "ErrorIcon", "LoaderIcon", "ToastBar", "ToastIcon", "Toaster", "src_default", "resolveValue", "toast", "useToaster", "useStore", "__toCommonJS", "isFunction", "valOrFunction", "resolveValue", "arg", "genId", "count", "prefersReducedMotion", "shouldReduceMotion", "mediaQuery", "import_react", "TOAST_LIMIT", "reducer", "state", "action", "TOAST_LIMIT", "t", "toast", "toastId", "diff", "listeners", "memoryState", "dispatch", "listener", "defaultTimeouts", "useStore", "toastOptions", "setState", "initial", "index", "mergedToasts", "_a", "_b", "_c", "createToast", "message", "type", "opts", "genId", "createHandler", "options", "toast", "dispatch", "toastId", "promise", "msgs", "id", "p", "successMessage", "resolveValue", "e", "errorMessage", "import_react", "updateHeight", "toastId", "height", "dispatch", "startPause", "toastTimeouts", "REMOVE_DELAY", "addToRemoveQueue", "<PERSON><PERSON><PERSON><PERSON>", "timeout", "useToaster", "toastOptions", "toasts", "pausedAt", "useStore", "now", "timeouts", "t", "durationLeft", "toast", "endPause", "calculateOffset", "opts", "reverseOrder", "gutter", "defaultPosition", "relevantToasts", "toastIndex", "toastsBefore", "i", "acc", "React", "import_goober", "React", "import_goober", "import_goober", "circleAnimation", "firstLineAnimation", "secondLineAnimation", "ErrorIcon", "p", "import_goober", "rotate", "LoaderIcon", "p", "import_goober", "circleAnimation", "checkmarkAnimation", "CheckmarkIcon", "p", "StatusWrapper", "IndicatorWrapper", "enter", "AnimatedIconWrapper", "ToastIcon", "toast", "icon", "type", "iconTheme", "LoaderIcon", "ErrorIcon", "CheckmarkIcon", "enterAnimation", "factor", "exitAnimation", "fadeInAnimation", "fadeOutAnimation", "ToastBarBase", "Message", "getAnimationStyle", "position", "visible", "enter", "exit", "prefersReducedMotion", "ToastBar", "toast", "style", "children", "animationStyle", "icon", "ToastIcon", "message", "resolveValue", "import_goober", "React", "ToastWrapper", "id", "className", "style", "onHeightUpdate", "children", "ref", "el", "updateHeight", "height", "getPositionStyle", "position", "offset", "top", "verticalStyle", "horizontalStyle", "prefersReducedMotion", "activeClass", "DEFAULT_OFFSET", "Toaster", "reverseOrder", "toastOptions", "gutter", "containerStyle", "containerClassName", "toasts", "handlers", "useToaster", "t", "toastPosition", "positionStyle", "resolveValue", "ToastBar", "src_default", "toast"]}