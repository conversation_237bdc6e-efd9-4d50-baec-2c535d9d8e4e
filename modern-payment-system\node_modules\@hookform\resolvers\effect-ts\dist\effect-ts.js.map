{"version": 3, "file": "effect-ts.js", "sources": ["../src/effect-ts.ts"], "sourcesContent": ["import { toNestErrors, validateFieldsNatively } from '@hookform/resolvers';\nimport { Effect } from 'effect';\n\nimport { ArrayFormatter, decodeUnknown } from 'effect/ParseResult';\nimport type { FieldErrors } from 'react-hook-form';\nimport type { Resolver } from './types';\n\nexport const effectTsResolver: Resolver =\n  (schema, config = { errors: 'all', onExcessProperty: 'ignore' }) =>\n  (values, _, options) => {\n    return decodeUnknown(\n      schema,\n      config,\n    )(values).pipe(\n      Effect.catchAll((parseIssue) =>\n        Effect.flip(ArrayFormatter.formatIssue(parseIssue)),\n      ),\n      Effect.mapError((issues) => {\n        const errors = issues.reduce((acc, current) => {\n          const key = current.path.join('.');\n          acc[key] = { message: current.message, type: current._tag };\n          return acc;\n        }, {} as FieldErrors);\n\n        return toNestErrors(errors, options);\n      }),\n      Effect.tap(() =>\n        Effect.sync(\n          () =>\n            options.shouldUseNativeValidation &&\n            validateFieldsNatively({}, options),\n        ),\n      ),\n      Effect.match({\n        onFailure: (errors) => ({ errors, values: {} }),\n        onSuccess: (result) => ({ errors: {}, values: result }),\n      }),\n      Effect.runPromise,\n    );\n  };\n"], "names": ["schema", "config", "errors", "onExcessProperty", "values", "_", "options", "decodeUnknown", "pipe", "Effect", "catchAll", "parseIssue", "flip", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "formatIssue", "mapError", "issues", "reduce", "acc", "current", "path", "join", "message", "type", "_tag", "toNestErrors", "tap", "sync", "shouldUseNativeValidation", "validateFieldsNatively", "match", "onFailure", "onSuccess", "result", "runPromise"], "mappings": "kHAQE,SAACA,EAAQC,GAAsD,YAAtDA,IAAAA,IAAAA,EAAS,CAAEC,OAAQ,MAAOC,iBAAkB,WACpDC,SAAAA,EAAQC,EAAGC,GACV,OAAOC,EAAAA,cACLP,EACAC,EAFKM,CAGLH,GAAQI,KACRC,EAAAA,OAAOC,SAAS,SAACC,GACf,OAAAF,EAAAA,OAAOG,KAAKC,EAAcA,eAACC,YAAYH,GAAY,GAErDF,EAAAA,OAAOM,SAAS,SAACC,GACf,IAAMd,EAASc,EAAOC,OAAO,SAACC,EAAKC,GAGjC,OADAD,EADYC,EAAQC,KAAKC,KAAK,MACnB,CAAEC,QAASH,EAAQG,QAASC,KAAMJ,EAAQK,MAC9CN,CACT,EAAG,CAAiB,GAEpB,OAAOO,EAAAA,aAAavB,EAAQI,EAC9B,GACAG,EAAAA,OAAOiB,IAAI,WAAA,OACTjB,EAAAA,OAAOkB,KACL,WAAA,OACErB,EAAQsB,2BACRC,EAAAA,uBAAuB,CAAA,EAAIvB,EAAQ,EACtC,GAEHG,EAAAA,OAAOqB,MAAM,CACXC,UAAW,SAAC7B,GAAM,MAAM,CAAEA,OAAAA,EAAQE,OAAQ,CAAA,EAAI,EAC9C4B,UAAW,SAACC,GAAY,MAAA,CAAE/B,OAAQ,CAAA,EAAIE,OAAQ6B,EAAQ,IAExDxB,EAAMA,OAACyB,WAEX,CAAC"}