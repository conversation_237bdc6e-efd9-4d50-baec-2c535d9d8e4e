import React from 'react'
import { motion } from 'framer-motion'
import { CheckCircle, Download, Share, RotateCcw } from 'lucide-react'
import './Steps.css'

const SuccessStep = ({ paymentData, onReset }) => {
  return (
    <div className="success-step">
      <motion.div
        className="success-content"
        initial={{ opacity: 0, scale: 0.8 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.5 }}
      >
        <motion.div
          className="success-icon"
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          transition={{ delay: 0.2, type: "spring", stiffness: 200 }}
        >
          <CheckCircle size={64} />
        </motion.div>

        <h2 className="success-title">Payment Successful!</h2>
        <p className="success-description">
          Your payment of ₹{paymentData.total.toLocaleString()} has been processed successfully.
        </p>

        <div className="success-details">
          <div className="detail-item">
            <span>Transaction ID:</span>
            <span>TXN{Date.now()}</span>
          </div>
          <div className="detail-item">
            <span>Payment Method:</span>
            <span>{paymentData.selectedMethod}</span>
          </div>
          <div className="detail-item">
            <span>Date & Time:</span>
            <span>{new Date().toLocaleString()}</span>
          </div>
        </div>

        <div className="success-actions">
          <button className="action-btn primary">
            <Download size={16} />
            Download Receipt
          </button>
          <button className="action-btn secondary">
            <Share size={16} />
            Share
          </button>
          <button className="action-btn secondary" onClick={onReset}>
            <RotateCcw size={16} />
            New Payment
          </button>
        </div>
      </motion.div>
    </div>
  )
}

export default SuccessStep
