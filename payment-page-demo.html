<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payment Page - Demo</title>
    <style>
        /* Payment Page Styles - Clean Black/White Theme */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
            padding: 20px;
        }

        .payment-page {
            max-width: 700px;
            margin: 0 auto;
            padding: 20px;
            background: #ffffff;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            color: #000000;
        }

        /* Navigation Tabs */
        .nav-tabs {
            display: flex;
            margin-bottom: 30px;
            border-bottom: 1px solid #e0e0e0;
        }

        .nav-tab {
            padding: 12px 24px;
            font-size: 16px;
            font-weight: 500;
            color: #666666;
            cursor: pointer;
            border-bottom: 2px solid transparent;
            transition: all 0.2s ease;
        }

        .nav-tab.active {
            color: #000000;
            border-bottom-color: #000000;
            font-weight: 600;
        }

        /* Payment Content */
        .payment-content {
            display: flex;
            flex-direction: column;
            gap: 25px;
        }

        /* Section Styling */
        .section {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .section-title {
            font-size: 18px;
            font-weight: 600;
            color: #000000;
            margin: 0;
        }

        /* Saved Payment Methods */
        .saved-methods {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .saved-method {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 16px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            background: #ffffff;
        }

        .method-info {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .method-type {
            font-weight: 600;
            font-size: 16px;
            color: #000000;
        }

        .method-details {
            color: #666666;
            font-size: 14px;
        }

        .remove-btn {
            background: none;
            border: none;
            color: #666666;
            cursor: pointer;
            font-size: 14px;
            text-decoration: underline;
            padding: 4px 8px;
        }

        .remove-btn:hover {
            color: #000000;
        }

        .add-payment-link {
            background: none;
            border: none;
            color: #000000;
            cursor: pointer;
            font-size: 14px;
            text-decoration: underline;
            text-align: left;
            padding: 0;
            margin-top: 8px;
        }

        /* Payment Methods */
        .payment-methods {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 12px;
        }

        .payment-method {
            display: flex;
            align-items: center;
            gap: 8px;
            cursor: pointer;
            font-size: 14px;
            color: #000000;
        }

        .payment-method input[type="radio"] {
            width: 16px;
            height: 16px;
            margin: 0;
        }

        /* Payment Variation */
        .payment-variation {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .variation-select {
            padding: 8px 12px;
            border: 1px solid #e0e0e0;
            border-radius: 4px;
            font-size: 14px;
            background: #ffffff;
            color: #000000;
            min-width: 120px;
        }

        /* Product Payment Select */
        .product-payment-select {
            padding: 8px 12px;
            border: 1px solid #e0e0e0;
            border-radius: 4px;
            font-size: 14px;
            background: #ffffff;
            color: #000000;
            width: 100%;
            max-width: 200px;
        }

        /* Checkbox Styling */
        .checkbox-label {
            display: flex;
            align-items: center;
            gap: 8px;
            cursor: pointer;
            font-size: 14px;
            color: #000000;
        }

        .checkbox-label input[type="checkbox"] {
            width: 16px;
            height: 16px;
            margin: 0;
        }

        /* Order Summary */
        .order-summary {
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 20px;
            background: #ffffff;
        }

        .summary-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            padding-bottom: 15px;
            border-bottom: 1px solid #e0e0e0;
        }

        .summary-title {
            font-size: 16px;
            font-weight: 600;
            color: #000000;
        }

        .summary-amount {
            font-size: 18px;
            font-weight: 700;
            color: #000000;
        }

        /* Pay Now Button */
        .pay-now-btn {
            width: 100%;
            padding: 16px;
            background: #000000;
            color: #ffffff;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: background-color 0.2s ease;
        }

        .pay-now-btn:hover {
            background: #333333;
        }

        /* Navigation Buttons */
        .navigation-buttons {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 20px;
        }

        .nav-btn {
            background: none;
            border: none;
            color: #000000;
            cursor: pointer;
            font-size: 14px;
            padding: 8px 16px;
            border-radius: 4px;
            transition: background-color 0.2s ease;
        }

        .nav-btn:hover {
            background: #f5f5f5;
        }
    </style>
</head>
<body>
    <div class="payment-page">
        <!-- Navigation Tabs -->
        <div class="nav-tabs">
            <div class="nav-tab">Shipping</div>
            <div class="nav-tab active">Payment</div>
        </div>

        <div class="payment-content">
            <!-- Saved Payment Methods -->
            <div class="section">
                <h3 class="section-title">Saved Payment Methods</h3>
                <div class="saved-methods">
                    <div class="saved-method">
                        <div class="method-info">
                            <span class="method-type">VISA</span>
                            <span class="method-details">**** 1234</span>
                        </div>
                        <button class="remove-btn">Remove</button>
                    </div>
                    <div class="saved-method">
                        <div class="method-info">
                            <span class="method-type">UPI</span>
                            <span class="method-details">user@upi</span>
                        </div>
                        <button class="remove-btn">Remove</button>
                    </div>
                </div>
                <button class="add-payment-link">Add payment method</button>
            </div>

            <!-- Payment Method Selection -->
            <div class="section">
                <h3 class="section-title">Payment Method</h3>
                <div class="payment-methods">
                    <label class="payment-method">
                        <input type="radio" name="paymentMethod" value="upi">
                        <span>UPI</span>
                    </label>
                    <label class="payment-method">
                        <input type="radio" name="paymentMethod" value="card">
                        <span>Credit/Debit Card</span>
                    </label>
                    <label class="payment-method">
                        <input type="radio" name="paymentMethod" value="wallet">
                        <span>Wallet</span>
                    </label>
                    <label class="payment-method">
                        <input type="radio" name="paymentMethod" value="cod">
                        <span>Cash on Delivery</span>
                    </label>
                    <label class="payment-method">
                        <input type="radio" name="paymentMethod" value="bnpl">
                        <span>Buy Now, Pay Later</span>
                    </label>
                </div>
            </div>

            <!-- Payment Variation -->
            <div class="section">
                <h3 class="section-title">Payment Variation</h3>
                <div class="payment-variation">
                    <select class="variation-select">
                        <option value="same-day">Same day</option>
                        <option value="next-day">Next day</option>
                        <option value="standard">Standard</option>
                    </select>
                    <label class="checkbox-label">
                        <input type="checkbox">
                        <span>Apply best offer</span>
                    </label>
                </div>
            </div>

            <!-- Product-Level Payment -->
            <div class="section">
                <h3 class="section-title">Product-Level Payment</h3>
                <select class="product-payment-select">
                    <option value="">Payment Option</option>
                    <option value="full">Full Payment</option>
                    <option value="partial">Partial Payment</option>
                    <option value="installment">Installment</option>
                </select>
            </div>

            <!-- Order Summary -->
            <div class="section">
                <div class="order-summary">
                    <div class="summary-header">
                        <span class="summary-title">Order Summary</span>
                        <span class="summary-amount">€42.99</span>
                    </div>
                    <label class="checkbox-label">
                        <input type="checkbox" checked>
                        <span>Send payment notification</span>
                    </label>
                </div>
            </div>

            <!-- Pay Now Button -->
            <div class="section">
                <button class="pay-now-btn">Pay Now</button>
            </div>

            <!-- Navigation Buttons -->
            <div class="navigation-buttons">
                <button class="nav-btn back-btn">← Back</button>
                <button class="nav-btn next-btn">Next</button>
            </div>
        </div>
    </div>
</body>
</html>
