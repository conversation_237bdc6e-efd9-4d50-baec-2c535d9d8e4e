/* Clean horizontal step indicator */
.step-indicator {
  width: 100%;
}

.step-progress {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: var(--spacing-6);
}

.step-info {
  flex-shrink: 0;
}

.current-step-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  margin: 0 0 var(--spacing-1) 0;
}

.step-counter {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  margin: 0;
}

.steps-track {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  flex: 1;
  justify-content: center;
}

.step-dot {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--surface);
  border: 2px solid var(--border);
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-bold);
  transition: all var(--transition-normal);
  flex-shrink: 0;
}

.step-dot.clickable {
  cursor: pointer;
}

.step-dot.clickable:hover {
  border-color: var(--text-primary);
  color: var(--text-primary);
}

.step-dot.current {
  background: var(--text-primary);
  border-color: var(--text-primary);
  color: var(--surface);
}

.step-dot.completed {
  background: var(--text-primary);
  border-color: var(--text-primary);
  color: var(--surface);
}

.step-line {
  height: 2px;
  background: var(--border);
  flex: 1;
  min-width: 40px;
  transition: background-color var(--transition-normal);
}

.step-line.completed {
  background: var(--text-primary);
}

/* Responsive design */
@media (max-width: 768px) {
  .step-progress {
    flex-direction: column;
    gap: var(--spacing-4);
    align-items: flex-start;
  }
  
  .step-info {
    width: 100%;
  }
  
  .steps-track {
    width: 100%;
    justify-content: flex-start;
  }
  
  .step-dot {
    width: 28px;
    height: 28px;
    font-size: var(--font-size-xs);
  }
  
  .step-line {
    min-width: 20px;
  }
}
