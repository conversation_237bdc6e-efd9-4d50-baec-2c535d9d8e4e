import{validateFieldsNatively as s,toNestErrors as e}from"@hookform/resolvers";import{validate as o}from"@typeschema/main";import{appendErrors as t}from"react-hook-form";const a=(s,e)=>{const o={};for(;s.length;){const a=s[0];if(!a.path)continue;const r=a.path.join(".");if(o[r]||(o[r]={message:a.message,type:""}),e){const s=o[r].types,i=s&&s[""];o[r]=t(r,e,o,"",i?[].concat(i,a.message):a.message)}s.shift()}return o},r=(t,r,i={})=>async(r,n,c)=>{const m=await o(t,r);return c.shouldUseNativeValidation&&s({},c),m.success?{errors:{},values:i.raw?r:m.data}:{values:{},errors:e(a(m.issues,!c.shouldUseNativeValidation&&"all"===c.criteriaMode),c)}};export{r as typeschemaResolver};
//# sourceMappingURL=typeschema.modern.mjs.map
