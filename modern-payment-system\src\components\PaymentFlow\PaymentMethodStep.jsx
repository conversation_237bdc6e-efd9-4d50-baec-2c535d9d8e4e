import React, { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  CreditCard, 
  Smartphone, 
  Wallet, 
  Truck, 
  Clock, 
  Star,
  Plus,
  Trash2,
  Shield,
  Zap
} from 'lucide-react'
import './PaymentMethodStep.css'

const PaymentMethodStep = ({ paymentData, onUpdate }) => {
  const [showAddForm, setShowAddForm] = useState(false)
  const [selectedNewMethod, setSelectedNewMethod] = useState('card')

  const paymentMethods = [
    {
      id: 'upi',
      name: 'UPI Payment',
      description: 'Pay instantly using UPI apps',
      icon: <Smartphone size={24} />,
      processingTime: 'Instant',
      fees: 'Free',
      popularity: 95,
      recommended: true,
      features: ['Instant transfer', 'No fees', 'Secure'],
      color: '#4CAF50'
    },
    {
      id: 'card',
      name: 'Credit/Debit Card',
      description: 'Visa, Mastercard, RuPay accepted',
      icon: <CreditCard size={24} />,
      processingTime: '2-3 mins',
      fees: '2.5%',
      popularity: 88,
      recommended: false,
      features: ['EMI available', 'Reward points', 'Secure'],
      color: '#2196F3'
    },
    {
      id: 'wallet',
      name: 'Digital Wallet',
      description: 'PayTM, PhonePe, Google Pay',
      icon: <Wallet size={24} />,
      processingTime: 'Instant',
      fees: 'Free',
      popularity: 82,
      recommended: false,
      features: ['Cashback offers', 'Quick payment', 'Secure'],
      color: '#FF9800'
    },
    {
      id: 'cod',
      name: 'Cash on Delivery',
      description: 'Pay when you receive the order',
      icon: <Truck size={24} />,
      processingTime: 'On delivery',
      fees: '₹50',
      popularity: 65,
      recommended: false,
      features: ['Pay on delivery', 'No advance payment', 'Flexible'],
      color: '#9C27B0'
    },
    {
      id: 'bnpl',
      name: 'Buy Now, Pay Later',
      description: 'Split payments into EMIs',
      icon: <Clock size={24} />,
      processingTime: 'Instant',
      fees: 'Varies',
      popularity: 45,
      recommended: false,
      features: ['No interest EMI', 'Flexible tenure', 'Quick approval'],
      color: '#E91E63'
    }
  ]

  const handleMethodSelect = (methodId) => {
    onUpdate({ selectedMethod: methodId })
  }

  const handleSavedMethodSelect = (method) => {
    onUpdate({ selectedMethod: `saved-${method.id}` })
  }

  const handleRemoveSavedMethod = (methodId) => {
    const updatedMethods = paymentData.savedMethods.filter(m => m.id !== methodId)
    onUpdate({ savedMethods: updatedMethods })
  }

  const getSavedMethodDisplay = (method) => {
    switch (method.type) {
      case 'card':
        return `${method.brand} •••• ${method.last4}`
      case 'upi':
        return method.vpa
      default:
        return 'Unknown Method'
    }
  }

  const getSavedMethodIcon = (type) => {
    switch (type) {
      case 'card':
        return <CreditCard size={20} />
      case 'upi':
        return <Smartphone size={20} />
      default:
        return <CreditCard size={20} />
    }
  }

  return (
    <div className="payment-method-step">
      <div className="step-header">
        <h2 className="step-title">Choose Payment Method</h2>
        <p className="step-description">
          Select your preferred way to pay securely and conveniently
        </p>
      </div>

      {/* Saved Payment Methods */}
      {paymentData.savedMethods.length > 0 && (
        <div className="saved-methods-section">
          <h3 className="section-title">
            <Shield size={20} />
            Saved Payment Methods
          </h3>
          
          <div className="saved-methods-grid">
            {paymentData.savedMethods.map((method) => (
              <motion.div
                key={method.id}
                className={`saved-method-card ${paymentData.selectedMethod === `saved-${method.id}` ? 'selected' : ''} ${method.isDefault ? 'default' : ''}`}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                onClick={() => handleSavedMethodSelect(method)}
              >
                {method.isDefault && (
                  <div className="default-badge">
                    <Star size={12} />
                    <span>Default</span>
                  </div>
                )}
                
                <div className="method-header">
                  <div className="method-icon">
                    {getSavedMethodIcon(method.type)}
                  </div>
                  <div className="method-details">
                    <h4 className="method-name">{getSavedMethodDisplay(method)}</h4>
                    <p className="method-type">{method.type.toUpperCase()}</p>
                  </div>
                </div>
                
                <button
                  className="remove-btn"
                  onClick={(e) => {
                    e.stopPropagation()
                    handleRemoveSavedMethod(method.id)
                  }}
                >
                  <Trash2 size={16} />
                </button>
                
                {paymentData.selectedMethod === `saved-${method.id}` && (
                  <motion.div
                    className="selection-indicator"
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    transition={{ type: "spring", stiffness: 500, damping: 30 }}
                  >
                    ✓
                  </motion.div>
                )}
              </motion.div>
            ))}
          </div>
        </div>
      )}

      {/* New Payment Methods */}
      <div className="new-methods-section">
        <h3 className="section-title">
          <Plus size={20} />
          Payment Options
        </h3>
        
        <div className="payment-methods-grid">
          {paymentMethods.map((method) => (
            <motion.div
              key={method.id}
              className={`payment-method-card ${paymentData.selectedMethod === method.id ? 'selected' : ''} ${method.recommended ? 'recommended' : ''}`}
              whileHover={{ scale: 1.02, y: -2 }}
              whileTap={{ scale: 0.98 }}
              onClick={() => handleMethodSelect(method.id)}
              style={{ '--method-color': method.color }}
            >
              {method.recommended && (
                <div className="recommended-badge">
                  <Star size={12} />
                  <span>Recommended</span>
                </div>
              )}

              <div className="method-header">
                <div className="method-icon" style={{ backgroundColor: `${method.color}15`, color: method.color }}>
                  {method.icon}
                </div>
                <div className="method-info">
                  <h4 className="method-name">{method.name}</h4>
                  <p className="method-description">{method.description}</p>
                </div>
              </div>

              <div className="method-details">
                <div className="detail-item">
                  <Zap size={14} />
                  <span>Processing: {method.processingTime}</span>
                </div>
                <div className="detail-item">
                  <Shield size={14} />
                  <span>Fees: {method.fees}</span>
                </div>
              </div>

              <div className="method-features">
                {method.features.map((feature, index) => (
                  <span key={index} className="feature-tag">
                    {feature}
                  </span>
                ))}
              </div>

              <div className="popularity-section">
                <div className="popularity-bar">
                  <div className="popularity-track">
                    <motion.div 
                      className="popularity-fill"
                      initial={{ width: 0 }}
                      animate={{ width: `${method.popularity}%` }}
                      transition={{ duration: 1, delay: 0.2 }}
                      style={{ backgroundColor: method.color }}
                    />
                  </div>
                </div>
                <span className="popularity-text">{method.popularity}% users prefer this</span>
              </div>

              {paymentData.selectedMethod === method.id && (
                <motion.div
                  className="selection-indicator"
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  transition={{ type: "spring", stiffness: 500, damping: 30 }}
                >
                  ✓
                </motion.div>
              )}
            </motion.div>
          ))}
        </div>
      </div>

      {/* Selected Method Summary */}
      {paymentData.selectedMethod && (
        <motion.div
          className="selected-summary"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
        >
          <div className="summary-header">
            <Shield size={20} />
            <h4>Selected Payment Method</h4>
          </div>
          
          <div className="summary-content">
            {(() => {
              if (paymentData.selectedMethod.startsWith('saved-')) {
                const methodId = parseInt(paymentData.selectedMethod.replace('saved-', ''))
                const method = paymentData.savedMethods.find(m => m.id === methodId)
                return method ? (
                  <div className="summary-details">
                    <div className="summary-icon">
                      {getSavedMethodIcon(method.type)}
                    </div>
                    <div className="summary-text">
                      <h5>{getSavedMethodDisplay(method)}</h5>
                      <p>Saved {method.type} method</p>
                    </div>
                    <div className="summary-badge">
                      <span>Secure & Fast</span>
                    </div>
                  </div>
                ) : null
              } else {
                const method = paymentMethods.find(m => m.id === paymentData.selectedMethod)
                return method ? (
                  <div className="summary-details">
                    <div className="summary-icon" style={{ backgroundColor: `${method.color}15`, color: method.color }}>
                      {method.icon}
                    </div>
                    <div className="summary-text">
                      <h5>{method.name}</h5>
                      <p>{method.description}</p>
                    </div>
                    <div className="summary-stats">
                      <span>Processing: {method.processingTime}</span>
                      <span>Fees: {method.fees}</span>
                    </div>
                  </div>
                ) : null
              }
            })()}
          </div>
        </motion.div>
      )}
    </div>
  )
}

export default PaymentMethodStep
