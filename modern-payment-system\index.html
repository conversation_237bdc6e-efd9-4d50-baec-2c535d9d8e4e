<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Modern Payment System</title>
  
  <!-- Preconnect to external resources -->
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  
  <!-- Google Fonts -->
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
  
  <!-- Material Icons -->
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
  
  <!-- Favicon -->
  <link rel="icon" type="image/svg+xml" href="/payment-icon.svg" />
  
  <!-- Meta tags for SEO and social sharing -->
  <meta name="description" content="Modern, secure payment interface with advanced UX features">
  <meta name="keywords" content="payment, secure, modern, UX, React">
  <meta name="author" content="Payment System Team">
  
  <!-- Open Graph tags -->
  <meta property="og:title" content="Modern Payment System">
  <meta property="og:description" content="Advanced payment interface with smooth user experience">
  <meta property="og:type" content="website">
  
  <!-- Theme color for mobile browsers -->
  <meta name="theme-color" content="#2563eb">
  
  <!-- Prevent FOUC (Flash of Unstyled Content) -->
  <style>
    body {
      margin: 0;
      font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      min-height: 100vh;
    }
    
    #root {
      min-height: 100vh;
    }
    
    /* Loading spinner */
    .loading-spinner {
      position: fixed;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 40px;
      height: 40px;
      border: 4px solid rgba(255, 255, 255, 0.3);
      border-top: 4px solid #ffffff;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      z-index: 9999;
    }
    
    @keyframes spin {
      0% { transform: translate(-50%, -50%) rotate(0deg); }
      100% { transform: translate(-50%, -50%) rotate(360deg); }
    }
    
    /* Hide loading spinner when React loads */
    .loaded .loading-spinner {
      display: none;
    }
  </style>
</head>
<body>
  <div id="root">
    <!-- Loading spinner shown until React loads -->
    <div class="loading-spinner"></div>
  </div>
  
  <script type="module" src="/src/main.jsx"></script>
  
  <!-- Mark as loaded when React takes over -->
  <script>
    window.addEventListener('load', () => {
      setTimeout(() => {
        document.body.classList.add('loaded');
      }, 100);
    });
  </script>
</body>
</html>
