{"version": 3, "file": "index.umd.js", "sources": ["../src/utils/isCheckBoxInput.ts", "../src/utils/isDateObject.ts", "../src/utils/isNullOrUndefined.ts", "../src/utils/isObject.ts", "../src/logic/getEventValue.ts", "../src/logic/isNameInFieldArray.ts", "../src/logic/getNodeParentName.ts", "../src/utils/isWeb.ts", "../src/utils/cloneObject.ts", "../src/utils/isPlainObject.ts", "../src/utils/compact.ts", "../src/utils/isUndefined.ts", "../src/utils/get.ts", "../src/utils/isBoolean.ts", "../src/utils/isKey.ts", "../src/utils/stringToPath.ts", "../src/utils/set.ts", "../src/constants.ts", "../src/useFormContext.tsx", "../src/logic/getProxyFormState.ts", "../src/useIsomorphicLayoutEffect.ts", "../src/useFormState.ts", "../src/utils/isString.ts", "../src/logic/generateWatchOutput.ts", "../src/useWatch.ts", "../src/useController.ts", "../src/controller.tsx", "../src/utils/flatten.ts", "../src/form.tsx", "../src/logic/appendErrors.ts", "../src/utils/convertToArrayPayload.ts", "../src/utils/createSubject.ts", "../src/utils/isPrimitive.ts", "../src/utils/deepEqual.ts", "../src/utils/isEmptyObject.ts", "../src/utils/isFileInput.ts", "../src/utils/isFunction.ts", "../src/utils/isHTMLElement.ts", "../src/utils/isMultipleSelect.ts", "../src/utils/isRadioInput.ts", "../src/utils/live.ts", "../src/utils/unset.ts", "../src/utils/objectHasFunction.ts", "../src/logic/getDirtyFields.ts", "../src/logic/getCheckboxValue.ts", "../src/logic/getFieldValueAs.ts", "../src/logic/getRadioValue.ts", "../src/logic/getFieldValue.ts", "../src/logic/getResolverOptions.ts", "../src/utils/isRegex.ts", "../src/logic/getRuleValue.ts", "../src/logic/getValidationModes.ts", "../src/logic/hasPromiseValidation.ts", "../src/logic/isWatched.ts", "../src/logic/iterateFieldsByAction.ts", "../src/logic/schemaErrorLookup.ts", "../src/logic/shouldRenderFormState.ts", "../src/logic/updateFieldArrayRootError.ts", "../src/utils/isMessage.ts", "../src/logic/getValidateError.ts", "../src/logic/getValueAndMessage.ts", "../src/logic/validateField.ts", "../src/logic/createFormControl.ts", "../src/logic/hasValidation.ts", "../src/logic/skipValidation.ts", "../src/logic/shouldSubscribeByName.ts", "../src/utils/isRadioOrCheckbox.ts", "../src/logic/unsetEmptyArray.ts", "../src/logic/generateId.ts", "../src/logic/getFocusFieldName.ts", "../src/utils/append.ts", "../src/utils/fillEmptyArray.ts", "../src/utils/insert.ts", "../src/utils/move.ts", "../src/utils/prepend.ts", "../src/utils/remove.ts", "../src/utils/swap.ts", "../src/utils/update.ts", "../src/useFieldArray.ts", "../src/useForm.ts"], "sourcesContent": ["import { FieldElement } from '../types';\n\nexport default (element: FieldElement): element is HTMLInputElement =>\n  element.type === 'checkbox';\n", "export default (value: unknown): value is Date => value instanceof Date;\n", "export default (value: unknown): value is null | undefined => value == null;\n", "import isDateObject from './isDateObject';\nimport isNullOrUndefined from './isNullOrUndefined';\n\nexport const isObjectType = (value: unknown): value is object =>\n  typeof value === 'object';\n\nexport default <T extends object>(value: unknown): value is T =>\n  !isNullOrUndefined(value) &&\n  !Array.isArray(value) &&\n  isObjectType(value) &&\n  !isDateObject(value);\n", "import isCheckBoxInput from '../utils/isCheckBoxInput';\nimport isObject from '../utils/isObject';\n\ntype Event = { target: any };\n\nexport default (event: unknown) =>\n  isObject(event) && (event as Event).target\n    ? isCheckBoxInput((event as Event).target)\n      ? (event as Event).target.checked\n      : (event as Event).target.value\n    : event;\n", "import { InternalFieldName } from '../types';\n\nimport getNodeParentName from './getNodeParentName';\n\nexport default (names: Set<InternalFieldName>, name: InternalFieldName) =>\n  names.has(getNodeParentName(name));\n", "export default (name: string) =>\n  name.substring(0, name.search(/\\.\\d+(\\.|$)/)) || name;\n", "export default typeof window !== 'undefined' &&\n  typeof window.HTMLElement !== 'undefined' &&\n  typeof document !== 'undefined';\n", "import isObject from './isObject';\nimport isPlainObject from './isPlainObject';\nimport isWeb from './isWeb';\n\nexport default function cloneObject<T>(data: T): T {\n  let copy: any;\n  const isArray = Array.isArray(data);\n  const isFileListInstance =\n    typeof FileList !== 'undefined' ? data instanceof FileList : false;\n\n  if (data instanceof Date) {\n    copy = new Date(data);\n  } else if (data instanceof Set) {\n    copy = new Set(data);\n  } else if (\n    !(isWeb && (data instanceof Blob || isFileListInstance)) &&\n    (isArray || isObject(data))\n  ) {\n    copy = isArray ? [] : {};\n\n    if (!isArray && !isPlainObject(data)) {\n      copy = data;\n    } else {\n      for (const key in data) {\n        if (data.hasOwnProperty(key)) {\n          copy[key] = cloneObject(data[key]);\n        }\n      }\n    }\n  } else {\n    return data;\n  }\n\n  return copy;\n}\n", "import isObject from './isObject';\n\nexport default (tempObject: object) => {\n  const prototypeCopy =\n    tempObject.constructor && tempObject.constructor.prototype;\n\n  return (\n    isObject(prototypeCopy) && prototypeCopy.hasOwnProperty('isPrototypeOf')\n  );\n};\n", "export default <TValue>(value: TValue[]) =>\n  Array.isArray(value) ? value.filter(Boolean) : [];\n", "export default (val: unknown): val is undefined => val === undefined;\n", "import compact from './compact';\nimport isNullOrUndefined from './isNullOrUndefined';\nimport isObject from './isObject';\nimport isUndefined from './isUndefined';\n\nexport default <T>(\n  object: T,\n  path?: string | null,\n  defaultValue?: unknown,\n): any => {\n  if (!path || !isObject(object)) {\n    return defaultValue;\n  }\n\n  const result = compact(path.split(/[,[\\].]+?/)).reduce(\n    (result, key) =>\n      isNullOrUndefined(result) ? result : result[key as keyof {}],\n    object,\n  );\n\n  return isUndefined(result) || result === object\n    ? isUndefined(object[path as keyof T])\n      ? defaultValue\n      : object[path as keyof T]\n    : result;\n};\n", "export default (value: unknown): value is boolean => typeof value === 'boolean';\n", "export default (value: string) => /^\\w*$/.test(value);\n", "import compact from './compact';\n\nexport default (input: string): string[] =>\n  compact(input.replace(/[\"|']|\\]/g, '').split(/\\.|\\[/));\n", "import { FieldPath, FieldValues } from '../types';\n\nimport isKey from './isKey';\nimport isObject from './isObject';\nimport stringToPath from './stringToPath';\n\nexport default (\n  object: FieldValues,\n  path: FieldPath<FieldValues>,\n  value?: unknown,\n) => {\n  let index = -1;\n  const tempPath = isKey(path) ? [path] : stringToPath(path);\n  const length = tempPath.length;\n  const lastIndex = length - 1;\n\n  while (++index < length) {\n    const key = tempPath[index];\n    let newValue = value;\n\n    if (index !== lastIndex) {\n      const objValue = object[key];\n      newValue =\n        isObject(objValue) || Array.isArray(objValue)\n          ? objValue\n          : !isNaN(+tempPath[index + 1])\n            ? []\n            : {};\n    }\n\n    if (key === '__proto__' || key === 'constructor' || key === 'prototype') {\n      return;\n    }\n\n    object[key] = newValue;\n    object = object[key];\n  }\n};\n", "export const EVENTS = {\n  BLUR: 'blur',\n  FOCUS_OUT: 'focusout',\n  CHANGE: 'change',\n} as const;\n\nexport const VALIDATION_MODE = {\n  onBlur: 'onBlur',\n  onChange: 'onChange',\n  onSubmit: 'onSubmit',\n  onTouched: 'onTouched',\n  all: 'all',\n} as const;\n\nexport const INPUT_VALIDATION_RULES = {\n  max: 'max',\n  min: 'min',\n  maxLength: 'maxLength',\n  minLength: 'minLength',\n  pattern: 'pattern',\n  required: 'required',\n  validate: 'validate',\n} as const;\n", "import React from 'react';\n\nimport { FieldValues, FormProviderProps, UseFormReturn } from './types';\n\nconst HookFormContext = React.createContext<UseFormReturn | null>(null);\n\n/**\n * This custom hook allows you to access the form context. useFormContext is intended to be used in deeply nested structures, where it would become inconvenient to pass the context as a prop. To be used with {@link FormProvider}.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/useformcontext) • [Demo](https://codesandbox.io/s/react-hook-form-v7-form-context-ytudi)\n *\n * @returns return all useForm methods\n *\n * @example\n * ```tsx\n * function App() {\n *   const methods = useForm();\n *   const onSubmit = data => console.log(data);\n *\n *   return (\n *     <FormProvider {...methods} >\n *       <form onSubmit={methods.handleSubmit(onSubmit)}>\n *         <NestedInput />\n *         <input type=\"submit\" />\n *       </form>\n *     </FormProvider>\n *   );\n * }\n *\n *  function NestedInput() {\n *   const { register } = useFormContext(); // retrieve all hook methods\n *   return <input {...register(\"test\")} />;\n * }\n * ```\n */\nexport const useFormContext = <\n  TFieldValues extends FieldValues,\n  TContext = any,\n  TTransformedValues = TFieldValues,\n>(): UseFormReturn<TFieldValues, TContext, TTransformedValues> =>\n  React.useContext(HookFormContext) as UseFormReturn<\n    TFieldValues,\n    TContext,\n    TTransformedValues\n  >;\n\n/**\n * A provider component that propagates the `useForm` methods to all children components via [React Context](https://reactjs.org/docs/context.html) API. To be used with {@link useFormContext}.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/useformcontext) • [Demo](https://codesandbox.io/s/react-hook-form-v7-form-context-ytudi)\n *\n * @param props - all useForm methods\n *\n * @example\n * ```tsx\n * function App() {\n *   const methods = useForm();\n *   const onSubmit = data => console.log(data);\n *\n *   return (\n *     <FormProvider {...methods} >\n *       <form onSubmit={methods.handleSubmit(onSubmit)}>\n *         <NestedInput />\n *         <input type=\"submit\" />\n *       </form>\n *     </FormProvider>\n *   );\n * }\n *\n *  function NestedInput() {\n *   const { register } = useFormContext(); // retrieve all hook methods\n *   return <input {...register(\"test\")} />;\n * }\n * ```\n */\nexport const FormProvider = <\n  TFieldValues extends FieldValues,\n  TContext = any,\n  TTransformedValues = TFieldValues,\n>(\n  props: FormProviderProps<TFieldValues, TContext, TTransformedValues>,\n) => {\n  const { children, ...data } = props;\n  return (\n    <HookFormContext.Provider value={data as unknown as UseFormReturn}>\n      {children}\n    </HookFormContext.Provider>\n  );\n};\n", "import { VALIDATION_MODE } from '../constants';\nimport { Control, FieldValues, FormState, ReadFormState } from '../types';\n\nexport default <\n  TFieldValues extends FieldValues,\n  TContext = any,\n  TTransformedValues = TFieldValues,\n>(\n  formState: FormState<TFieldValues>,\n  control: Control<TFieldValues, TContext, TTransformedValues>,\n  localProxyFormState?: ReadFormState,\n  isRoot = true,\n) => {\n  const result = {\n    defaultValues: control._defaultValues,\n  } as typeof formState;\n\n  for (const key in formState) {\n    Object.defineProperty(result, key, {\n      get: () => {\n        const _key = key as keyof FormState<TFieldValues> & keyof ReadFormState;\n\n        if (control._proxyFormState[_key] !== VALIDATION_MODE.all) {\n          control._proxyFormState[_key] = !isRoot || VALIDATION_MODE.all;\n        }\n\n        localProxyFormState && (localProxyFormState[_key] = true);\n        return formState[_key];\n      },\n    });\n  }\n\n  return result;\n};\n", "import * as React from 'react';\n\nexport const useIsomorphicLayoutEffect =\n  typeof window !== 'undefined' ? React.useLayoutEffect : React.useEffect;\n", "import React from 'react';\n\nimport getProxyFormState from './logic/getProxyFormState';\nimport { FieldValues, UseFormStateProps, UseFormStateReturn } from './types';\nimport { useFormContext } from './useFormContext';\nimport { useIsomorphicLayoutEffect } from './useIsomorphicLayoutEffect';\n\n/**\n * This custom hook allows you to subscribe to each form state, and isolate the re-render at the custom hook level. It has its scope in terms of form state subscription, so it would not affect other useFormState and useForm. Using this hook can reduce the re-render impact on large and complex form application.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/useformstate) • [Demo](https://codesandbox.io/s/useformstate-75xly)\n *\n * @param props - include options on specify fields to subscribe. {@link UseFormStateReturn}\n *\n * @example\n * ```tsx\n * function App() {\n *   const { register, handleSubmit, control } = useForm({\n *     defaultValues: {\n *     firstName: \"firstName\"\n *   }});\n *   const { dirtyFields } = useFormState({\n *     control\n *   });\n *   const onSubmit = (data) => console.log(data);\n *\n *   return (\n *     <form onSubmit={handleSubmit(onSubmit)}>\n *       <input {...register(\"firstName\")} placeholder=\"First Name\" />\n *       {dirtyFields.firstName && <p>Field is dirty.</p>}\n *       <input type=\"submit\" />\n *     </form>\n *   );\n * }\n * ```\n */\nexport function useFormState<\n  TFieldValues extends FieldValues = FieldValues,\n  TTransformedValues = TFieldValues,\n>(\n  props?: UseFormStateProps<TFieldValues, TTransformedValues>,\n): UseFormStateReturn<TFieldValues> {\n  const methods = useFormContext<TFieldValues, any, TTransformedValues>();\n  const { control = methods.control, disabled, name, exact } = props || {};\n  const [formState, updateFormState] = React.useState(control._formState);\n  const _localProxyFormState = React.useRef({\n    isDirty: false,\n    isLoading: false,\n    dirtyFields: false,\n    touchedFields: false,\n    validatingFields: false,\n    isValidating: false,\n    isValid: false,\n    errors: false,\n  });\n\n  useIsomorphicLayoutEffect(\n    () =>\n      control._subscribe({\n        name,\n        formState: _localProxyFormState.current,\n        exact,\n        callback: (formState) => {\n          !disabled &&\n            updateFormState({\n              ...control._formState,\n              ...formState,\n            });\n        },\n      }),\n    [name, disabled, exact],\n  );\n\n  React.useEffect(() => {\n    _localProxyFormState.current.isValid && control._setValid(true);\n  }, [control]);\n\n  return React.useMemo(\n    () =>\n      getProxyFormState(\n        formState,\n        control,\n        _localProxyFormState.current,\n        false,\n      ),\n    [formState, control],\n  );\n}\n", "export default (value: unknown): value is string => typeof value === 'string';\n", "import { DeepPartial, FieldValues, Names } from '../types';\nimport get from '../utils/get';\nimport isString from '../utils/isString';\n\nexport default <T>(\n  names: string | string[] | undefined,\n  _names: Names,\n  formValues?: FieldValues,\n  isGlobal?: boolean,\n  defaultValue?: DeepPartial<T> | unknown,\n) => {\n  if (isString(names)) {\n    isGlobal && _names.watch.add(names);\n    return get(formValues, names, defaultValue);\n  }\n\n  if (Array.isArray(names)) {\n    return names.map(\n      (fieldName) => (\n        isGlobal && _names.watch.add(fieldName), get(formValues, fieldName)\n      ),\n    );\n  }\n\n  isGlobal && (_names.watchAll = true);\n\n  return formValues;\n};\n", "import React from 'react';\n\nimport generateWatchOutput from './logic/generateWatchOutput';\nimport {\n  Control,\n  DeepPartialSkipArrayKey,\n  FieldPath,\n  FieldPathValue,\n  FieldPathValues,\n  FieldValues,\n  InternalFieldName,\n  UseWatchProps,\n} from './types';\nimport { useFormContext } from './useFormContext';\nimport { useIsomorphicLayoutEffect } from './useIsomorphicLayoutEffect';\n\n/**\n * Subscribe to the entire form values change and re-render at the hook level.\n *\n * @remarks\n *\n * [API](https://react-hook-form.com/docs/usewatch) • [Demo](https://codesandbox.io/s/react-hook-form-v7-ts-usewatch-h9i5e)\n *\n * @param props - defaultValue, disable subscription and match exact name.\n *\n * @example\n * ```tsx\n * const { control } = useForm();\n * const values = useWatch({\n *   control,\n *   defaultValue: {\n *     name: \"data\"\n *   },\n *   exact: false,\n * })\n * ```\n */\nexport function useWatch<\n  TFieldValues extends FieldValues = FieldValues,\n  TTransformedValues = TFieldValues,\n>(props: {\n  defaultValue?: DeepPartialSkipArrayKey<TFieldValues>;\n  control?: Control<TFieldValues, any, TTransformedValues>;\n  disabled?: boolean;\n  exact?: boolean;\n}): DeepPartialSkipArrayKey<TFieldValues>;\n/**\n * Custom hook to subscribe to field change and isolate re-rendering at the component level.\n *\n * @remarks\n *\n * [API](https://react-hook-form.com/docs/usewatch) • [Demo](https://codesandbox.io/s/react-hook-form-v7-ts-usewatch-h9i5e)\n *\n * @param props - defaultValue, disable subscription and match exact name.\n *\n * @example\n * ```tsx\n * const { control } = useForm();\n * const values = useWatch({\n *   control,\n *   name: \"fieldA\",\n *   defaultValue: \"default value\",\n *   exact: false,\n * })\n * ```\n */\nexport function useWatch<\n  TFieldValues extends FieldValues = FieldValues,\n  TFieldName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,\n  TTransformedValues = TFieldValues,\n>(props: {\n  name: TFieldName;\n  defaultValue?: FieldPathValue<TFieldValues, TFieldName>;\n  control?: Control<TFieldValues, any, TTransformedValues>;\n  disabled?: boolean;\n  exact?: boolean;\n}): FieldPathValue<TFieldValues, TFieldName>;\n/**\n * Custom hook to subscribe to field change and isolate re-rendering at the component level.\n *\n * @remarks\n *\n * [API](https://react-hook-form.com/docs/usewatch) • [Demo](https://codesandbox.io/s/react-hook-form-v7-ts-usewatch-h9i5e)\n *\n * @param props - defaultValue, disable subscription and match exact name.\n *\n * @example\n * ```tsx\n * const { control } = useForm();\n * const values = useWatch({\n *   control,\n *   name: [\"fieldA\", \"fieldB\"],\n *   defaultValue: {\n *     fieldA: \"data\",\n *     fieldB: \"data\"\n *   },\n *   exact: false,\n * })\n * ```\n */\nexport function useWatch<\n  TFieldValues extends FieldValues = FieldValues,\n  TFieldNames extends\n    readonly FieldPath<TFieldValues>[] = readonly FieldPath<TFieldValues>[],\n  TTransformedValues = TFieldValues,\n>(props: {\n  name: readonly [...TFieldNames];\n  defaultValue?: DeepPartialSkipArrayKey<TFieldValues>;\n  control?: Control<TFieldValues, any, TTransformedValues>;\n  disabled?: boolean;\n  exact?: boolean;\n}): FieldPathValues<TFieldValues, TFieldNames>;\n/**\n * Custom hook to subscribe to field change and isolate re-rendering at the component level.\n *\n * @remarks\n *\n * [API](https://react-hook-form.com/docs/usewatch) • [Demo](https://codesandbox.io/s/react-hook-form-v7-ts-usewatch-h9i5e)\n *\n * @example\n * ```tsx\n * // can skip passing down the control into useWatch if the form is wrapped with the FormProvider\n * const values = useWatch()\n * ```\n */\nexport function useWatch<\n  TFieldValues extends FieldValues = FieldValues,\n>(): DeepPartialSkipArrayKey<TFieldValues>;\n/**\n * Custom hook to subscribe to field change and isolate re-rendering at the component level.\n *\n * @remarks\n *\n * [API](https://react-hook-form.com/docs/usewatch) • [Demo](https://codesandbox.io/s/react-hook-form-v7-ts-usewatch-h9i5e)\n *\n * @example\n * ```tsx\n * const { control } = useForm();\n * const values = useWatch({\n *   name: \"fieldName\"\n *   control,\n * })\n * ```\n */\nexport function useWatch<TFieldValues extends FieldValues>(\n  props?: UseWatchProps<TFieldValues>,\n) {\n  const methods = useFormContext<TFieldValues>();\n  const {\n    control = methods.control,\n    name,\n    defaultValue,\n    disabled,\n    exact,\n  } = props || {};\n  const _defaultValue = React.useRef(defaultValue);\n  const [value, updateValue] = React.useState(\n    control._getWatch(\n      name as InternalFieldName,\n      _defaultValue.current as DeepPartialSkipArrayKey<TFieldValues>,\n    ),\n  );\n\n  useIsomorphicLayoutEffect(\n    () =>\n      control._subscribe({\n        name,\n        formState: {\n          values: true,\n        },\n        exact,\n        callback: (formState) =>\n          !disabled &&\n          updateValue(\n            generateWatchOutput(\n              name as InternalFieldName | InternalFieldName[],\n              control._names,\n              formState.values || control._formValues,\n              false,\n              _defaultValue.current,\n            ),\n          ),\n      }),\n    [name, control, disabled, exact],\n  );\n\n  React.useEffect(() => control._removeUnmounted());\n\n  return value;\n}\n", "import React from 'react';\n\nimport getEventValue from './logic/getEventValue';\nimport isNameInFieldArray from './logic/isNameInFieldArray';\nimport cloneObject from './utils/cloneObject';\nimport get from './utils/get';\nimport isBoolean from './utils/isBoolean';\nimport isUndefined from './utils/isUndefined';\nimport set from './utils/set';\nimport { EVENTS } from './constants';\nimport {\n  ControllerFieldState,\n  Field,\n  FieldPath,\n  FieldPathValue,\n  FieldValues,\n  InternalFieldName,\n  UseControllerProps,\n  UseControllerReturn,\n} from './types';\nimport { useFormContext } from './useFormContext';\nimport { useFormState } from './useFormState';\nimport { useWatch } from './useWatch';\n\n/**\n * Custom hook to work with controlled component, this function provide you with both form and field level state. Re-render is isolated at the hook level.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/usecontroller) • [Demo](https://codesandbox.io/s/usecontroller-0o8px)\n *\n * @param props - the path name to the form field value, and validation rules.\n *\n * @returns field properties, field and form state. {@link UseControllerReturn}\n *\n * @example\n * ```tsx\n * function Input(props) {\n *   const { field, fieldState, formState } = useController(props);\n *   return (\n *     <div>\n *       <input {...field} placeholder={props.name} />\n *       <p>{fieldState.isTouched && \"Touched\"}</p>\n *       <p>{formState.isSubmitted ? \"submitted\" : \"\"}</p>\n *     </div>\n *   );\n * }\n * ```\n */\nexport function useController<\n  TFieldValues extends FieldValues = FieldValues,\n  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,\n  TTransformedValues = TFieldValues,\n>(\n  props: UseControllerProps<TFieldValues, TName, TTransformedValues>,\n): UseControllerReturn<TFieldValues, TName> {\n  const methods = useFormContext<TFieldValues, any, TTransformedValues>();\n  const { name, disabled, control = methods.control, shouldUnregister } = props;\n  const isArrayField = isNameInFieldArray(control._names.array, name);\n  const value = useWatch({\n    control,\n    name,\n    defaultValue: get(\n      control._formValues,\n      name,\n      get(control._defaultValues, name, props.defaultValue),\n    ),\n    exact: true,\n  }) as FieldPathValue<TFieldValues, TName>;\n  const formState = useFormState({\n    control,\n    name,\n    exact: true,\n  });\n\n  const _props = React.useRef(props);\n  const _registerProps = React.useRef(\n    control.register(name, {\n      ...props.rules,\n      value,\n      ...(isBoolean(props.disabled) ? { disabled: props.disabled } : {}),\n    }),\n  );\n\n  const fieldState = React.useMemo(\n    () =>\n      Object.defineProperties(\n        {},\n        {\n          invalid: {\n            enumerable: true,\n            get: () => !!get(formState.errors, name),\n          },\n          isDirty: {\n            enumerable: true,\n            get: () => !!get(formState.dirtyFields, name),\n          },\n          isTouched: {\n            enumerable: true,\n            get: () => !!get(formState.touchedFields, name),\n          },\n          isValidating: {\n            enumerable: true,\n            get: () => !!get(formState.validatingFields, name),\n          },\n          error: {\n            enumerable: true,\n            get: () => get(formState.errors, name),\n          },\n        },\n      ) as ControllerFieldState,\n    [formState, name],\n  );\n\n  const onChange = React.useCallback(\n    (event: any) =>\n      _registerProps.current.onChange({\n        target: {\n          value: getEventValue(event),\n          name: name as InternalFieldName,\n        },\n        type: EVENTS.CHANGE,\n      }),\n    [name],\n  );\n\n  const onBlur = React.useCallback(\n    () =>\n      _registerProps.current.onBlur({\n        target: {\n          value: get(control._formValues, name),\n          name: name as InternalFieldName,\n        },\n        type: EVENTS.BLUR,\n      }),\n    [name, control._formValues],\n  );\n\n  const ref = React.useCallback(\n    (elm: any) => {\n      const field = get(control._fields, name);\n\n      if (field && elm) {\n        field._f.ref = {\n          focus: () => elm.focus && elm.focus(),\n          select: () => elm.select && elm.select(),\n          setCustomValidity: (message: string) =>\n            elm.setCustomValidity(message),\n          reportValidity: () => elm.reportValidity(),\n        };\n      }\n    },\n    [control._fields, name],\n  );\n\n  const field = React.useMemo(\n    () => ({\n      name,\n      value,\n      ...(isBoolean(disabled) || formState.disabled\n        ? { disabled: formState.disabled || disabled }\n        : {}),\n      onChange,\n      onBlur,\n      ref,\n    }),\n    [name, disabled, formState.disabled, onChange, onBlur, ref, value],\n  );\n\n  React.useEffect(() => {\n    const _shouldUnregisterField =\n      control._options.shouldUnregister || shouldUnregister;\n\n    control.register(name, {\n      ..._props.current.rules,\n      ...(isBoolean(_props.current.disabled)\n        ? { disabled: _props.current.disabled }\n        : {}),\n    });\n\n    const updateMounted = (name: InternalFieldName, value: boolean) => {\n      const field: Field = get(control._fields, name);\n\n      if (field && field._f) {\n        field._f.mount = value;\n      }\n    };\n\n    updateMounted(name, true);\n\n    if (_shouldUnregisterField) {\n      const value = cloneObject(get(control._options.defaultValues, name));\n      set(control._defaultValues, name, value);\n      if (isUndefined(get(control._formValues, name))) {\n        set(control._formValues, name, value);\n      }\n    }\n\n    !isArrayField && control.register(name);\n\n    return () => {\n      (\n        isArrayField\n          ? _shouldUnregisterField && !control._state.action\n          : _shouldUnregisterField\n      )\n        ? control.unregister(name)\n        : updateMounted(name, false);\n    };\n  }, [name, control, isArrayField, shouldUnregister]);\n\n  React.useEffect(() => {\n    control._setDisabledField({\n      disabled,\n      name,\n    });\n  }, [disabled, name, control]);\n\n  return React.useMemo(\n    () => ({\n      field,\n      formState,\n      fieldState,\n    }),\n    [field, formState, fieldState],\n  );\n}\n", "import { ControllerProps, FieldPath, FieldValues } from './types';\nimport { useController } from './useController';\n\n/**\n * Component based on `useController` hook to work with controlled component.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/usecontroller/controller) • [Demo](https://codesandbox.io/s/react-hook-form-v6-controller-ts-jwyzw) • [Video](https://www.youtube.com/watch?v=N2UNk_UCVyA)\n *\n * @param props - the path name to the form field value, and validation rules.\n *\n * @returns provide field handler functions, field and form state.\n *\n * @example\n * ```tsx\n * function App() {\n *   const { control } = useForm<FormValues>({\n *     defaultValues: {\n *       test: \"\"\n *     }\n *   });\n *\n *   return (\n *     <form>\n *       <Controller\n *         control={control}\n *         name=\"test\"\n *         render={({ field: { onChange, onBlur, value, ref }, formState, fieldState }) => (\n *           <>\n *             <input\n *               onChange={onChange} // send value to hook form\n *               onBlur={onBlur} // notify when input is touched\n *               value={value} // return updated value\n *               ref={ref} // set ref for focus management\n *             />\n *             <p>{formState.isSubmitted ? \"submitted\" : \"\"}</p>\n *             <p>{fieldState.isTouched ? \"touched\" : \"\"}</p>\n *           </>\n *         )}\n *       />\n *     </form>\n *   );\n * }\n * ```\n */\nconst Controller = <\n  TFieldValues extends FieldValues = FieldValues,\n  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,\n  TTransformedValues = TFieldValues,\n>(\n  props: ControllerProps<TFieldValues, TName, TTransformedValues>,\n) =>\n  props.render(useController<TFieldValues, TName, TTransformedValues>(props));\n\nexport { Controller };\n", "import { FieldValues } from '../types';\n\nimport { isObjectType } from './isObject';\n\nexport const flatten = (obj: FieldValues) => {\n  const output: FieldValues = {};\n\n  for (const key of Object.keys(obj)) {\n    if (isObjectType(obj[key]) && obj[key] !== null) {\n      const nested = flatten(obj[key]);\n\n      for (const nestedKey of Object.keys(nested)) {\n        output[`${key}.${nestedKey}`] = nested[nestedKey];\n      }\n    } else {\n      output[key] = obj[key];\n    }\n  }\n\n  return output;\n};\n", "import React from 'react';\n\nimport { flatten } from './utils/flatten';\nimport { FieldValues, FormProps } from './types';\nimport { useFormContext } from './useFormContext';\n\nconst POST_REQUEST = 'post';\n\n/**\n * Form component to manage submission.\n *\n * @param props - to setup submission detail. {@link FormProps}\n *\n * @returns form component or headless render prop.\n *\n * @example\n * ```tsx\n * function App() {\n *   const { control, formState: { errors } } = useForm();\n *\n *   return (\n *     <Form action=\"/api\" control={control}>\n *       <input {...register(\"name\")} />\n *       <p>{errors?.root?.server && 'Server error'}</p>\n *       <button>Submit</button>\n *     </Form>\n *   );\n * }\n * ```\n */\nfunction Form<\n  TFieldValues extends FieldValues,\n  TTransformedValues = TFieldValues,\n>(props: FormProps<TFieldValues, TTransformedValues>) {\n  const methods = useFormContext<TFieldValues, any, TTransformedValues>();\n  const [mounted, setMounted] = React.useState(false);\n  const {\n    control = methods.control,\n    onSubmit,\n    children,\n    action,\n    method = POST_REQUEST,\n    headers,\n    encType,\n    onError,\n    render,\n    onSuccess,\n    validateStatus,\n    ...rest\n  } = props;\n\n  const submit = async (event?: React.BaseSyntheticEvent) => {\n    let hasError = false;\n    let type = '';\n\n    await control.handleSubmit(async (data) => {\n      const formData = new FormData();\n      let formDataJson = '';\n\n      try {\n        formDataJson = JSON.stringify(data);\n      } catch {}\n\n      const flattenFormValues = flatten(control._formValues);\n\n      for (const key in flattenFormValues) {\n        formData.append(key, flattenFormValues[key]);\n      }\n\n      if (onSubmit) {\n        await onSubmit({\n          data,\n          event,\n          method,\n          formData,\n          formDataJson,\n        });\n      }\n\n      if (action) {\n        try {\n          const shouldStringifySubmissionData = [\n            headers && headers['Content-Type'],\n            encType,\n          ].some((value) => value && value.includes('json'));\n\n          const response = await fetch(String(action), {\n            method,\n            headers: {\n              ...headers,\n              ...(encType ? { 'Content-Type': encType } : {}),\n            },\n            body: shouldStringifySubmissionData ? formDataJson : formData,\n          });\n\n          if (\n            response &&\n            (validateStatus\n              ? !validateStatus(response.status)\n              : response.status < 200 || response.status >= 300)\n          ) {\n            hasError = true;\n            onError && onError({ response });\n            type = String(response.status);\n          } else {\n            onSuccess && onSuccess({ response });\n          }\n        } catch (error: unknown) {\n          hasError = true;\n          onError && onError({ error });\n        }\n      }\n    })(event);\n\n    if (hasError && props.control) {\n      props.control._subjects.state.next({\n        isSubmitSuccessful: false,\n      });\n      props.control.setError('root.server', {\n        type,\n      });\n    }\n  };\n\n  React.useEffect(() => {\n    setMounted(true);\n  }, []);\n\n  return render ? (\n    <>\n      {render({\n        submit,\n      })}\n    </>\n  ) : (\n    <form\n      noValidate={mounted}\n      action={action}\n      method={method}\n      encType={encType}\n      onSubmit={submit}\n      {...rest}\n    >\n      {children}\n    </form>\n  );\n}\n\nexport { Form };\n", "import {\n  InternalFieldErrors,\n  InternalFieldName,\n  ValidateResult,\n} from '../types';\n\nexport default (\n  name: InternalFieldName,\n  validateAllFieldCriteria: boolean,\n  errors: InternalFieldErrors,\n  type: string,\n  message: ValidateResult,\n) =>\n  validateAllFieldCriteria\n    ? {\n        ...errors[name],\n        types: {\n          ...(errors[name] && errors[name]!.types ? errors[name]!.types : {}),\n          [type]: message || true,\n        },\n      }\n    : {};\n", "export default <T>(value: T) => (Array.isArray(value) ? value : [value]);\n", "import { Noop } from '../types';\n\nexport type Observer<T> = {\n  next: (value: T) => void;\n};\n\nexport type Subscription = {\n  unsubscribe: Noop;\n};\n\nexport type Subject<T> = {\n  readonly observers: Observer<T>[];\n  subscribe: (value: Observer<T>) => Subscription;\n  unsubscribe: Noop;\n} & Observer<T>;\n\nexport default <T>(): Subject<T> => {\n  let _observers: Observer<T>[] = [];\n\n  const next = (value: T) => {\n    for (const observer of _observers) {\n      observer.next && observer.next(value);\n    }\n  };\n\n  const subscribe = (observer: Observer<T>): Subscription => {\n    _observers.push(observer);\n    return {\n      unsubscribe: () => {\n        _observers = _observers.filter((o) => o !== observer);\n      },\n    };\n  };\n\n  const unsubscribe = () => {\n    _observers = [];\n  };\n\n  return {\n    get observers() {\n      return _observers;\n    },\n    next,\n    subscribe,\n    unsubscribe,\n  };\n};\n", "import { Primitive } from '../types';\n\nimport isNullOrUndefined from './isNullOrUndefined';\nimport { isObjectType } from './isObject';\n\nexport default (value: unknown): value is Primitive =>\n  isNullOrUndefined(value) || !isObjectType(value);\n", "import isObject from '../utils/isObject';\n\nimport isDateObject from './isDateObject';\nimport isPrimitive from './isPrimitive';\n\nexport default function deepEqual(object1: any, object2: any) {\n  if (isPrimitive(object1) || isPrimitive(object2)) {\n    return object1 === object2;\n  }\n\n  if (isDateObject(object1) && isDateObject(object2)) {\n    return object1.getTime() === object2.getTime();\n  }\n\n  const keys1 = Object.keys(object1);\n  const keys2 = Object.keys(object2);\n\n  if (keys1.length !== keys2.length) {\n    return false;\n  }\n\n  for (const key of keys1) {\n    const val1 = object1[key];\n\n    if (!keys2.includes(key)) {\n      return false;\n    }\n\n    if (key !== 'ref') {\n      const val2 = object2[key];\n\n      if (\n        (isDateObject(val1) && isDateObject(val2)) ||\n        (isObject(val1) && isObject(val2)) ||\n        (Array.isArray(val1) && Array.isArray(val2))\n          ? !deepEqual(val1, val2)\n          : val1 !== val2\n      ) {\n        return false;\n      }\n    }\n  }\n\n  return true;\n}\n", "import { EmptyObject } from '../types';\n\nimport isObject from './isObject';\n\nexport default (value: unknown): value is EmptyObject =>\n  isObject(value) && !Object.keys(value).length;\n", "import { FieldElement } from '../types';\n\nexport default (element: FieldElement): element is HTMLInputElement =>\n  element.type === 'file';\n", "export default (value: unknown): value is Function =>\n  typeof value === 'function';\n", "import isWeb from './isWeb';\n\nexport default (value: unknown): value is HTMLElement => {\n  if (!isWeb) {\n    return false;\n  }\n\n  const owner = value ? ((value as HTMLElement).ownerDocument as Document) : 0;\n  return (\n    value instanceof\n    (owner && owner.defaultView ? owner.defaultView.HTMLElement : HTMLElement)\n  );\n};\n", "import { FieldElement } from '../types';\n\nexport default (element: FieldElement): element is HTMLSelectElement =>\n  element.type === `select-multiple`;\n", "import { FieldElement } from '../types';\n\nexport default (element: FieldElement): element is HTMLInputElement =>\n  element.type === 'radio';\n", "import { Ref } from '../types';\n\nimport isHTMLElement from './isHTMLElement';\n\nexport default (ref: Ref) => isHTMLElement(ref) && ref.isConnected;\n", "import isEmptyObject from './isEmptyObject';\nimport isKey from './isKey';\nimport isObject from './isObject';\nimport isUndefined from './isUndefined';\nimport stringToPath from './stringToPath';\n\nfunction baseGet(object: any, updatePath: (string | number)[]) {\n  const length = updatePath.slice(0, -1).length;\n  let index = 0;\n\n  while (index < length) {\n    object = isUndefined(object) ? index++ : object[updatePath[index++]];\n  }\n\n  return object;\n}\n\nfunction isEmptyArray(obj: unknown[]) {\n  for (const key in obj) {\n    if (obj.hasOwnProperty(key) && !isUndefined(obj[key])) {\n      return false;\n    }\n  }\n  return true;\n}\n\nexport default function unset(object: any, path: string | (string | number)[]) {\n  const paths = Array.isArray(path)\n    ? path\n    : isKey(path)\n      ? [path]\n      : stringToPath(path);\n\n  const childObject = paths.length === 1 ? object : baseGet(object, paths);\n\n  const index = paths.length - 1;\n  const key = paths[index];\n\n  if (childObject) {\n    delete childObject[key];\n  }\n\n  if (\n    index !== 0 &&\n    ((isObject(childObject) && isEmptyObject(childObject)) ||\n      (Array.isArray(childObject) && isEmptyArray(childObject)))\n  ) {\n    unset(object, paths.slice(0, -1));\n  }\n\n  return object;\n}\n", "import isFunction from './isFunction';\n\nexport default <T>(data: T): boolean => {\n  for (const key in data) {\n    if (isFunction(data[key])) {\n      return true;\n    }\n  }\n  return false;\n};\n", "import deepEqual from '../utils/deepEqual';\nimport isNullOrUndefined from '../utils/isNullOrUndefined';\nimport isObject from '../utils/isObject';\nimport isPrimitive from '../utils/isPrimitive';\nimport isUndefined from '../utils/isUndefined';\nimport objectHasFunction from '../utils/objectHasFunction';\n\nfunction markFieldsDirty<T>(data: T, fields: Record<string, any> = {}) {\n  const isParentNodeArray = Array.isArray(data);\n\n  if (isObject(data) || isParentNodeArray) {\n    for (const key in data) {\n      if (\n        Array.isArray(data[key]) ||\n        (isObject(data[key]) && !objectHasFunction(data[key]))\n      ) {\n        fields[key] = Array.isArray(data[key]) ? [] : {};\n        markFieldsDirty(data[key], fields[key]);\n      } else if (!isNullOrUndefined(data[key])) {\n        fields[key] = true;\n      }\n    }\n  }\n\n  return fields;\n}\n\nfunction getDirtyFieldsFromDefaultValues<T>(\n  data: T,\n  formValues: T,\n  dirtyFieldsFromValues: Record<\n    Extract<keyof T, string>,\n    ReturnType<typeof markFieldsDirty> | boolean\n  >,\n) {\n  const isParentNodeArray = Array.isArray(data);\n\n  if (isObject(data) || isParentNodeArray) {\n    for (const key in data) {\n      if (\n        Array.isArray(data[key]) ||\n        (isObject(data[key]) && !objectHasFunction(data[key]))\n      ) {\n        if (\n          isUndefined(formValues) ||\n          isPrimitive(dirtyFieldsFromValues[key])\n        ) {\n          dirtyFieldsFromValues[key] = Array.isArray(data[key])\n            ? markFieldsDirty(data[key], [])\n            : { ...markFieldsDirty(data[key]) };\n        } else {\n          getDirtyFieldsFromDefaultValues(\n            data[key],\n            isNullOrUndefined(formValues) ? {} : formValues[key],\n            dirtyFieldsFromValues[key],\n          );\n        }\n      } else {\n        dirtyFieldsFromValues[key] = !deepEqual(data[key], formValues[key]);\n      }\n    }\n  }\n\n  return dirtyFieldsFromValues;\n}\n\nexport default <T>(defaultValues: T, formValues: T) =>\n  getDirtyFieldsFromDefaultValues(\n    defaultValues,\n    formValues,\n    markFieldsDirty(formValues),\n  );\n", "import isUndefined from '../utils/isUndefined';\n\ntype CheckboxFieldResult = {\n  isValid: boolean;\n  value: string | string[] | boolean | undefined;\n};\n\nconst defaultResult: CheckboxFieldResult = {\n  value: false,\n  isValid: false,\n};\n\nconst validResult = { value: true, isValid: true };\n\nexport default (options?: HTMLInputElement[]): CheckboxFieldResult => {\n  if (Array.isArray(options)) {\n    if (options.length > 1) {\n      const values = options\n        .filter((option) => option && option.checked && !option.disabled)\n        .map((option) => option.value);\n      return { value: values, isValid: !!values.length };\n    }\n\n    return options[0].checked && !options[0].disabled\n      ? // @ts-expect-error expected to work in the browser\n        options[0].attributes && !isUndefined(options[0].attributes.value)\n        ? isUndefined(options[0].value) || options[0].value === ''\n          ? validResult\n          : { value: options[0].value, isValid: true }\n        : validResult\n      : defaultResult;\n  }\n\n  return defaultResult;\n};\n", "import { Field, NativeFieldValue } from '../types';\nimport isString from '../utils/isString';\nimport isUndefined from '../utils/isUndefined';\n\nexport default <T extends NativeFieldValue>(\n  value: T,\n  { valueAsNumber, valueAsDate, setValueAs }: Field['_f'],\n) =>\n  isUndefined(value)\n    ? value\n    : valueAsNumber\n      ? value === ''\n        ? NaN\n        : value\n          ? +value\n          : value\n      : valueAsDate && isString(value)\n        ? new Date(value)\n        : setValueAs\n          ? setValueAs(value)\n          : value;\n", "type RadioFieldResult = {\n  isValid: boolean;\n  value: number | string | null;\n};\n\nconst defaultReturn: RadioFieldResult = {\n  isValid: false,\n  value: null,\n};\n\nexport default (options?: HTMLInputElement[]): RadioFieldResult =>\n  Array.isArray(options)\n    ? options.reduce(\n        (previous, option): RadioFieldResult =>\n          option && option.checked && !option.disabled\n            ? {\n                isValid: true,\n                value: option.value,\n              }\n            : previous,\n        defaultReturn,\n      )\n    : defaultReturn;\n", "import { Field } from '../types';\nimport isCheckBox from '../utils/isCheckBoxInput';\nimport isFileInput from '../utils/isFileInput';\nimport isMultipleSelect from '../utils/isMultipleSelect';\nimport isRadioInput from '../utils/isRadioInput';\nimport isUndefined from '../utils/isUndefined';\n\nimport getCheckboxValue from './getCheckboxValue';\nimport getFieldValueAs from './getFieldValueAs';\nimport getRadioValue from './getRadioValue';\n\nexport default function getFieldValue(_f: Field['_f']) {\n  const ref = _f.ref;\n\n  if (isFileInput(ref)) {\n    return ref.files;\n  }\n\n  if (isRadioInput(ref)) {\n    return getRadioValue(_f.refs).value;\n  }\n\n  if (isMultipleSelect(ref)) {\n    return [...ref.selectedOptions].map(({ value }) => value);\n  }\n\n  if (isCheckBox(ref)) {\n    return getCheckboxValue(_f.refs).value;\n  }\n\n  return getFieldValueAs(isUndefined(ref.value) ? _f.ref.value : ref.value, _f);\n}\n", "import {\n  CriteriaMode,\n  Field,\n  FieldName,\n  FieldRefs,\n  FieldValues,\n  InternalFieldName,\n} from '../types';\nimport { get } from '../utils';\nimport set from '../utils/set';\n\nexport default <TFieldValues extends FieldValues>(\n  fieldsNames: Set<InternalFieldName> | InternalFieldName[],\n  _fields: FieldRefs,\n  criteriaMode?: CriteriaMode,\n  shouldUseNativeValidation?: boolean | undefined,\n) => {\n  const fields: Record<InternalFieldName, Field['_f']> = {};\n\n  for (const name of fieldsNames) {\n    const field: Field = get(_fields, name);\n\n    field && set(fields, name, field._f);\n  }\n\n  return {\n    criteriaMode,\n    names: [...fieldsNames] as FieldName<TFieldValues>[],\n    fields,\n    shouldUseNativeValidation,\n  };\n};\n", "export default (value: unknown): value is RegExp => value instanceof RegExp;\n", "import {\n  ValidationRule,\n  ValidationValue,\n  ValidationValueMessage,\n} from '../types';\nimport isObject from '../utils/isObject';\nimport isRegex from '../utils/isRegex';\nimport isUndefined from '../utils/isUndefined';\n\nexport default <T extends ValidationValue>(\n  rule?: ValidationRule<T> | ValidationValueMessage<T>,\n) =>\n  isUndefined(rule)\n    ? rule\n    : isRegex(rule)\n      ? rule.source\n      : isObject(rule)\n        ? isRegex(rule.value)\n          ? rule.value.source\n          : rule.value\n        : rule;\n", "import { VALIDATION_MODE } from '../constants';\nimport { Mode, ValidationModeFlags } from '../types';\n\nexport default (mode?: Mode): ValidationModeFlags => ({\n  isOnSubmit: !mode || mode === VALIDATION_MODE.onSubmit,\n  isOnBlur: mode === VALIDATION_MODE.onBlur,\n  isOnChange: mode === VALIDATION_MODE.onChange,\n  isOnAll: mode === VALIDATION_MODE.all,\n  isOnTouch: mode === VALIDATION_MODE.onTouched,\n});\n", "import { Field, Validate } from '../types';\nimport isFunction from '../utils/isFunction';\nimport isObject from '../utils/isObject';\n\nconst ASYNC_FUNCTION = 'AsyncFunction';\n\nexport default (fieldReference: Field['_f']) =>\n  !!fieldReference &&\n  !!fieldReference.validate &&\n  !!(\n    (isFunction(fieldReference.validate) &&\n      fieldReference.validate.constructor.name === ASYNC_FUNCTION) ||\n    (isObject(fieldReference.validate) &&\n      Object.values(fieldReference.validate).find(\n        (validateFunction: Validate<unknown, unknown>) =>\n          validateFunction.constructor.name === ASYNC_FUNCTION,\n      ))\n  );\n", "import { InternalFieldName, Names } from '../types';\n\nexport default (\n  name: InternalFieldName,\n  _names: Names,\n  isBlurEvent?: boolean,\n) =>\n  !isBlurEvent &&\n  (_names.watchAll ||\n    _names.watch.has(name) ||\n    [..._names.watch].some(\n      (watchName) =>\n        name.startsWith(watchName) &&\n        /^\\.\\w+/.test(name.slice(watchName.length)),\n    ));\n", "import { FieldRefs, InternalFieldName, Ref } from '../types';\nimport { get } from '../utils';\nimport isObject from '../utils/isObject';\n\nconst iterateFieldsByAction = (\n  fields: FieldRefs,\n  action: (ref: Ref, name: string) => 1 | undefined | void,\n  fieldsNames?: Set<InternalFieldName> | InternalFieldName[] | 0,\n  abortEarly?: boolean,\n) => {\n  for (const key of fieldsNames || Object.keys(fields)) {\n    const field = get(fields, key);\n\n    if (field) {\n      const { _f, ...currentField } = field;\n\n      if (_f) {\n        if (_f.refs && _f.refs[0] && action(_f.refs[0], key) && !abortEarly) {\n          return true;\n        } else if (_f.ref && action(_f.ref, _f.name) && !abortEarly) {\n          return true;\n        } else {\n          if (iterateFieldsByAction(currentField, action)) {\n            break;\n          }\n        }\n      } else if (isObject(currentField)) {\n        if (iterateFieldsByAction(currentField as FieldRefs, action)) {\n          break;\n        }\n      }\n    }\n  }\n  return;\n};\nexport default iterateFieldsByAction;\n", "import { FieldError, FieldErrors, FieldValues } from '../types';\nimport get from '../utils/get';\nimport isKey from '../utils/isKey';\n\nexport default function schemaErrorLookup<T extends FieldValues = FieldValues>(\n  errors: FieldErrors<T>,\n  _fields: FieldValues,\n  name: string,\n): {\n  error?: FieldError;\n  name: string;\n} {\n  const error = get(errors, name);\n\n  if (error || isKey(name)) {\n    return {\n      error,\n      name,\n    };\n  }\n\n  const names = name.split('.');\n\n  while (names.length) {\n    const fieldName = names.join('.');\n    const field = get(_fields, fieldName);\n    const foundError = get(errors, fieldName);\n\n    if (field && !Array.isArray(field) && name !== fieldName) {\n      return { name };\n    }\n\n    if (foundError && foundError.type) {\n      return {\n        name: fieldName,\n        error: foundError,\n      };\n    }\n\n    if (foundError && foundError.root && foundError.root.type) {\n      return {\n        name: `${fieldName}.root`,\n        error: foundError.root,\n      };\n    }\n\n    names.pop();\n  }\n\n  return {\n    name,\n  };\n}\n", "import { VALIDATION_MODE } from '../constants';\nimport {\n  FieldValues,\n  FormState,\n  InternalFieldName,\n  ReadFormState,\n} from '../types';\nimport isEmptyObject from '../utils/isEmptyObject';\n\nexport default <T extends FieldValues, K extends ReadFormState>(\n  formStateData: Partial<FormState<T>> & {\n    name?: InternalFieldName;\n    values?: T;\n  },\n  _proxyFormState: K,\n  updateFormState: (formState: Partial<FormState<T>>) => void,\n  isRoot?: boolean,\n) => {\n  updateFormState(formStateData);\n  const { name, ...formState } = formStateData;\n\n  return (\n    isEmptyObject(formState) ||\n    Object.keys(formState).length >= Object.keys(_proxyFormState).length ||\n    Object.keys(formState).find(\n      (key) =>\n        _proxyFormState[key as keyof ReadFormState] ===\n        (!isRoot || VALIDATION_MODE.all),\n    )\n  );\n};\n", "import {\n  FieldError,\n  FieldErrors,\n  FieldValues,\n  InternalFieldName,\n} from '../types';\nimport convertToArrayPayload from '../utils/convertToArrayPayload';\nimport get from '../utils/get';\nimport set from '../utils/set';\n\nexport default <T extends FieldValues = FieldValues>(\n  errors: FieldErrors<T>,\n  error: Partial<Record<string, FieldError>>,\n  name: InternalFieldName,\n): FieldErrors<T> => {\n  const fieldArrayErrors = convertToArrayPayload(get(errors, name));\n  set(fieldArrayErrors, 'root', error[name]);\n  set(errors, name, fieldArrayErrors);\n  return errors;\n};\n", "import { Message } from '../types';\nimport isString from '../utils/isString';\n\nexport default (value: unknown): value is Message => isString(value);\n", "import { FieldError, Ref, ValidateResult } from '../types';\nimport isBoolean from '../utils/isBoolean';\nimport isMessage from '../utils/isMessage';\n\nexport default function getValidateError(\n  result: ValidateResult,\n  ref: Ref,\n  type = 'validate',\n): FieldError | void {\n  if (\n    isMessage(result) ||\n    (Array.isArray(result) && result.every(isMessage)) ||\n    (isBoolean(result) && !result)\n  ) {\n    return {\n      type,\n      message: isMessage(result) ? result : '',\n      ref,\n    };\n  }\n}\n", "import { ValidationRule } from '../types';\nimport isObject from '../utils/isObject';\nimport isRegex from '../utils/isRegex';\n\nexport default (validationData?: ValidationRule) =>\n  isObject(validationData) && !isRegex(validationData)\n    ? validationData\n    : {\n        value: validationData,\n        message: '',\n      };\n", "import { INPUT_VALIDATION_RULES } from '../constants';\nimport {\n  Field,\n  FieldError,\n  FieldValues,\n  InternalFieldErrors,\n  InternalNameSet,\n  MaxType,\n  Message,\n  MinType,\n  NativeFieldValue,\n} from '../types';\nimport get from '../utils/get';\nimport isBoolean from '../utils/isBoolean';\nimport isCheckBoxInput from '../utils/isCheckBoxInput';\nimport isEmptyObject from '../utils/isEmptyObject';\nimport isFileInput from '../utils/isFileInput';\nimport isFunction from '../utils/isFunction';\nimport isHTMLElement from '../utils/isHTMLElement';\nimport isMessage from '../utils/isMessage';\nimport isNullOrUndefined from '../utils/isNullOrUndefined';\nimport isObject from '../utils/isObject';\nimport isRadioInput from '../utils/isRadioInput';\nimport isRegex from '../utils/isRegex';\nimport isString from '../utils/isString';\nimport isUndefined from '../utils/isUndefined';\n\nimport appendErrors from './appendErrors';\nimport getCheckboxValue from './getCheckboxValue';\nimport getRadioValue from './getRadioValue';\nimport getValidateError from './getValidateError';\nimport getValueAndMessage from './getValueAndMessage';\n\nexport default async <T extends FieldValues>(\n  field: Field,\n  disabledFieldNames: InternalNameSet,\n  formValues: T,\n  validateAllFieldCriteria: boolean,\n  shouldUseNativeValidation?: boolean,\n  isFieldArray?: boolean,\n): Promise<InternalFieldErrors> => {\n  const {\n    ref,\n    refs,\n    required,\n    maxLength,\n    minLength,\n    min,\n    max,\n    pattern,\n    validate,\n    name,\n    valueAsNumber,\n    mount,\n  } = field._f;\n  const inputValue: NativeFieldValue = get(formValues, name);\n  if (!mount || disabledFieldNames.has(name)) {\n    return {};\n  }\n  const inputRef: HTMLInputElement = refs ? refs[0] : (ref as HTMLInputElement);\n  const setCustomValidity = (message?: string | boolean) => {\n    if (shouldUseNativeValidation && inputRef.reportValidity) {\n      inputRef.setCustomValidity(isBoolean(message) ? '' : message || '');\n      inputRef.reportValidity();\n    }\n  };\n  const error: InternalFieldErrors = {};\n  const isRadio = isRadioInput(ref);\n  const isCheckBox = isCheckBoxInput(ref);\n  const isRadioOrCheckbox = isRadio || isCheckBox;\n  const isEmpty =\n    ((valueAsNumber || isFileInput(ref)) &&\n      isUndefined(ref.value) &&\n      isUndefined(inputValue)) ||\n    (isHTMLElement(ref) && ref.value === '') ||\n    inputValue === '' ||\n    (Array.isArray(inputValue) && !inputValue.length);\n  const appendErrorsCurry = appendErrors.bind(\n    null,\n    name,\n    validateAllFieldCriteria,\n    error,\n  );\n  const getMinMaxMessage = (\n    exceedMax: boolean,\n    maxLengthMessage: Message,\n    minLengthMessage: Message,\n    maxType: MaxType = INPUT_VALIDATION_RULES.maxLength,\n    minType: MinType = INPUT_VALIDATION_RULES.minLength,\n  ) => {\n    const message = exceedMax ? maxLengthMessage : minLengthMessage;\n    error[name] = {\n      type: exceedMax ? maxType : minType,\n      message,\n      ref,\n      ...appendErrorsCurry(exceedMax ? maxType : minType, message),\n    };\n  };\n\n  if (\n    isFieldArray\n      ? !Array.isArray(inputValue) || !inputValue.length\n      : required &&\n        ((!isRadioOrCheckbox && (isEmpty || isNullOrUndefined(inputValue))) ||\n          (isBoolean(inputValue) && !inputValue) ||\n          (isCheckBox && !getCheckboxValue(refs).isValid) ||\n          (isRadio && !getRadioValue(refs).isValid))\n  ) {\n    const { value, message } = isMessage(required)\n      ? { value: !!required, message: required }\n      : getValueAndMessage(required);\n\n    if (value) {\n      error[name] = {\n        type: INPUT_VALIDATION_RULES.required,\n        message,\n        ref: inputRef,\n        ...appendErrorsCurry(INPUT_VALIDATION_RULES.required, message),\n      };\n      if (!validateAllFieldCriteria) {\n        setCustomValidity(message);\n        return error;\n      }\n    }\n  }\n\n  if (!isEmpty && (!isNullOrUndefined(min) || !isNullOrUndefined(max))) {\n    let exceedMax;\n    let exceedMin;\n    const maxOutput = getValueAndMessage(max);\n    const minOutput = getValueAndMessage(min);\n\n    if (!isNullOrUndefined(inputValue) && !isNaN(inputValue as number)) {\n      const valueNumber =\n        (ref as HTMLInputElement).valueAsNumber ||\n        (inputValue ? +inputValue : inputValue);\n      if (!isNullOrUndefined(maxOutput.value)) {\n        exceedMax = valueNumber > maxOutput.value;\n      }\n      if (!isNullOrUndefined(minOutput.value)) {\n        exceedMin = valueNumber < minOutput.value;\n      }\n    } else {\n      const valueDate =\n        (ref as HTMLInputElement).valueAsDate || new Date(inputValue as string);\n      const convertTimeToDate = (time: unknown) =>\n        new Date(new Date().toDateString() + ' ' + time);\n      const isTime = ref.type == 'time';\n      const isWeek = ref.type == 'week';\n\n      if (isString(maxOutput.value) && inputValue) {\n        exceedMax = isTime\n          ? convertTimeToDate(inputValue) > convertTimeToDate(maxOutput.value)\n          : isWeek\n            ? inputValue > maxOutput.value\n            : valueDate > new Date(maxOutput.value);\n      }\n\n      if (isString(minOutput.value) && inputValue) {\n        exceedMin = isTime\n          ? convertTimeToDate(inputValue) < convertTimeToDate(minOutput.value)\n          : isWeek\n            ? inputValue < minOutput.value\n            : valueDate < new Date(minOutput.value);\n      }\n    }\n\n    if (exceedMax || exceedMin) {\n      getMinMaxMessage(\n        !!exceedMax,\n        maxOutput.message,\n        minOutput.message,\n        INPUT_VALIDATION_RULES.max,\n        INPUT_VALIDATION_RULES.min,\n      );\n      if (!validateAllFieldCriteria) {\n        setCustomValidity(error[name]!.message);\n        return error;\n      }\n    }\n  }\n\n  if (\n    (maxLength || minLength) &&\n    !isEmpty &&\n    (isString(inputValue) || (isFieldArray && Array.isArray(inputValue)))\n  ) {\n    const maxLengthOutput = getValueAndMessage(maxLength);\n    const minLengthOutput = getValueAndMessage(minLength);\n    const exceedMax =\n      !isNullOrUndefined(maxLengthOutput.value) &&\n      inputValue.length > +maxLengthOutput.value;\n    const exceedMin =\n      !isNullOrUndefined(minLengthOutput.value) &&\n      inputValue.length < +minLengthOutput.value;\n\n    if (exceedMax || exceedMin) {\n      getMinMaxMessage(\n        exceedMax,\n        maxLengthOutput.message,\n        minLengthOutput.message,\n      );\n      if (!validateAllFieldCriteria) {\n        setCustomValidity(error[name]!.message);\n        return error;\n      }\n    }\n  }\n\n  if (pattern && !isEmpty && isString(inputValue)) {\n    const { value: patternValue, message } = getValueAndMessage(pattern);\n\n    if (isRegex(patternValue) && !inputValue.match(patternValue)) {\n      error[name] = {\n        type: INPUT_VALIDATION_RULES.pattern,\n        message,\n        ref,\n        ...appendErrorsCurry(INPUT_VALIDATION_RULES.pattern, message),\n      };\n      if (!validateAllFieldCriteria) {\n        setCustomValidity(message);\n        return error;\n      }\n    }\n  }\n\n  if (validate) {\n    if (isFunction(validate)) {\n      const result = await validate(inputValue, formValues);\n      const validateError = getValidateError(result, inputRef);\n\n      if (validateError) {\n        error[name] = {\n          ...validateError,\n          ...appendErrorsCurry(\n            INPUT_VALIDATION_RULES.validate,\n            validateError.message,\n          ),\n        };\n        if (!validateAllFieldCriteria) {\n          setCustomValidity(validateError.message);\n          return error;\n        }\n      }\n    } else if (isObject(validate)) {\n      let validationResult = {} as FieldError;\n\n      for (const key in validate) {\n        if (!isEmptyObject(validationResult) && !validateAllFieldCriteria) {\n          break;\n        }\n\n        const validateError = getValidateError(\n          await validate[key](inputValue, formValues),\n          inputRef,\n          key,\n        );\n\n        if (validateError) {\n          validationResult = {\n            ...validateError,\n            ...appendErrorsCurry(key, validateError.message),\n          };\n\n          setCustomValidity(validateError.message);\n\n          if (validateAllFieldCriteria) {\n            error[name] = validationResult;\n          }\n        }\n      }\n\n      if (!isEmptyObject(validationResult)) {\n        error[name] = {\n          ref: inputRef,\n          ...validationResult,\n        };\n        if (!validateAllFieldCriteria) {\n          return error;\n        }\n      }\n    }\n  }\n\n  setCustomValidity(true);\n  return error;\n};\n", "import { EVENTS, VALIDATION_MODE } from '../constants';\nimport {\n  BatchFieldArrayUpdate,\n  ChangeHandler,\n  Control,\n  DeepPartial,\n  DelayCallback,\n  EventType,\n  Field,\n  FieldError,\n  FieldErrors,\n  FieldNamesMarkedBoolean,\n  FieldPath,\n  FieldRefs,\n  FieldValues,\n  FormState,\n  FromSubscribe,\n  GetIsDirty,\n  InternalFieldName,\n  Names,\n  Path,\n  ReadFormState,\n  Ref,\n  SetFieldValue,\n  SetValueConfig,\n  Subjects,\n  UseFormClearErrors,\n  UseFormGetFieldState,\n  UseFormGetValues,\n  UseFormHandleSubmit,\n  UseFormProps,\n  UseFormRegister,\n  UseFormReset,\n  UseFormResetField,\n  UseFormReturn,\n  UseFormSetError,\n  UseFormSetFocus,\n  UseFormSetValue,\n  UseFormTrigger,\n  UseFormUnregister,\n  UseFormWatch,\n  UseFromSubscribe,\n  WatchInternal,\n  WatchObserver,\n} from '../types';\nimport cloneObject from '../utils/cloneObject';\nimport compact from '../utils/compact';\nimport convertToArrayPayload from '../utils/convertToArrayPayload';\nimport createSubject from '../utils/createSubject';\nimport deepEqual from '../utils/deepEqual';\nimport get from '../utils/get';\nimport isBoolean from '../utils/isBoolean';\nimport isCheckBoxInput from '../utils/isCheckBoxInput';\nimport isDateObject from '../utils/isDateObject';\nimport isEmptyObject from '../utils/isEmptyObject';\nimport isFileInput from '../utils/isFileInput';\nimport isFunction from '../utils/isFunction';\nimport isHTMLElement from '../utils/isHTMLElement';\nimport isMultipleSelect from '../utils/isMultipleSelect';\nimport isNullOrUndefined from '../utils/isNullOrUndefined';\nimport isObject from '../utils/isObject';\nimport isRadioOrCheckbox from '../utils/isRadioOrCheckbox';\nimport isString from '../utils/isString';\nimport isUndefined from '../utils/isUndefined';\nimport isWeb from '../utils/isWeb';\nimport live from '../utils/live';\nimport set from '../utils/set';\nimport unset from '../utils/unset';\n\nimport generateWatchOutput from './generateWatchOutput';\nimport getDirtyFields from './getDirtyFields';\nimport getEventValue from './getEventValue';\nimport getFieldValue from './getFieldValue';\nimport getFieldValueAs from './getFieldValueAs';\nimport getResolverOptions from './getResolverOptions';\nimport getRuleValue from './getRuleValue';\nimport getValidationModes from './getValidationModes';\nimport hasPromiseValidation from './hasPromiseValidation';\nimport hasValidation from './hasValidation';\nimport isNameInFieldArray from './isNameInFieldArray';\nimport isWatched from './isWatched';\nimport iterateFieldsByAction from './iterateFieldsByAction';\nimport schemaErrorLookup from './schemaErrorLookup';\nimport shouldRenderFormState from './shouldRenderFormState';\nimport shouldSubscribeByName from './shouldSubscribeByName';\nimport skipValidation from './skipValidation';\nimport unsetEmptyArray from './unsetEmptyArray';\nimport updateFieldArrayRootError from './updateFieldArrayRootError';\nimport validateField from './validateField';\n\nconst defaultOptions = {\n  mode: VALIDATION_MODE.onSubmit,\n  reValidateMode: VALIDATION_MODE.onChange,\n  shouldFocusError: true,\n} as const;\n\nexport function createFormControl<\n  TFieldValues extends FieldValues = FieldValues,\n  TContext = any,\n  TTransformedValues = TFieldValues,\n>(\n  props: UseFormProps<TFieldValues, TContext, TTransformedValues> = {},\n): Omit<\n  UseFormReturn<TFieldValues, TContext, TTransformedValues>,\n  'formState'\n> & {\n  formControl: Omit<\n    UseFormReturn<TFieldValues, TContext, TTransformedValues>,\n    'formState'\n  >;\n} {\n  let _options = {\n    ...defaultOptions,\n    ...props,\n  };\n  let _formState: FormState<TFieldValues> = {\n    submitCount: 0,\n    isDirty: false,\n    isReady: false,\n    isLoading: isFunction(_options.defaultValues),\n    isValidating: false,\n    isSubmitted: false,\n    isSubmitting: false,\n    isSubmitSuccessful: false,\n    isValid: false,\n    touchedFields: {},\n    dirtyFields: {},\n    validatingFields: {},\n    errors: _options.errors || {},\n    disabled: _options.disabled || false,\n  };\n  const _fields: FieldRefs = {};\n  let _defaultValues =\n    isObject(_options.defaultValues) || isObject(_options.values)\n      ? cloneObject(_options.defaultValues || _options.values) || {}\n      : {};\n  let _formValues = _options.shouldUnregister\n    ? ({} as TFieldValues)\n    : (cloneObject(_defaultValues) as TFieldValues);\n  let _state = {\n    action: false,\n    mount: false,\n    watch: false,\n  };\n  let _names: Names = {\n    mount: new Set(),\n    disabled: new Set(),\n    unMount: new Set(),\n    array: new Set(),\n    watch: new Set(),\n  };\n  let delayErrorCallback: DelayCallback | null;\n  let timer = 0;\n  const _proxyFormState: ReadFormState = {\n    isDirty: false,\n    dirtyFields: false,\n    validatingFields: false,\n    touchedFields: false,\n    isValidating: false,\n    isValid: false,\n    errors: false,\n  };\n  let _proxySubscribeFormState = {\n    ..._proxyFormState,\n  };\n  const _subjects: Subjects<TFieldValues> = {\n    array: createSubject(),\n    state: createSubject(),\n  };\n\n  const shouldDisplayAllAssociatedErrors =\n    _options.criteriaMode === VALIDATION_MODE.all;\n\n  const debounce =\n    <T extends Function>(callback: T) =>\n    (wait: number) => {\n      clearTimeout(timer);\n      timer = setTimeout(callback, wait);\n    };\n\n  const _setValid = async (shouldUpdateValid?: boolean) => {\n    if (\n      !_options.disabled &&\n      (_proxyFormState.isValid ||\n        _proxySubscribeFormState.isValid ||\n        shouldUpdateValid)\n    ) {\n      const isValid = _options.resolver\n        ? isEmptyObject((await _runSchema()).errors)\n        : await executeBuiltInValidation(_fields, true);\n\n      if (isValid !== _formState.isValid) {\n        _subjects.state.next({\n          isValid,\n        });\n      }\n    }\n  };\n\n  const _updateIsValidating = (names?: string[], isValidating?: boolean) => {\n    if (\n      !_options.disabled &&\n      (_proxyFormState.isValidating ||\n        _proxyFormState.validatingFields ||\n        _proxySubscribeFormState.isValidating ||\n        _proxySubscribeFormState.validatingFields)\n    ) {\n      (names || Array.from(_names.mount)).forEach((name) => {\n        if (name) {\n          isValidating\n            ? set(_formState.validatingFields, name, isValidating)\n            : unset(_formState.validatingFields, name);\n        }\n      });\n\n      _subjects.state.next({\n        validatingFields: _formState.validatingFields,\n        isValidating: !isEmptyObject(_formState.validatingFields),\n      });\n    }\n  };\n\n  const _setFieldArray: BatchFieldArrayUpdate = (\n    name,\n    values = [],\n    method,\n    args,\n    shouldSetValues = true,\n    shouldUpdateFieldsAndState = true,\n  ) => {\n    if (args && method && !_options.disabled) {\n      _state.action = true;\n      if (shouldUpdateFieldsAndState && Array.isArray(get(_fields, name))) {\n        const fieldValues = method(get(_fields, name), args.argA, args.argB);\n        shouldSetValues && set(_fields, name, fieldValues);\n      }\n\n      if (\n        shouldUpdateFieldsAndState &&\n        Array.isArray(get(_formState.errors, name))\n      ) {\n        const errors = method(\n          get(_formState.errors, name),\n          args.argA,\n          args.argB,\n        );\n        shouldSetValues && set(_formState.errors, name, errors);\n        unsetEmptyArray(_formState.errors, name);\n      }\n\n      if (\n        (_proxyFormState.touchedFields ||\n          _proxySubscribeFormState.touchedFields) &&\n        shouldUpdateFieldsAndState &&\n        Array.isArray(get(_formState.touchedFields, name))\n      ) {\n        const touchedFields = method(\n          get(_formState.touchedFields, name),\n          args.argA,\n          args.argB,\n        );\n        shouldSetValues && set(_formState.touchedFields, name, touchedFields);\n      }\n\n      if (_proxyFormState.dirtyFields || _proxySubscribeFormState.dirtyFields) {\n        _formState.dirtyFields = getDirtyFields(_defaultValues, _formValues);\n      }\n\n      _subjects.state.next({\n        name,\n        isDirty: _getDirty(name, values),\n        dirtyFields: _formState.dirtyFields,\n        errors: _formState.errors,\n        isValid: _formState.isValid,\n      });\n    } else {\n      set(_formValues, name, values);\n    }\n  };\n\n  const updateErrors = (name: InternalFieldName, error: FieldError) => {\n    set(_formState.errors, name, error);\n    _subjects.state.next({\n      errors: _formState.errors,\n    });\n  };\n\n  const _setErrors = (errors: FieldErrors<TFieldValues>) => {\n    _formState.errors = errors;\n    _subjects.state.next({\n      errors: _formState.errors,\n      isValid: false,\n    });\n  };\n\n  const updateValidAndValue = (\n    name: InternalFieldName,\n    shouldSkipSetValueAs: boolean,\n    value?: unknown,\n    ref?: Ref,\n  ) => {\n    const field: Field = get(_fields, name);\n\n    if (field) {\n      const defaultValue = get(\n        _formValues,\n        name,\n        isUndefined(value) ? get(_defaultValues, name) : value,\n      );\n\n      isUndefined(defaultValue) ||\n      (ref && (ref as HTMLInputElement).defaultChecked) ||\n      shouldSkipSetValueAs\n        ? set(\n            _formValues,\n            name,\n            shouldSkipSetValueAs ? defaultValue : getFieldValue(field._f),\n          )\n        : setFieldValue(name, defaultValue);\n\n      _state.mount && _setValid();\n    }\n  };\n\n  const updateTouchAndDirty = (\n    name: InternalFieldName,\n    fieldValue: unknown,\n    isBlurEvent?: boolean,\n    shouldDirty?: boolean,\n    shouldRender?: boolean,\n  ): Partial<\n    Pick<FormState<TFieldValues>, 'dirtyFields' | 'isDirty' | 'touchedFields'>\n  > => {\n    let shouldUpdateField = false;\n    let isPreviousDirty = false;\n    const output: Partial<FormState<TFieldValues>> & { name: string } = {\n      name,\n    };\n\n    if (!_options.disabled) {\n      if (!isBlurEvent || shouldDirty) {\n        if (_proxyFormState.isDirty || _proxySubscribeFormState.isDirty) {\n          isPreviousDirty = _formState.isDirty;\n          _formState.isDirty = output.isDirty = _getDirty();\n          shouldUpdateField = isPreviousDirty !== output.isDirty;\n        }\n\n        const isCurrentFieldPristine = deepEqual(\n          get(_defaultValues, name),\n          fieldValue,\n        );\n\n        isPreviousDirty = !!get(_formState.dirtyFields, name);\n        isCurrentFieldPristine\n          ? unset(_formState.dirtyFields, name)\n          : set(_formState.dirtyFields, name, true);\n        output.dirtyFields = _formState.dirtyFields;\n        shouldUpdateField =\n          shouldUpdateField ||\n          ((_proxyFormState.dirtyFields ||\n            _proxySubscribeFormState.dirtyFields) &&\n            isPreviousDirty !== !isCurrentFieldPristine);\n      }\n\n      if (isBlurEvent) {\n        const isPreviousFieldTouched = get(_formState.touchedFields, name);\n\n        if (!isPreviousFieldTouched) {\n          set(_formState.touchedFields, name, isBlurEvent);\n          output.touchedFields = _formState.touchedFields;\n          shouldUpdateField =\n            shouldUpdateField ||\n            ((_proxyFormState.touchedFields ||\n              _proxySubscribeFormState.touchedFields) &&\n              isPreviousFieldTouched !== isBlurEvent);\n        }\n      }\n\n      shouldUpdateField && shouldRender && _subjects.state.next(output);\n    }\n\n    return shouldUpdateField ? output : {};\n  };\n\n  const shouldRenderByError = (\n    name: InternalFieldName,\n    isValid?: boolean,\n    error?: FieldError,\n    fieldState?: {\n      dirty?: FieldNamesMarkedBoolean<TFieldValues>;\n      isDirty?: boolean;\n      touched?: FieldNamesMarkedBoolean<TFieldValues>;\n    },\n  ) => {\n    const previousFieldError = get(_formState.errors, name);\n    const shouldUpdateValid =\n      (_proxyFormState.isValid || _proxySubscribeFormState.isValid) &&\n      isBoolean(isValid) &&\n      _formState.isValid !== isValid;\n\n    if (_options.delayError && error) {\n      delayErrorCallback = debounce(() => updateErrors(name, error));\n      delayErrorCallback(_options.delayError);\n    } else {\n      clearTimeout(timer);\n      delayErrorCallback = null;\n      error\n        ? set(_formState.errors, name, error)\n        : unset(_formState.errors, name);\n    }\n\n    if (\n      (error ? !deepEqual(previousFieldError, error) : previousFieldError) ||\n      !isEmptyObject(fieldState) ||\n      shouldUpdateValid\n    ) {\n      const updatedFormState = {\n        ...fieldState,\n        ...(shouldUpdateValid && isBoolean(isValid) ? { isValid } : {}),\n        errors: _formState.errors,\n        name,\n      };\n\n      _formState = {\n        ..._formState,\n        ...updatedFormState,\n      };\n\n      _subjects.state.next(updatedFormState);\n    }\n  };\n\n  const _runSchema = async (name?: InternalFieldName[]) => {\n    _updateIsValidating(name, true);\n    const result = await _options.resolver!(\n      _formValues as TFieldValues,\n      _options.context,\n      getResolverOptions(\n        name || _names.mount,\n        _fields,\n        _options.criteriaMode,\n        _options.shouldUseNativeValidation,\n      ),\n    );\n    _updateIsValidating(name);\n    return result;\n  };\n\n  const executeSchemaAndUpdateState = async (names?: InternalFieldName[]) => {\n    const { errors } = await _runSchema(names);\n\n    if (names) {\n      for (const name of names) {\n        const error = get(errors, name);\n        error\n          ? set(_formState.errors, name, error)\n          : unset(_formState.errors, name);\n      }\n    } else {\n      _formState.errors = errors;\n    }\n\n    return errors;\n  };\n\n  const executeBuiltInValidation = async (\n    fields: FieldRefs,\n    shouldOnlyCheckValid?: boolean,\n    context: {\n      valid: boolean;\n    } = {\n      valid: true,\n    },\n  ) => {\n    for (const name in fields) {\n      const field = fields[name];\n\n      if (field) {\n        const { _f, ...fieldValue } = field as Field;\n\n        if (_f) {\n          const isFieldArrayRoot = _names.array.has(_f.name);\n          const isPromiseFunction =\n            field._f && hasPromiseValidation((field as Field)._f);\n\n          if (isPromiseFunction && _proxyFormState.validatingFields) {\n            _updateIsValidating([name], true);\n          }\n\n          const fieldError = await validateField(\n            field as Field,\n            _names.disabled,\n            _formValues,\n            shouldDisplayAllAssociatedErrors,\n            _options.shouldUseNativeValidation && !shouldOnlyCheckValid,\n            isFieldArrayRoot,\n          );\n\n          if (isPromiseFunction && _proxyFormState.validatingFields) {\n            _updateIsValidating([name]);\n          }\n\n          if (fieldError[_f.name]) {\n            context.valid = false;\n            if (shouldOnlyCheckValid) {\n              break;\n            }\n          }\n\n          !shouldOnlyCheckValid &&\n            (get(fieldError, _f.name)\n              ? isFieldArrayRoot\n                ? updateFieldArrayRootError(\n                    _formState.errors,\n                    fieldError,\n                    _f.name,\n                  )\n                : set(_formState.errors, _f.name, fieldError[_f.name])\n              : unset(_formState.errors, _f.name));\n        }\n\n        !isEmptyObject(fieldValue) &&\n          (await executeBuiltInValidation(\n            fieldValue,\n            shouldOnlyCheckValid,\n            context,\n          ));\n      }\n    }\n\n    return context.valid;\n  };\n\n  const _removeUnmounted = () => {\n    for (const name of _names.unMount) {\n      const field: Field = get(_fields, name);\n\n      field &&\n        (field._f.refs\n          ? field._f.refs.every((ref) => !live(ref))\n          : !live(field._f.ref)) &&\n        unregister(name as FieldPath<TFieldValues>);\n    }\n\n    _names.unMount = new Set();\n  };\n\n  const _getDirty: GetIsDirty = (name, data) =>\n    !_options.disabled &&\n    (name && data && set(_formValues, name, data),\n    !deepEqual(getValues(), _defaultValues));\n\n  const _getWatch: WatchInternal<TFieldValues> = (\n    names,\n    defaultValue,\n    isGlobal,\n  ) =>\n    generateWatchOutput(\n      names,\n      _names,\n      {\n        ...(_state.mount\n          ? _formValues\n          : isUndefined(defaultValue)\n            ? _defaultValues\n            : isString(names)\n              ? { [names]: defaultValue }\n              : defaultValue),\n      },\n      isGlobal,\n      defaultValue,\n    );\n\n  const _getFieldArray = <TFieldArrayValues>(\n    name: InternalFieldName,\n  ): Partial<TFieldArrayValues>[] =>\n    compact(\n      get(\n        _state.mount ? _formValues : _defaultValues,\n        name,\n        _options.shouldUnregister ? get(_defaultValues, name, []) : [],\n      ),\n    );\n\n  const setFieldValue = (\n    name: InternalFieldName,\n    value: SetFieldValue<TFieldValues>,\n    options: SetValueConfig = {},\n  ) => {\n    const field: Field = get(_fields, name);\n    let fieldValue: unknown = value;\n\n    if (field) {\n      const fieldReference = field._f;\n\n      if (fieldReference) {\n        !fieldReference.disabled &&\n          set(_formValues, name, getFieldValueAs(value, fieldReference));\n\n        fieldValue =\n          isHTMLElement(fieldReference.ref) && isNullOrUndefined(value)\n            ? ''\n            : value;\n\n        if (isMultipleSelect(fieldReference.ref)) {\n          [...fieldReference.ref.options].forEach(\n            (optionRef) =>\n              (optionRef.selected = (\n                fieldValue as InternalFieldName[]\n              ).includes(optionRef.value)),\n          );\n        } else if (fieldReference.refs) {\n          if (isCheckBoxInput(fieldReference.ref)) {\n            fieldReference.refs.forEach((checkboxRef) => {\n              if (!checkboxRef.defaultChecked || !checkboxRef.disabled) {\n                if (Array.isArray(fieldValue)) {\n                  checkboxRef.checked = !!fieldValue.find(\n                    (data: string) => data === checkboxRef.value,\n                  );\n                } else {\n                  checkboxRef.checked =\n                    fieldValue === checkboxRef.value || !!fieldValue;\n                }\n              }\n            });\n          } else {\n            fieldReference.refs.forEach(\n              (radioRef: HTMLInputElement) =>\n                (radioRef.checked = radioRef.value === fieldValue),\n            );\n          }\n        } else if (isFileInput(fieldReference.ref)) {\n          fieldReference.ref.value = '';\n        } else {\n          fieldReference.ref.value = fieldValue;\n\n          if (!fieldReference.ref.type) {\n            _subjects.state.next({\n              name,\n              values: cloneObject(_formValues),\n            });\n          }\n        }\n      }\n    }\n\n    (options.shouldDirty || options.shouldTouch) &&\n      updateTouchAndDirty(\n        name,\n        fieldValue,\n        options.shouldTouch,\n        options.shouldDirty,\n        true,\n      );\n\n    options.shouldValidate && trigger(name as Path<TFieldValues>);\n  };\n\n  const setValues = <\n    T extends InternalFieldName,\n    K extends SetFieldValue<TFieldValues>,\n    U extends SetValueConfig,\n  >(\n    name: T,\n    value: K,\n    options: U,\n  ) => {\n    for (const fieldKey in value) {\n      if (!value.hasOwnProperty(fieldKey)) {\n        return;\n      }\n      const fieldValue = value[fieldKey];\n      const fieldName = name + '.' + fieldKey;\n      const field = get(_fields, fieldName);\n\n      (_names.array.has(name) ||\n        isObject(fieldValue) ||\n        (field && !field._f)) &&\n      !isDateObject(fieldValue)\n        ? setValues(fieldName, fieldValue, options)\n        : setFieldValue(fieldName, fieldValue, options);\n    }\n  };\n\n  const setValue: UseFormSetValue<TFieldValues> = (\n    name,\n    value,\n    options = {},\n  ) => {\n    const field = get(_fields, name);\n    const isFieldArray = _names.array.has(name);\n    const cloneValue = cloneObject(value);\n\n    set(_formValues, name, cloneValue);\n\n    if (isFieldArray) {\n      _subjects.array.next({\n        name,\n        values: cloneObject(_formValues),\n      });\n\n      if (\n        (_proxyFormState.isDirty ||\n          _proxyFormState.dirtyFields ||\n          _proxySubscribeFormState.isDirty ||\n          _proxySubscribeFormState.dirtyFields) &&\n        options.shouldDirty\n      ) {\n        _subjects.state.next({\n          name,\n          dirtyFields: getDirtyFields(_defaultValues, _formValues),\n          isDirty: _getDirty(name, cloneValue),\n        });\n      }\n    } else {\n      field && !field._f && !isNullOrUndefined(cloneValue)\n        ? setValues(name, cloneValue, options)\n        : setFieldValue(name, cloneValue, options);\n    }\n\n    isWatched(name, _names) && _subjects.state.next({ ..._formState });\n    _subjects.state.next({\n      name: _state.mount ? name : undefined,\n      values: cloneObject(_formValues),\n    });\n  };\n\n  const onChange: ChangeHandler = async (event) => {\n    _state.mount = true;\n    const target = event.target;\n    let name: string = target.name;\n    let isFieldValueUpdated = true;\n    const field: Field = get(_fields, name);\n    const _updateIsFieldValueUpdated = (fieldValue: unknown) => {\n      isFieldValueUpdated =\n        Number.isNaN(fieldValue) ||\n        (isDateObject(fieldValue) && isNaN(fieldValue.getTime())) ||\n        deepEqual(fieldValue, get(_formValues, name, fieldValue));\n    };\n    const validationModeBeforeSubmit = getValidationModes(_options.mode);\n    const validationModeAfterSubmit = getValidationModes(\n      _options.reValidateMode,\n    );\n\n    if (field) {\n      let error;\n      let isValid;\n      const fieldValue = target.type\n        ? getFieldValue(field._f)\n        : getEventValue(event);\n      const isBlurEvent =\n        event.type === EVENTS.BLUR || event.type === EVENTS.FOCUS_OUT;\n      const shouldSkipValidation =\n        (!hasValidation(field._f) &&\n          !_options.resolver &&\n          !get(_formState.errors, name) &&\n          !field._f.deps) ||\n        skipValidation(\n          isBlurEvent,\n          get(_formState.touchedFields, name),\n          _formState.isSubmitted,\n          validationModeAfterSubmit,\n          validationModeBeforeSubmit,\n        );\n      const watched = isWatched(name, _names, isBlurEvent);\n\n      set(_formValues, name, fieldValue);\n\n      if (isBlurEvent) {\n        field._f.onBlur && field._f.onBlur(event);\n        delayErrorCallback && delayErrorCallback(0);\n      } else if (field._f.onChange) {\n        field._f.onChange(event);\n      }\n\n      const fieldState = updateTouchAndDirty(name, fieldValue, isBlurEvent);\n\n      const shouldRender = !isEmptyObject(fieldState) || watched;\n\n      !isBlurEvent &&\n        _subjects.state.next({\n          name,\n          type: event.type,\n          values: cloneObject(_formValues),\n        });\n\n      if (shouldSkipValidation) {\n        if (_proxyFormState.isValid || _proxySubscribeFormState.isValid) {\n          if (_options.mode === 'onBlur') {\n            if (isBlurEvent) {\n              _setValid();\n            }\n          } else if (!isBlurEvent) {\n            _setValid();\n          }\n        }\n\n        return (\n          shouldRender &&\n          _subjects.state.next({ name, ...(watched ? {} : fieldState) })\n        );\n      }\n\n      !isBlurEvent && watched && _subjects.state.next({ ..._formState });\n\n      if (_options.resolver) {\n        const { errors } = await _runSchema([name]);\n\n        _updateIsFieldValueUpdated(fieldValue);\n\n        if (isFieldValueUpdated) {\n          const previousErrorLookupResult = schemaErrorLookup(\n            _formState.errors,\n            _fields,\n            name,\n          );\n          const errorLookupResult = schemaErrorLookup(\n            errors,\n            _fields,\n            previousErrorLookupResult.name || name,\n          );\n\n          error = errorLookupResult.error;\n          name = errorLookupResult.name;\n\n          isValid = isEmptyObject(errors);\n        }\n      } else {\n        _updateIsValidating([name], true);\n        error = (\n          await validateField(\n            field,\n            _names.disabled,\n            _formValues,\n            shouldDisplayAllAssociatedErrors,\n            _options.shouldUseNativeValidation,\n          )\n        )[name];\n        _updateIsValidating([name]);\n\n        _updateIsFieldValueUpdated(fieldValue);\n\n        if (isFieldValueUpdated) {\n          if (error) {\n            isValid = false;\n          } else if (\n            _proxyFormState.isValid ||\n            _proxySubscribeFormState.isValid\n          ) {\n            isValid = await executeBuiltInValidation(_fields, true);\n          }\n        }\n      }\n\n      if (isFieldValueUpdated) {\n        field._f.deps &&\n          trigger(\n            field._f.deps as\n              | FieldPath<TFieldValues>\n              | FieldPath<TFieldValues>[],\n          );\n        shouldRenderByError(name, isValid, error, fieldState);\n      }\n    }\n  };\n\n  const _focusInput = (ref: Ref, key: string) => {\n    if (get(_formState.errors, key) && ref.focus) {\n      ref.focus();\n      return 1;\n    }\n    return;\n  };\n\n  const trigger: UseFormTrigger<TFieldValues> = async (name, options = {}) => {\n    let isValid;\n    let validationResult;\n    const fieldNames = convertToArrayPayload(name) as InternalFieldName[];\n\n    if (_options.resolver) {\n      const errors = await executeSchemaAndUpdateState(\n        isUndefined(name) ? name : fieldNames,\n      );\n\n      isValid = isEmptyObject(errors);\n      validationResult = name\n        ? !fieldNames.some((name) => get(errors, name))\n        : isValid;\n    } else if (name) {\n      validationResult = (\n        await Promise.all(\n          fieldNames.map(async (fieldName) => {\n            const field = get(_fields, fieldName);\n            return await executeBuiltInValidation(\n              field && field._f ? { [fieldName]: field } : field,\n            );\n          }),\n        )\n      ).every(Boolean);\n      !(!validationResult && !_formState.isValid) && _setValid();\n    } else {\n      validationResult = isValid = await executeBuiltInValidation(_fields);\n    }\n\n    _subjects.state.next({\n      ...(!isString(name) ||\n      ((_proxyFormState.isValid || _proxySubscribeFormState.isValid) &&\n        isValid !== _formState.isValid)\n        ? {}\n        : { name }),\n      ...(_options.resolver || !name ? { isValid } : {}),\n      errors: _formState.errors,\n    });\n\n    options.shouldFocus &&\n      !validationResult &&\n      iterateFieldsByAction(\n        _fields,\n        _focusInput,\n        name ? fieldNames : _names.mount,\n      );\n\n    return validationResult;\n  };\n\n  const getValues: UseFormGetValues<TFieldValues> = (\n    fieldNames?:\n      | FieldPath<TFieldValues>\n      | ReadonlyArray<FieldPath<TFieldValues>>,\n  ) => {\n    const values = {\n      ...(_state.mount ? _formValues : _defaultValues),\n    };\n\n    return isUndefined(fieldNames)\n      ? values\n      : isString(fieldNames)\n        ? get(values, fieldNames)\n        : fieldNames.map((name) => get(values, name));\n  };\n\n  const getFieldState: UseFormGetFieldState<TFieldValues> = (\n    name,\n    formState,\n  ) => ({\n    invalid: !!get((formState || _formState).errors, name),\n    isDirty: !!get((formState || _formState).dirtyFields, name),\n    error: get((formState || _formState).errors, name),\n    isValidating: !!get(_formState.validatingFields, name),\n    isTouched: !!get((formState || _formState).touchedFields, name),\n  });\n\n  const clearErrors: UseFormClearErrors<TFieldValues> = (name) => {\n    name &&\n      convertToArrayPayload(name).forEach((inputName) =>\n        unset(_formState.errors, inputName),\n      );\n\n    _subjects.state.next({\n      errors: name ? _formState.errors : {},\n    });\n  };\n\n  const setError: UseFormSetError<TFieldValues> = (name, error, options) => {\n    const ref = (get(_fields, name, { _f: {} })._f || {}).ref;\n    const currentError = get(_formState.errors, name) || {};\n\n    // Don't override existing error messages elsewhere in the object tree.\n    const { ref: currentRef, message, type, ...restOfErrorTree } = currentError;\n\n    set(_formState.errors, name, {\n      ...restOfErrorTree,\n      ...error,\n      ref,\n    });\n\n    _subjects.state.next({\n      name,\n      errors: _formState.errors,\n      isValid: false,\n    });\n\n    options && options.shouldFocus && ref && ref.focus && ref.focus();\n  };\n\n  const watch: UseFormWatch<TFieldValues> = (\n    name?:\n      | FieldPath<TFieldValues>\n      | ReadonlyArray<FieldPath<TFieldValues>>\n      | WatchObserver<TFieldValues>,\n    defaultValue?: DeepPartial<TFieldValues>,\n  ) =>\n    isFunction(name)\n      ? _subjects.state.subscribe({\n          next: (payload) =>\n            name(\n              _getWatch(undefined, defaultValue),\n              payload as {\n                name?: FieldPath<TFieldValues>;\n                type?: EventType;\n                value?: unknown;\n              },\n            ),\n        })\n      : _getWatch(\n          name as InternalFieldName | InternalFieldName[],\n          defaultValue,\n          true,\n        );\n\n  const _subscribe: FromSubscribe<TFieldValues> = (props) =>\n    _subjects.state.subscribe({\n      next: (\n        formState: Partial<FormState<TFieldValues>> & {\n          name?: InternalFieldName;\n          values?: TFieldValues | undefined;\n        },\n      ) => {\n        if (\n          shouldSubscribeByName(props.name, formState.name, props.exact) &&\n          shouldRenderFormState(\n            formState,\n            (props.formState as ReadFormState) || _proxyFormState,\n            _setFormState,\n            props.reRenderRoot,\n          )\n        ) {\n          props.callback({\n            values: { ..._formValues } as TFieldValues,\n            ..._formState,\n            ...formState,\n          });\n        }\n      },\n    }).unsubscribe;\n\n  const subscribe: UseFromSubscribe<TFieldValues> = (props) => {\n    _state.mount = true;\n    _proxySubscribeFormState = {\n      ..._proxySubscribeFormState,\n      ...props.formState,\n    };\n    return _subscribe({\n      ...props,\n      formState: _proxySubscribeFormState,\n    });\n  };\n\n  const unregister: UseFormUnregister<TFieldValues> = (name, options = {}) => {\n    for (const fieldName of name ? convertToArrayPayload(name) : _names.mount) {\n      _names.mount.delete(fieldName);\n      _names.array.delete(fieldName);\n\n      if (!options.keepValue) {\n        unset(_fields, fieldName);\n        unset(_formValues, fieldName);\n      }\n\n      !options.keepError && unset(_formState.errors, fieldName);\n      !options.keepDirty && unset(_formState.dirtyFields, fieldName);\n      !options.keepTouched && unset(_formState.touchedFields, fieldName);\n      !options.keepIsValidating &&\n        unset(_formState.validatingFields, fieldName);\n      !_options.shouldUnregister &&\n        !options.keepDefaultValue &&\n        unset(_defaultValues, fieldName);\n    }\n\n    _subjects.state.next({\n      values: cloneObject(_formValues),\n    });\n\n    _subjects.state.next({\n      ..._formState,\n      ...(!options.keepDirty ? {} : { isDirty: _getDirty() }),\n    });\n\n    !options.keepIsValid && _setValid();\n  };\n\n  const _setDisabledField: Control<TFieldValues>['_setDisabledField'] = ({\n    disabled,\n    name,\n  }) => {\n    if (\n      (isBoolean(disabled) && _state.mount) ||\n      !!disabled ||\n      _names.disabled.has(name)\n    ) {\n      disabled ? _names.disabled.add(name) : _names.disabled.delete(name);\n    }\n  };\n\n  const register: UseFormRegister<TFieldValues> = (name, options = {}) => {\n    let field = get(_fields, name);\n    const disabledIsDefined =\n      isBoolean(options.disabled) || isBoolean(_options.disabled);\n\n    set(_fields, name, {\n      ...(field || {}),\n      _f: {\n        ...(field && field._f ? field._f : { ref: { name } }),\n        name,\n        mount: true,\n        ...options,\n      },\n    });\n    _names.mount.add(name);\n\n    if (field) {\n      _setDisabledField({\n        disabled: isBoolean(options.disabled)\n          ? options.disabled\n          : _options.disabled,\n        name,\n      });\n    } else {\n      updateValidAndValue(name, true, options.value);\n    }\n\n    return {\n      ...(disabledIsDefined\n        ? { disabled: options.disabled || _options.disabled }\n        : {}),\n      ...(_options.progressive\n        ? {\n            required: !!options.required,\n            min: getRuleValue(options.min),\n            max: getRuleValue(options.max),\n            minLength: getRuleValue<number>(options.minLength) as number,\n            maxLength: getRuleValue(options.maxLength) as number,\n            pattern: getRuleValue(options.pattern) as string,\n          }\n        : {}),\n      name,\n      onChange,\n      onBlur: onChange,\n      ref: (ref: HTMLInputElement | null): void => {\n        if (ref) {\n          register(name, options);\n          field = get(_fields, name);\n\n          const fieldRef = isUndefined(ref.value)\n            ? ref.querySelectorAll\n              ? (ref.querySelectorAll('input,select,textarea')[0] as Ref) || ref\n              : ref\n            : ref;\n          const radioOrCheckbox = isRadioOrCheckbox(fieldRef);\n          const refs = field._f.refs || [];\n\n          if (\n            radioOrCheckbox\n              ? refs.find((option: Ref) => option === fieldRef)\n              : fieldRef === field._f.ref\n          ) {\n            return;\n          }\n\n          set(_fields, name, {\n            _f: {\n              ...field._f,\n              ...(radioOrCheckbox\n                ? {\n                    refs: [\n                      ...refs.filter(live),\n                      fieldRef,\n                      ...(Array.isArray(get(_defaultValues, name)) ? [{}] : []),\n                    ],\n                    ref: { type: fieldRef.type, name },\n                  }\n                : { ref: fieldRef }),\n            },\n          });\n\n          updateValidAndValue(name, false, undefined, fieldRef);\n        } else {\n          field = get(_fields, name, {});\n\n          if (field._f) {\n            field._f.mount = false;\n          }\n\n          (_options.shouldUnregister || options.shouldUnregister) &&\n            !(isNameInFieldArray(_names.array, name) && _state.action) &&\n            _names.unMount.add(name);\n        }\n      },\n    };\n  };\n\n  const _focusError = () =>\n    _options.shouldFocusError &&\n    iterateFieldsByAction(_fields, _focusInput, _names.mount);\n\n  const _disableForm = (disabled?: boolean) => {\n    if (isBoolean(disabled)) {\n      _subjects.state.next({ disabled });\n      iterateFieldsByAction(\n        _fields,\n        (ref, name) => {\n          const currentField: Field = get(_fields, name);\n          if (currentField) {\n            ref.disabled = currentField._f.disabled || disabled;\n\n            if (Array.isArray(currentField._f.refs)) {\n              currentField._f.refs.forEach((inputRef) => {\n                inputRef.disabled = currentField._f.disabled || disabled;\n              });\n            }\n          }\n        },\n        0,\n        false,\n      );\n    }\n  };\n\n  const handleSubmit: UseFormHandleSubmit<TFieldValues, TTransformedValues> =\n    (onValid, onInvalid) => async (e) => {\n      let onValidError = undefined;\n      if (e) {\n        e.preventDefault && e.preventDefault();\n        (e as React.BaseSyntheticEvent).persist &&\n          (e as React.BaseSyntheticEvent).persist();\n      }\n      let fieldValues: TFieldValues | TTransformedValues | {} =\n        cloneObject(_formValues);\n\n      _subjects.state.next({\n        isSubmitting: true,\n      });\n\n      if (_options.resolver) {\n        const { errors, values } = await _runSchema();\n        _formState.errors = errors;\n        fieldValues = values as TFieldValues;\n      } else {\n        await executeBuiltInValidation(_fields);\n      }\n\n      if (_names.disabled.size) {\n        for (const name of _names.disabled) {\n          set(fieldValues, name, undefined);\n        }\n      }\n\n      unset(_formState.errors, 'root');\n\n      if (isEmptyObject(_formState.errors)) {\n        _subjects.state.next({\n          errors: {},\n        });\n        try {\n          await onValid(fieldValues as TTransformedValues, e);\n        } catch (error) {\n          onValidError = error;\n        }\n      } else {\n        if (onInvalid) {\n          await onInvalid({ ..._formState.errors }, e);\n        }\n        _focusError();\n        setTimeout(_focusError);\n      }\n\n      _subjects.state.next({\n        isSubmitted: true,\n        isSubmitting: false,\n        isSubmitSuccessful: isEmptyObject(_formState.errors) && !onValidError,\n        submitCount: _formState.submitCount + 1,\n        errors: _formState.errors,\n      });\n      if (onValidError) {\n        throw onValidError;\n      }\n    };\n\n  const resetField: UseFormResetField<TFieldValues> = (name, options = {}) => {\n    if (get(_fields, name)) {\n      if (isUndefined(options.defaultValue)) {\n        setValue(name, cloneObject(get(_defaultValues, name)));\n      } else {\n        setValue(\n          name,\n          options.defaultValue as Parameters<typeof setValue<typeof name>>[1],\n        );\n        set(_defaultValues, name, cloneObject(options.defaultValue));\n      }\n\n      if (!options.keepTouched) {\n        unset(_formState.touchedFields, name);\n      }\n\n      if (!options.keepDirty) {\n        unset(_formState.dirtyFields, name);\n        _formState.isDirty = options.defaultValue\n          ? _getDirty(name, cloneObject(get(_defaultValues, name)))\n          : _getDirty();\n      }\n\n      if (!options.keepError) {\n        unset(_formState.errors, name);\n        _proxyFormState.isValid && _setValid();\n      }\n\n      _subjects.state.next({ ..._formState });\n    }\n  };\n\n  const _reset: UseFormReset<TFieldValues> = (\n    formValues,\n    keepStateOptions = {},\n  ) => {\n    const updatedValues = formValues ? cloneObject(formValues) : _defaultValues;\n    const cloneUpdatedValues = cloneObject(updatedValues);\n    const isEmptyResetValues = isEmptyObject(formValues);\n    const values = isEmptyResetValues ? _defaultValues : cloneUpdatedValues;\n\n    if (!keepStateOptions.keepDefaultValues) {\n      _defaultValues = updatedValues;\n    }\n\n    if (!keepStateOptions.keepValues) {\n      if (keepStateOptions.keepDirtyValues) {\n        const fieldsToCheck = new Set([\n          ..._names.mount,\n          ...Object.keys(getDirtyFields(_defaultValues, _formValues)),\n        ]);\n        for (const fieldName of Array.from(fieldsToCheck)) {\n          get(_formState.dirtyFields, fieldName)\n            ? set(values, fieldName, get(_formValues, fieldName))\n            : setValue(\n                fieldName as FieldPath<TFieldValues>,\n                get(values, fieldName),\n              );\n        }\n      } else {\n        if (isWeb && isUndefined(formValues)) {\n          for (const name of _names.mount) {\n            const field = get(_fields, name);\n            if (field && field._f) {\n              const fieldReference = Array.isArray(field._f.refs)\n                ? field._f.refs[0]\n                : field._f.ref;\n\n              if (isHTMLElement(fieldReference)) {\n                const form = fieldReference.closest('form');\n                if (form) {\n                  form.reset();\n                  break;\n                }\n              }\n            }\n          }\n        }\n\n        for (const fieldName of _names.mount) {\n          setValue(\n            fieldName as FieldPath<TFieldValues>,\n            get(values, fieldName),\n          );\n        }\n      }\n\n      _formValues = cloneObject(values) as TFieldValues;\n\n      _subjects.array.next({\n        values: { ...values },\n      });\n\n      _subjects.state.next({\n        values: { ...values } as TFieldValues,\n      });\n    }\n\n    _names = {\n      mount: keepStateOptions.keepDirtyValues ? _names.mount : new Set(),\n      unMount: new Set(),\n      array: new Set(),\n      disabled: new Set(),\n      watch: new Set(),\n      watchAll: false,\n      focus: '',\n    };\n\n    _state.mount =\n      !_proxyFormState.isValid ||\n      !!keepStateOptions.keepIsValid ||\n      !!keepStateOptions.keepDirtyValues;\n\n    _state.watch = !!_options.shouldUnregister;\n\n    _subjects.state.next({\n      submitCount: keepStateOptions.keepSubmitCount\n        ? _formState.submitCount\n        : 0,\n      isDirty: isEmptyResetValues\n        ? false\n        : keepStateOptions.keepDirty\n          ? _formState.isDirty\n          : !!(\n              keepStateOptions.keepDefaultValues &&\n              !deepEqual(formValues, _defaultValues)\n            ),\n      isSubmitted: keepStateOptions.keepIsSubmitted\n        ? _formState.isSubmitted\n        : false,\n      dirtyFields: isEmptyResetValues\n        ? {}\n        : keepStateOptions.keepDirtyValues\n          ? keepStateOptions.keepDefaultValues && _formValues\n            ? getDirtyFields(_defaultValues, _formValues)\n            : _formState.dirtyFields\n          : keepStateOptions.keepDefaultValues && formValues\n            ? getDirtyFields(_defaultValues, formValues)\n            : keepStateOptions.keepDirty\n              ? _formState.dirtyFields\n              : {},\n      touchedFields: keepStateOptions.keepTouched\n        ? _formState.touchedFields\n        : {},\n      errors: keepStateOptions.keepErrors ? _formState.errors : {},\n      isSubmitSuccessful: keepStateOptions.keepIsSubmitSuccessful\n        ? _formState.isSubmitSuccessful\n        : false,\n      isSubmitting: false,\n    });\n  };\n\n  const reset: UseFormReset<TFieldValues> = (formValues, keepStateOptions) =>\n    _reset(\n      isFunction(formValues)\n        ? (formValues as Function)(_formValues as TFieldValues)\n        : formValues,\n      keepStateOptions,\n    );\n\n  const setFocus: UseFormSetFocus<TFieldValues> = (name, options = {}) => {\n    const field = get(_fields, name);\n    const fieldReference = field && field._f;\n\n    if (fieldReference) {\n      const fieldRef = fieldReference.refs\n        ? fieldReference.refs[0]\n        : fieldReference.ref;\n\n      if (fieldRef.focus) {\n        fieldRef.focus();\n        options.shouldSelect &&\n          isFunction(fieldRef.select) &&\n          fieldRef.select();\n      }\n    }\n  };\n\n  const _setFormState = (\n    updatedFormState: Partial<FormState<TFieldValues>>,\n  ) => {\n    _formState = {\n      ..._formState,\n      ...updatedFormState,\n    };\n  };\n\n  const _resetDefaultValues = () =>\n    isFunction(_options.defaultValues) &&\n    (_options.defaultValues as Function)().then((values: TFieldValues) => {\n      reset(values, _options.resetOptions);\n      _subjects.state.next({\n        isLoading: false,\n      });\n    });\n\n  const methods = {\n    control: {\n      register,\n      unregister,\n      getFieldState,\n      handleSubmit,\n      setError,\n      _subscribe,\n      _runSchema,\n      _focusError,\n      _getWatch,\n      _getDirty,\n      _setValid,\n      _setFieldArray,\n      _setDisabledField,\n      _setErrors,\n      _getFieldArray,\n      _reset,\n      _resetDefaultValues,\n      _removeUnmounted,\n      _disableForm,\n      _subjects,\n      _proxyFormState,\n      get _fields() {\n        return _fields;\n      },\n      get _formValues() {\n        return _formValues;\n      },\n      get _state() {\n        return _state;\n      },\n      set _state(value) {\n        _state = value;\n      },\n      get _defaultValues() {\n        return _defaultValues;\n      },\n      get _names() {\n        return _names;\n      },\n      set _names(value) {\n        _names = value;\n      },\n      get _formState() {\n        return _formState;\n      },\n      get _options() {\n        return _options;\n      },\n      set _options(value) {\n        _options = {\n          ..._options,\n          ...value,\n        };\n      },\n    },\n    subscribe,\n    trigger,\n    register,\n    handleSubmit,\n    watch,\n    setValue,\n    getValues,\n    reset,\n    resetField,\n    clearErrors,\n    unregister,\n    setError,\n    setFocus,\n    getFieldState,\n  };\n\n  return {\n    ...methods,\n    formControl: methods,\n  };\n}\n", "import { Field } from '../types';\n\nexport default (options: Field['_f']) =>\n  options.mount &&\n  (options.required ||\n    options.min ||\n    options.max ||\n    options.maxLength ||\n    options.minLength ||\n    options.pattern ||\n    options.validate);\n", "import { ValidationModeFlags } from '../types';\n\nexport default (\n  isBlurEvent: boolean,\n  isTouched: boolean,\n  isSubmitted: boolean,\n  reValidateMode: {\n    isOnBlur: boolean;\n    isOnChange: boolean;\n  },\n  mode: Partial<ValidationModeFlags>,\n) => {\n  if (mode.isOnAll) {\n    return false;\n  } else if (!isSubmitted && mode.isOnTouch) {\n    return !(isTouched || isBlurEvent);\n  } else if (isSubmitted ? reValidateMode.isOnBlur : mode.isOnBlur) {\n    return !isBlurEvent;\n  } else if (isSubmitted ? reValidateMode.isOnChange : mode.isOnChange) {\n    return isBlurEvent;\n  }\n  return true;\n};\n", "import convertToArrayPayload from '../utils/convertToArrayPayload';\n\nexport default <T extends string | readonly string[] | undefined>(\n  name?: T,\n  signalName?: string,\n  exact?: boolean,\n) =>\n  !name ||\n  !signalName ||\n  name === signalName ||\n  convertToArrayPayload(name).some(\n    (currentName) =>\n      currentName &&\n      (exact\n        ? currentName === signalName\n        : currentName.startsWith(signalName) ||\n          signalName.startsWith(currentName)),\n  );\n", "import { FieldElement } from '../types';\n\nimport isCheckBoxInput from './isCheckBoxInput';\nimport isRadioInput from './isRadioInput';\n\nexport default (ref: FieldElement): ref is HTMLInputElement =>\n  isRadioInput(ref) || isCheckBoxInput(ref);\n", "import compact from '../utils/compact';\nimport get from '../utils/get';\nimport unset from '../utils/unset';\n\nexport default <T>(ref: T, name: string) =>\n  !compact(get(ref, name)).length && unset(ref, name);\n", "export default () => {\n  const d =\n    typeof performance === 'undefined' ? Date.now() : performance.now() * 1000;\n\n  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {\n    const r = (Math.random() * 16 + d) % 16 | 0;\n\n    return (c == 'x' ? r : (r & 0x3) | 0x8).toString(16);\n  });\n};\n", "import { FieldArrayMethodProps, InternalFieldName } from '../types';\nimport isUndefined from '../utils/isUndefined';\n\nexport default (\n  name: InternalFieldName,\n  index: number,\n  options: FieldArrayMethodProps = {},\n): string =>\n  options.shouldFocus || isUndefined(options.shouldFocus)\n    ? options.focusName ||\n      `${name}.${isUndefined(options.focusIndex) ? index : options.focusIndex}.`\n    : '';\n", "import convertToArrayPayload from './convertToArrayPayload';\n\nexport default <T>(data: T[], value: T | T[]): T[] => [\n  ...data,\n  ...convertToArrayPayload(value),\n];\n", "export default <T>(value: T | T[]): undefined[] | undefined =>\n  Array.isArray(value) ? value.map(() => undefined) : undefined;\n", "import convertToArrayPayload from './convertToArrayPayload';\n\nexport default function insert<T>(data: T[], index: number): (T | undefined)[];\nexport default function insert<T>(\n  data: T[],\n  index: number,\n  value: T | T[],\n): T[];\nexport default function insert<T>(\n  data: T[],\n  index: number,\n  value?: T | T[],\n): (T | undefined)[] {\n  return [\n    ...data.slice(0, index),\n    ...convertToArrayPayload(value),\n    ...data.slice(index),\n  ];\n}\n", "import isUndefined from './isUndefined';\n\nexport default <T>(\n  data: (T | undefined)[],\n  from: number,\n  to: number,\n): (T | undefined)[] => {\n  if (!Array.isArray(data)) {\n    return [];\n  }\n\n  if (isUndefined(data[to])) {\n    data[to] = undefined;\n  }\n  data.splice(to, 0, data.splice(from, 1)[0]);\n\n  return data;\n};\n", "import convertToArrayPayload from './convertToArrayPayload';\n\nexport default <T>(data: T[], value: T | T[]): T[] => [\n  ...convertToArrayPayload(value),\n  ...convertToArrayPayload(data),\n];\n", "import compact from './compact';\nimport convertToArrayPayload from './convertToArrayPayload';\nimport isUndefined from './isUndefined';\n\nfunction removeAtIndexes<T>(data: T[], indexes: number[]): T[] {\n  let i = 0;\n  const temp = [...data];\n\n  for (const index of indexes) {\n    temp.splice(index - i, 1);\n    i++;\n  }\n\n  return compact(temp).length ? temp : [];\n}\n\nexport default <T>(data: T[], index?: number | number[]): T[] =>\n  isUndefined(index)\n    ? []\n    : removeAtIndexes(\n        data,\n        (convertToArrayPayload(index) as number[]).sort((a, b) => a - b),\n      );\n", "export default <T>(data: T[], indexA: number, indexB: number): void => {\n  [data[indexA], data[indexB]] = [data[indexB], data[indexA]];\n};\n", "export default <T>(fieldValues: T[], index: number, value: T) => {\n  fieldValues[index] = value;\n  return fieldValues;\n};\n", "import React from 'react';\n\nimport generateId from './logic/generateId';\nimport getFocusFieldName from './logic/getFocusFieldName';\nimport getValidationModes from './logic/getValidationModes';\nimport isWatched from './logic/isWatched';\nimport iterateFieldsByAction from './logic/iterateFieldsByAction';\nimport updateFieldArrayRootError from './logic/updateFieldArrayRootError';\nimport validateField from './logic/validateField';\nimport appendAt from './utils/append';\nimport cloneObject from './utils/cloneObject';\nimport convertToArrayPayload from './utils/convertToArrayPayload';\nimport fillEmptyArray from './utils/fillEmptyArray';\nimport get from './utils/get';\nimport insertAt from './utils/insert';\nimport isEmptyObject from './utils/isEmptyObject';\nimport moveArrayAt from './utils/move';\nimport prependAt from './utils/prepend';\nimport removeArrayAt from './utils/remove';\nimport set from './utils/set';\nimport swapArrayAt from './utils/swap';\nimport unset from './utils/unset';\nimport updateAt from './utils/update';\nimport { VALIDATION_MODE } from './constants';\nimport {\n  Control,\n  Field,\n  FieldArray,\n  FieldArrayMethodProps,\n  FieldArrayPath,\n  FieldArrayWithId,\n  FieldErrors,\n  FieldPath,\n  FieldValues,\n  FormState,\n  InternalFieldName,\n  RegisterOptions,\n  UseFieldArrayProps,\n  UseFieldArrayReturn,\n} from './types';\nimport { useFormContext } from './useFormContext';\n\n/**\n * A custom hook that exposes convenient methods to perform operations with a list of dynamic inputs that need to be appended, updated, removed etc. • [Demo](https://codesandbox.io/s/react-hook-form-usefieldarray-ssugn) • [Video](https://youtu.be/4MrbfGSFY2A)\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/usefieldarray) • [Demo](https://codesandbox.io/s/react-hook-form-usefieldarray-ssugn)\n *\n * @param props - useFieldArray props\n *\n * @returns methods - functions to manipulate with the Field Arrays (dynamic inputs) {@link UseFieldArrayReturn}\n *\n * @example\n * ```tsx\n * function App() {\n *   const { register, control, handleSubmit, reset, trigger, setError } = useForm({\n *     defaultValues: {\n *       test: []\n *     }\n *   });\n *   const { fields, append } = useFieldArray({\n *     control,\n *     name: \"test\"\n *   });\n *\n *   return (\n *     <form onSubmit={handleSubmit(data => console.log(data))}>\n *       {fields.map((item, index) => (\n *          <input key={item.id} {...register(`test.${index}.firstName`)}  />\n *       ))}\n *       <button type=\"button\" onClick={() => append({ firstName: \"bill\" })}>\n *         append\n *       </button>\n *       <input type=\"submit\" />\n *     </form>\n *   );\n * }\n * ```\n */\nexport function useFieldArray<\n  TFieldValues extends FieldValues = FieldValues,\n  TFieldArrayName extends\n    FieldArrayPath<TFieldValues> = FieldArrayPath<TFieldValues>,\n  TKeyName extends string = 'id',\n  TTransformedValues = TFieldValues,\n>(\n  props: UseFieldArrayProps<\n    TFieldValues,\n    TFieldArrayName,\n    TKeyName,\n    TTransformedValues\n  >,\n): UseFieldArrayReturn<TFieldValues, TFieldArrayName, TKeyName> {\n  const methods = useFormContext();\n  const {\n    control = methods.control,\n    name,\n    keyName = 'id',\n    shouldUnregister,\n    rules,\n  } = props;\n  const [fields, setFields] = React.useState(control._getFieldArray(name));\n  const ids = React.useRef<string[]>(\n    control._getFieldArray(name).map(generateId),\n  );\n  const _fieldIds = React.useRef(fields);\n  const _name = React.useRef(name);\n  const _actioned = React.useRef(false);\n\n  _name.current = name;\n  _fieldIds.current = fields;\n  control._names.array.add(name);\n\n  rules &&\n    (control as Control<TFieldValues, any, TTransformedValues>).register(\n      name as FieldPath<TFieldValues>,\n      rules as RegisterOptions<TFieldValues>,\n    );\n\n  React.useEffect(\n    () =>\n      control._subjects.array.subscribe({\n        next: ({\n          values,\n          name: fieldArrayName,\n        }: {\n          values?: FieldValues;\n          name?: InternalFieldName;\n        }) => {\n          if (fieldArrayName === _name.current || !fieldArrayName) {\n            const fieldValues = get(values, _name.current);\n            if (Array.isArray(fieldValues)) {\n              setFields(fieldValues);\n              ids.current = fieldValues.map(generateId);\n            }\n          }\n        },\n      }).unsubscribe,\n    [control],\n  );\n\n  const updateValues = React.useCallback(\n    <\n      T extends Partial<\n        FieldArrayWithId<TFieldValues, TFieldArrayName, TKeyName>\n      >[],\n    >(\n      updatedFieldArrayValues: T,\n    ) => {\n      _actioned.current = true;\n      control._setFieldArray(name, updatedFieldArrayValues);\n    },\n    [control, name],\n  );\n\n  const append = (\n    value:\n      | Partial<FieldArray<TFieldValues, TFieldArrayName>>\n      | Partial<FieldArray<TFieldValues, TFieldArrayName>>[],\n    options?: FieldArrayMethodProps,\n  ) => {\n    const appendValue = convertToArrayPayload(cloneObject(value));\n    const updatedFieldArrayValues = appendAt(\n      control._getFieldArray(name),\n      appendValue,\n    );\n    control._names.focus = getFocusFieldName(\n      name,\n      updatedFieldArrayValues.length - 1,\n      options,\n    );\n    ids.current = appendAt(ids.current, appendValue.map(generateId));\n    updateValues(updatedFieldArrayValues);\n    setFields(updatedFieldArrayValues);\n    control._setFieldArray(name, updatedFieldArrayValues, appendAt, {\n      argA: fillEmptyArray(value),\n    });\n  };\n\n  const prepend = (\n    value:\n      | Partial<FieldArray<TFieldValues, TFieldArrayName>>\n      | Partial<FieldArray<TFieldValues, TFieldArrayName>>[],\n    options?: FieldArrayMethodProps,\n  ) => {\n    const prependValue = convertToArrayPayload(cloneObject(value));\n    const updatedFieldArrayValues = prependAt(\n      control._getFieldArray(name),\n      prependValue,\n    );\n    control._names.focus = getFocusFieldName(name, 0, options);\n    ids.current = prependAt(ids.current, prependValue.map(generateId));\n    updateValues(updatedFieldArrayValues);\n    setFields(updatedFieldArrayValues);\n    control._setFieldArray(name, updatedFieldArrayValues, prependAt, {\n      argA: fillEmptyArray(value),\n    });\n  };\n\n  const remove = (index?: number | number[]) => {\n    const updatedFieldArrayValues: Partial<\n      FieldArrayWithId<TFieldValues, TFieldArrayName, TKeyName>\n    >[] = removeArrayAt(control._getFieldArray(name), index);\n    ids.current = removeArrayAt(ids.current, index);\n    updateValues(updatedFieldArrayValues);\n    setFields(updatedFieldArrayValues);\n    !Array.isArray(get(control._fields, name)) &&\n      set(control._fields, name, undefined);\n    control._setFieldArray(name, updatedFieldArrayValues, removeArrayAt, {\n      argA: index,\n    });\n  };\n\n  const insert = (\n    index: number,\n    value:\n      | Partial<FieldArray<TFieldValues, TFieldArrayName>>\n      | Partial<FieldArray<TFieldValues, TFieldArrayName>>[],\n    options?: FieldArrayMethodProps,\n  ) => {\n    const insertValue = convertToArrayPayload(cloneObject(value));\n    const updatedFieldArrayValues = insertAt(\n      control._getFieldArray(name),\n      index,\n      insertValue,\n    );\n    control._names.focus = getFocusFieldName(name, index, options);\n    ids.current = insertAt(ids.current, index, insertValue.map(generateId));\n    updateValues(updatedFieldArrayValues);\n    setFields(updatedFieldArrayValues);\n    control._setFieldArray(name, updatedFieldArrayValues, insertAt, {\n      argA: index,\n      argB: fillEmptyArray(value),\n    });\n  };\n\n  const swap = (indexA: number, indexB: number) => {\n    const updatedFieldArrayValues = control._getFieldArray(name);\n    swapArrayAt(updatedFieldArrayValues, indexA, indexB);\n    swapArrayAt(ids.current, indexA, indexB);\n    updateValues(updatedFieldArrayValues);\n    setFields(updatedFieldArrayValues);\n    control._setFieldArray(\n      name,\n      updatedFieldArrayValues,\n      swapArrayAt,\n      {\n        argA: indexA,\n        argB: indexB,\n      },\n      false,\n    );\n  };\n\n  const move = (from: number, to: number) => {\n    const updatedFieldArrayValues = control._getFieldArray(name);\n    moveArrayAt(updatedFieldArrayValues, from, to);\n    moveArrayAt(ids.current, from, to);\n    updateValues(updatedFieldArrayValues);\n    setFields(updatedFieldArrayValues);\n    control._setFieldArray(\n      name,\n      updatedFieldArrayValues,\n      moveArrayAt,\n      {\n        argA: from,\n        argB: to,\n      },\n      false,\n    );\n  };\n\n  const update = (\n    index: number,\n    value: FieldArray<TFieldValues, TFieldArrayName>,\n  ) => {\n    const updateValue = cloneObject(value);\n    const updatedFieldArrayValues = updateAt(\n      control._getFieldArray<\n        FieldArrayWithId<TFieldValues, TFieldArrayName, TKeyName>\n      >(name),\n      index,\n      updateValue as FieldArrayWithId<TFieldValues, TFieldArrayName, TKeyName>,\n    );\n    ids.current = [...updatedFieldArrayValues].map((item, i) =>\n      !item || i === index ? generateId() : ids.current[i],\n    );\n    updateValues(updatedFieldArrayValues);\n    setFields([...updatedFieldArrayValues]);\n    control._setFieldArray(\n      name,\n      updatedFieldArrayValues,\n      updateAt,\n      {\n        argA: index,\n        argB: updateValue,\n      },\n      true,\n      false,\n    );\n  };\n\n  const replace = (\n    value:\n      | Partial<FieldArray<TFieldValues, TFieldArrayName>>\n      | Partial<FieldArray<TFieldValues, TFieldArrayName>>[],\n  ) => {\n    const updatedFieldArrayValues = convertToArrayPayload(cloneObject(value));\n    ids.current = updatedFieldArrayValues.map(generateId);\n    updateValues([...updatedFieldArrayValues]);\n    setFields([...updatedFieldArrayValues]);\n    control._setFieldArray(\n      name,\n      [...updatedFieldArrayValues],\n      <T>(data: T): T => data,\n      {},\n      true,\n      false,\n    );\n  };\n\n  React.useEffect(() => {\n    control._state.action = false;\n\n    isWatched(name, control._names) &&\n      control._subjects.state.next({\n        ...control._formState,\n      } as FormState<TFieldValues>);\n\n    if (\n      _actioned.current &&\n      (!getValidationModes(control._options.mode).isOnSubmit ||\n        control._formState.isSubmitted) &&\n      !getValidationModes(control._options.reValidateMode).isOnSubmit\n    ) {\n      if (control._options.resolver) {\n        control._runSchema([name]).then((result) => {\n          const error = get(result.errors, name);\n          const existingError = get(control._formState.errors, name);\n\n          if (\n            existingError\n              ? (!error && existingError.type) ||\n                (error &&\n                  (existingError.type !== error.type ||\n                    existingError.message !== error.message))\n              : error && error.type\n          ) {\n            error\n              ? set(control._formState.errors, name, error)\n              : unset(control._formState.errors, name);\n            control._subjects.state.next({\n              errors: control._formState.errors as FieldErrors<TFieldValues>,\n            });\n          }\n        });\n      } else {\n        const field: Field = get(control._fields, name);\n        if (\n          field &&\n          field._f &&\n          !(\n            getValidationModes(control._options.reValidateMode).isOnSubmit &&\n            getValidationModes(control._options.mode).isOnSubmit\n          )\n        ) {\n          validateField(\n            field,\n            control._names.disabled,\n            control._formValues,\n            control._options.criteriaMode === VALIDATION_MODE.all,\n            control._options.shouldUseNativeValidation,\n            true,\n          ).then(\n            (error) =>\n              !isEmptyObject(error) &&\n              control._subjects.state.next({\n                errors: updateFieldArrayRootError(\n                  control._formState.errors as FieldErrors<TFieldValues>,\n                  error,\n                  name,\n                ) as FieldErrors<TFieldValues>,\n              }),\n          );\n        }\n      }\n    }\n\n    control._subjects.state.next({\n      name,\n      values: cloneObject(control._formValues) as TFieldValues,\n    });\n\n    control._names.focus &&\n      iterateFieldsByAction(control._fields, (ref, key: string) => {\n        if (\n          control._names.focus &&\n          key.startsWith(control._names.focus) &&\n          ref.focus\n        ) {\n          ref.focus();\n          return 1;\n        }\n        return;\n      });\n\n    control._names.focus = '';\n\n    control._setValid();\n    _actioned.current = false;\n  }, [fields, name, control]);\n\n  React.useEffect(() => {\n    !get(control._formValues, name) && control._setFieldArray(name);\n\n    return () => {\n      const updateMounted = (name: InternalFieldName, value: boolean) => {\n        const field: Field = get(control._fields, name);\n        if (field && field._f) {\n          field._f.mount = value;\n        }\n      };\n\n      control._options.shouldUnregister || shouldUnregister\n        ? control.unregister(name as FieldPath<TFieldValues>)\n        : updateMounted(name, false);\n    };\n  }, [name, control, keyName, shouldUnregister]);\n\n  return {\n    swap: React.useCallback(swap, [updateValues, name, control]),\n    move: React.useCallback(move, [updateValues, name, control]),\n    prepend: React.useCallback(prepend, [updateValues, name, control]),\n    append: React.useCallback(append, [updateValues, name, control]),\n    remove: React.useCallback(remove, [updateValues, name, control]),\n    insert: React.useCallback(insert, [updateValues, name, control]),\n    update: React.useCallback(update, [updateValues, name, control]),\n    replace: React.useCallback(replace, [updateValues, name, control]),\n    fields: React.useMemo(\n      () =>\n        fields.map((field, index) => ({\n          ...field,\n          [keyName]: ids.current[index] || generateId(),\n        })) as FieldArrayWithId<TFieldValues, TFieldArrayName, TKeyName>[],\n      [fields, keyName],\n    ),\n  };\n}\n", "import React from 'react';\n\nimport getProxyFormState from './logic/getProxyFormState';\nimport deepEqual from './utils/deepEqual';\nimport isFunction from './utils/isFunction';\nimport { createFormControl } from './logic';\nimport { FieldValues, FormState, UseFormProps, UseFormReturn } from './types';\nimport { useIsomorphicLayoutEffect } from './useIsomorphicLayoutEffect';\n\n/**\n * Custom hook to manage the entire form.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/useform) • [Demo](https://codesandbox.io/s/react-hook-form-get-started-ts-5ksmm) • [Video](https://www.youtube.com/watch?v=RkXv4AXXC_4)\n *\n * @param props - form configuration and validation parameters.\n *\n * @returns methods - individual functions to manage the form state. {@link UseFormReturn}\n *\n * @example\n * ```tsx\n * function App() {\n *   const { register, handleSubmit, watch, formState: { errors } } = useForm();\n *   const onSubmit = data => console.log(data);\n *\n *   console.log(watch(\"example\"));\n *\n *   return (\n *     <form onSubmit={handleSubmit(onSubmit)}>\n *       <input defaultValue=\"test\" {...register(\"example\")} />\n *       <input {...register(\"exampleRequired\", { required: true })} />\n *       {errors.exampleRequired && <span>This field is required</span>}\n *       <button>Submit</button>\n *     </form>\n *   );\n * }\n * ```\n */\nexport function useForm<\n  TFieldValues extends FieldValues = FieldValues,\n  TContext = any,\n  TTransformedValues = TFieldValues,\n>(\n  props: UseFormProps<TFieldValues, TContext, TTransformedValues> = {},\n): UseFormReturn<TFieldValues, TContext, TTransformedValues> {\n  const _formControl = React.useRef<\n    UseFormReturn<TFieldValues, TContext, TTransformedValues> | undefined\n  >(undefined);\n  const _values = React.useRef<typeof props.values>(undefined);\n  const [formState, updateFormState] = React.useState<FormState<TFieldValues>>({\n    isDirty: false,\n    isValidating: false,\n    isLoading: isFunction(props.defaultValues),\n    isSubmitted: false,\n    isSubmitting: false,\n    isSubmitSuccessful: false,\n    isValid: false,\n    submitCount: 0,\n    dirtyFields: {},\n    touchedFields: {},\n    validatingFields: {},\n    errors: props.errors || {},\n    disabled: props.disabled || false,\n    isReady: false,\n    defaultValues: isFunction(props.defaultValues)\n      ? undefined\n      : props.defaultValues,\n  });\n\n  if (!_formControl.current) {\n    _formControl.current = {\n      ...(props.formControl ? props.formControl : createFormControl(props)),\n      formState,\n    };\n\n    if (\n      props.formControl &&\n      props.defaultValues &&\n      !isFunction(props.defaultValues)\n    ) {\n      props.formControl.reset(props.defaultValues, props.resetOptions);\n    }\n  }\n\n  const control = _formControl.current.control;\n  control._options = props;\n\n  useIsomorphicLayoutEffect(() => {\n    const sub = control._subscribe({\n      formState: control._proxyFormState,\n      callback: () => updateFormState({ ...control._formState }),\n      reRenderRoot: true,\n    });\n\n    updateFormState((data) => ({\n      ...data,\n      isReady: true,\n    }));\n\n    control._formState.isReady = true;\n\n    return sub;\n  }, [control]);\n\n  React.useEffect(\n    () => control._disableForm(props.disabled),\n    [control, props.disabled],\n  );\n\n  React.useEffect(() => {\n    if (props.mode) {\n      control._options.mode = props.mode;\n    }\n    if (props.reValidateMode) {\n      control._options.reValidateMode = props.reValidateMode;\n    }\n  }, [control, props.mode, props.reValidateMode]);\n\n  React.useEffect(() => {\n    if (props.errors) {\n      control._setErrors(props.errors);\n      control._focusError();\n    }\n  }, [control, props.errors]);\n\n  React.useEffect(() => {\n    props.shouldUnregister &&\n      control._subjects.state.next({\n        values: control._getWatch(),\n      });\n  }, [control, props.shouldUnregister]);\n\n  React.useEffect(() => {\n    if (control._proxyFormState.isDirty) {\n      const isDirty = control._getDirty();\n      if (isDirty !== formState.isDirty) {\n        control._subjects.state.next({\n          isDirty,\n        });\n      }\n    }\n  }, [control, formState.isDirty]);\n\n  React.useEffect(() => {\n    if (props.values && !deepEqual(props.values, _values.current)) {\n      control._reset(props.values, control._options.resetOptions);\n      _values.current = props.values;\n      updateFormState((state) => ({ ...state }));\n    } else {\n      control._resetDefaultValues();\n    }\n  }, [control, props.values]);\n\n  React.useEffect(() => {\n    if (!control._state.mount) {\n      control._setValid();\n      control._state.mount = true;\n    }\n\n    if (control._state.watch) {\n      control._state.watch = false;\n      control._subjects.state.next({ ...control._formState });\n    }\n\n    control._removeUnmounted();\n  });\n\n  _formControl.current.formState = getProxyFormState(formState, control);\n\n  return _formControl.current;\n}\n"], "names": ["isCheckBoxInput", "element", "type", "isDateObject", "value", "Date", "isNullOrUndefined", "isObjectType", "isObject", "Array", "isArray", "getEventValue", "event", "target", "checked", "isNameInFieldArray", "names", "name", "has", "substring", "search", "getNodeParentName", "isWeb", "window", "HTMLElement", "document", "cloneObject", "data", "copy", "isFileListInstance", "FileList", "Set", "Blob", "tempObject", "prototypeCopy", "constructor", "prototype", "hasOwnProperty", "isPlainObject", "key", "compact", "filter", "Boolean", "isUndefined", "val", "undefined", "get", "object", "path", "defaultValue", "result", "split", "reduce", "isBoolean", "is<PERSON>ey", "test", "stringToPath", "input", "replace", "set", "index", "temp<PERSON>ath", "length", "lastIndex", "newValue", "objValue", "isNaN", "EVENTS", "VALIDATION_MODE", "INPUT_VALIDATION_RULES", "HookFormContext", "React", "createContext", "useFormContext", "useContext", "getProxyFormState", "formState", "control", "localProxyFormState", "isRoot", "defaultValues", "_defaultValues", "Object", "defineProperty", "_key", "_proxyFormState", "useIsomorphicLayoutEffect", "useLayoutEffect", "useEffect", "useFormState", "props", "methods", "disabled", "exact", "updateFormState", "useState", "_formState", "_localProxyFormState", "useRef", "isDirty", "isLoading", "dirtyFields", "touchedFields", "validatingFields", "isValidating", "<PERSON><PERSON><PERSON><PERSON>", "errors", "_subscribe", "current", "callback", "_setValid", "useMemo", "isString", "generateWatchOutput", "_names", "formValues", "isGlobal", "watch", "add", "map", "fieldName", "watchAll", "useWatch", "_defaultValue", "updateValue", "_getWatch", "values", "_formValues", "_removeUnmounted", "useController", "shouldUnregister", "isArrayField", "array", "_props", "_registerProps", "register", "rules", "fieldState", "defineProperties", "invalid", "enumerable", "isTouched", "error", "onChange", "useCallback", "onBlur", "ref", "elm", "field", "_fields", "_f", "focus", "select", "setCustomValidity", "message", "reportValidity", "_shouldUnregisterField", "_options", "updateMounted", "mount", "_state", "action", "unregister", "_setDisabledField", "flatten", "obj", "output", "keys", "nested", "nested<PERSON><PERSON>", "POST_REQUEST", "appendErrors", "validateAllFieldCriteria", "types", "convertToArrayPayload", "createSubject", "_observers", "observers", "next", "observer", "subscribe", "push", "unsubscribe", "o", "isPrimitive", "deepEqual", "object1", "object2", "getTime", "keys1", "keys2", "val1", "includes", "val2", "isEmptyObject", "isFileInput", "isFunction", "isHTMLElement", "owner", "ownerDocument", "defaultView", "isMultipleSelect", "isRadioInput", "live", "isConnected", "unset", "paths", "childObject", "updatePath", "slice", "baseGet", "isEmptyArray", "objectHasFunction", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fields", "isParentNodeArray", "getDirtyFieldsFromDefaultValues", "dirtyField<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "getDirty<PERSON>ields", "defaultResult", "validResult", "getCheckboxValue", "options", "option", "attributes", "getFieldValueAs", "valueAsNumber", "valueAsDate", "setValueAs", "NaN", "defaultReturn", "getRadioValue", "previous", "getFieldValue", "files", "refs", "selectedOptions", "isCheckBox", "isRegex", "RegExp", "getRuleValue", "rule", "source", "getValidationModes", "mode", "isOnSubmit", "isOnBlur", "isOnChange", "isOnAll", "isOnTouch", "ASYNC_FUNCTION", "isWatched", "isBlurEvent", "some", "watchName", "startsWith", "iterateFieldsByAction", "fieldsNames", "abort<PERSON><PERSON><PERSON>", "current<PERSON><PERSON>", "schemaErrorLookup", "join", "found<PERSON><PERSON>r", "root", "pop", "updateFieldArrayRootError", "fieldArrayErrors", "isMessage", "getValidateError", "every", "getValueAndMessage", "validationData", "validateField", "async", "disabled<PERSON>ieldN<PERSON>s", "shouldUseNativeValidation", "isFieldArray", "required", "max<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "min", "max", "pattern", "validate", "inputValue", "inputRef", "isRadio", "isRadioOrCheckbox", "isEmpty", "appendErrors<PERSON><PERSON><PERSON>", "bind", "getMinMaxMessage", "exceedMax", "maxLengthMessage", "minLengthMessage", "maxType", "minType", "exceedMin", "maxOutput", "minOutput", "valueDate", "convertTimeToDate", "time", "toDateString", "isTime", "isWeek", "valueNumber", "maxLengthOutput", "minLengthOutput", "patternValue", "match", "validateError", "validationResult", "defaultOptions", "reValidateMode", "shouldFocusError", "createFormControl", "submitCount", "isReady", "isSubmitted", "isSubmitting", "isSubmitSuccessful", "delayError<PERSON><PERSON><PERSON>", "unMount", "timer", "_proxySubscribeFormState", "_subjects", "state", "shouldDisplayAllAssociatedErrors", "criteriaMode", "shouldUpdateValid", "resolver", "_runSchema", "executeBuiltInValidation", "_updateIsValidating", "from", "for<PERSON>ach", "updateValidAndValue", "shouldSkipSetValueAs", "defaultChecked", "setFieldValue", "updateTouchAndDirty", "fieldValue", "should<PERSON>irty", "shouldRender", "shouldUpdateField", "is<PERSON>revious<PERSON><PERSON>y", "_getDirty", "isCurrentFieldPristine", "isPreviousFieldTouched", "shouldRenderByError", "previousFieldError", "delayError", "updateErrors", "wait", "clearTimeout", "setTimeout", "updatedFormState", "context", "getResolverOptions", "should<PERSON>nly<PERSON><PERSON><PERSON><PERSON>d", "valid", "isFieldArrayRoot", "isPromiseFunction", "fieldReference", "find", "validateFunction", "fieldError", "getV<PERSON>ues", "optionRef", "selected", "checkboxRef", "radioRef", "shouldTouch", "shouldValidate", "trigger", "set<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "setValue", "cloneValue", "isFieldValueUpdated", "_updateIsFieldValueUpdated", "Number", "validationModeBeforeSubmit", "validationModeAfterSubmit", "shouldSkipValidation", "deps", "skipValidation", "watched", "previousErrorLookupResult", "errorLookupResult", "_focusInput", "fieldNames", "executeSchemaAndUpdateState", "Promise", "all", "shouldFocus", "getFieldState", "setError", "currentError", "currentRef", "restOfErrorTree", "signalName", "currentName", "formStateData", "shouldRenderFormState", "_setFormState", "reRenderRoot", "delete", "keepValue", "keepError", "keep<PERSON>irty", "keepTouched", "keepIsValidating", "keepDefaultValue", "keepIsValid", "disabledIsDefined", "progressive", "fieldRef", "querySelectorAll", "radioOrCheckbox", "_focusError", "handleSubmit", "onValid", "onInvalid", "e", "onValidError", "preventDefault", "persist", "field<PERSON><PERSON><PERSON>", "size", "_reset", "keepStateOptions", "updatedValues", "cloneUpdatedValues", "isEmptyResetValues", "keepDefaultValues", "keepV<PERSON>ues", "keepDirtyV<PERSON>ues", "fieldsToCheck", "form", "closest", "reset", "keepSubmitCount", "keepIsSubmitted", "keepErrors", "keepIsSubmitSuccessful", "_setFieldArray", "method", "args", "shouldSetValues", "shouldUpdateFieldsAndState", "argA", "argB", "unsetEmptyArray", "_setErrors", "_getFieldArray", "_resetDefaultValues", "then", "resetOptions", "_disableForm", "payload", "reset<PERSON>ield", "clearErrors", "inputName", "setFocus", "shouldSelect", "formControl", "generateId", "d", "performance", "now", "c", "r", "Math", "random", "toString", "getFocusFieldName", "focusName", "focusIndex", "appendAt", "fillEmptyArray", "insert", "moveArrayAt", "to", "splice", "prependAt", "removeArrayAt", "indexes", "i", "temp", "removeAtIndexes", "sort", "a", "b", "swapArrayAt", "indexA", "indexB", "updateAt", "render", "mounted", "setMounted", "onSubmit", "children", "headers", "encType", "onError", "onSuccess", "validateStatus", "rest", "submit", "<PERSON><PERSON><PERSON><PERSON>", "formData", "FormData", "formDataJson", "JSON", "stringify", "_a", "flattenForm<PERSON><PERSON>ues", "append", "shouldStringifySubmissionData", "response", "fetch", "String", "body", "status", "createElement", "Fragment", "noValidate", "Provider", "keyName", "setFields", "ids", "_fieldIds", "_name", "_actioned", "fieldArrayName", "updateValues", "updatedFieldArrayValues", "existingError", "swap", "move", "prepend", "prependValue", "appendValue", "remove", "insertValue", "insertAt", "update", "item", "_formControl", "_values", "sub"], "mappings": "siBAEAA,EAAgBC,GACG,aAAjBA,EAAQC,KCHVC,EAAgBC,GAAkCA,aAAiBC,KCAnEC,EAAgBF,GAAuD,MAATA,ECGvD,MAAMG,EAAgBH,GACV,iBAAVA,EAET,IAAAI,EAAkCJ,IAC/BE,EAAkBF,KAClBK,MAAMC,QAAQN,IACfG,EAAaH,KACZD,EAAaC,GCLDO,EAACC,GACdJ,EAASI,IAAWA,EAAgBC,OAChCb,EAAiBY,EAAgBC,QAC9BD,EAAgBC,OAAOC,QACvBF,EAAgBC,OAAOT,MAC1BQ,ECNNG,EAAe,CAACC,EAA+BC,IAC7CD,EAAME,ICLO,CAACD,GACdA,EAAKE,UAAU,EAAGF,EAAKG,OAAO,iBAAmBH,EDIvCI,CAAkBJ,IELfK,EAAkB,oBAAXC,aACU,IAAvBA,OAAOC,aACM,oBAAbC,SCEe,SAAAC,EAAeC,GACrC,IAAIC,EACJ,MAAMlB,EAAUD,MAAMC,QAAQiB,GACxBE,EACgB,oBAAbC,UAA2BH,aAAgBG,SAEpD,GAAIH,aAAgBtB,KAClBuB,EAAO,IAAIvB,KAAKsB,QACX,GAAIA,aAAgBI,IACzBH,EAAO,IAAIG,IAAIJ,OACV,IACHL,IAAUK,aAAgBK,MAAQH,KACnCnB,IAAWF,EAASmB,GAcrB,OAAOA,EAVP,GAFAC,EAAOlB,EAAU,GAAK,CAAE,EAEnBA,GClBM,CAACuB,IACd,MAAMC,EACJD,EAAWE,aAAeF,EAAWE,YAAYC,UAEnD,OACE5B,EAAS0B,IAAkBA,EAAcG,eAAe,gBAAgB,EDavDC,CAAcX,GAG7B,IAAK,MAAMY,KAAOZ,EACZA,EAAKU,eAAeE,KACtBX,EAAKW,GAAOb,EAAYC,EAAKY,UAJjCX,EAAOD,EAYX,OAAOC,CACT,CElCA,IAAAY,EAAwBpC,GACtBK,MAAMC,QAAQN,GAASA,EAAMqC,OAAOC,SAAW,GCDjDC,EAAgBC,QAA2CC,IAARD,ECKnDE,EAAe,CACbC,EACAC,EACAC,KAEA,IAAKD,IAASxC,EAASuC,GACrB,OAAOE,EAGT,MAAMC,EAASV,EAAQQ,EAAKG,MAAM,cAAcC,QAC9C,CAACF,EAAQX,IACPjC,EAAkB4C,GAAUA,EAASA,EAAOX,IAC9CQ,GAGF,OAAOJ,EAAYO,IAAWA,IAAWH,EACrCJ,EAAYI,EAAOC,IACjBC,EACAF,EAAOC,GACTE,CAAM,ECxBZG,EAAgBjD,GAAsD,kBAAVA,ECA7CkD,EAAClD,GAAkB,QAAQmD,KAAKnD,GCE/CoD,EAAgBC,GACdjB,EAAQiB,EAAMC,QAAQ,YAAa,IAAIP,MAAM,UCG/CQ,EAAe,CACbZ,EACAC,EACA5C,KAEA,IAAIwD,GAAU,EACd,MAAMC,EAAWP,EAAMN,GAAQ,CAACA,GAAQQ,EAAaR,GAC/Cc,EAASD,EAASC,OAClBC,EAAYD,EAAS,EAE3B,OAASF,EAAQE,GAAQ,CACvB,MAAMvB,EAAMsB,EAASD,GACrB,IAAII,EAAW5D,EAEf,GAAIwD,IAAUG,EAAW,CACvB,MAAME,EAAWlB,EAAOR,GACxByB,EACExD,EAASyD,IAAaxD,MAAMC,QAAQuD,GAChCA,EACCC,OAAOL,EAASD,EAAQ,IAEvB,CAAE,EADF,GAIV,GAAY,cAARrB,GAA+B,gBAARA,GAAiC,cAARA,EAClD,OAGFQ,EAAOR,GAAOyB,EACdjB,EAASA,EAAOR,KCnCb,MAAM4B,EACL,OADKA,EAEA,WAFAA,EAGH,SAGGC,EACH,SADGA,EAED,WAFCA,EAGD,WAHCA,EAIA,YAJAA,EAKN,MAGMC,EACN,MADMA,EAEN,MAFMA,EAGA,YAHAA,EAIA,YAJAA,EAKF,UALEA,EAMD,WANCA,EAOD,WCjBNC,EAAkBC,EAAMC,cAAoC,MAgCrDC,EAAiB,IAK5BF,EAAMG,WAAWJ,GCtCJ,IAAAK,EAAA,CAKbC,EACAC,EACAC,EACAC,GAAS,KAET,MAAM7B,EAAS,CACb8B,cAAeH,EAAQI,gBAGzB,IAAK,MAAM1C,KAAOqC,EAChBM,OAAOC,eAAejC,EAAQX,EAAK,CACjCO,IAAK,KACH,MAAMsC,EAAO7C,EAOb,OALIsC,EAAQQ,gBAAgBD,KAAUhB,IACpCS,EAAQQ,gBAAgBD,IAASL,GAAUX,GAG7CU,IAAwBA,EAAoBM,IAAQ,GAC7CR,EAAUQ,EAAK,IAK5B,OAAOlC,CAAM,EC9BR,MAAMoC,EACO,oBAAX/D,OAAyBgD,EAAMgB,gBAAkBhB,EAAMiB,UCkC1D,SAAUC,EAIdC,GAEA,MAAMC,EAAUlB,KACVI,QAAEA,EAAUc,EAAQd,QAAOe,SAAEA,EAAQ3E,KAAEA,EAAI4E,MAAEA,GAAUH,GAAS,CAAE,GACjEd,EAAWkB,GAAmBvB,EAAMwB,SAASlB,EAAQmB,YACtDC,EAAuB1B,EAAM2B,OAAO,CACxCC,SAAS,EACTC,WAAW,EACXC,aAAa,EACbC,eAAe,EACfC,kBAAkB,EAClBC,cAAc,EACdC,SAAS,EACTC,QAAQ,IAwBV,OArBApB,GACE,IACET,EAAQ8B,WAAW,CACjB1F,OACA2D,UAAWqB,EAAqBW,QAChCf,QACAgB,SAAWjC,KACRgB,GACCE,EAAgB,IACXjB,EAAQmB,cACRpB,GACH,KAGV,CAAC3D,EAAM2E,EAAUC,IAGnBtB,EAAMiB,WAAU,KACdS,EAAqBW,QAAQH,SAAW5B,EAAQiC,WAAU,EAAK,GAC9D,CAACjC,IAEGN,EAAMwC,SACX,IACEpC,EACEC,EACAC,EACAoB,EAAqBW,SACrB,IAEJ,CAAChC,EAAWC,GAEhB,CCxFA,IAAAmC,EAAgB5G,GAAqD,iBAAVA,ECI5C6G,EAAA,CACbjG,EACAkG,EACAC,EACAC,EACAnE,IAEI+D,EAAShG,IACXoG,GAAYF,EAAOG,MAAMC,IAAItG,GACtB8B,EAAIqE,EAAYnG,EAAOiC,IAG5BxC,MAAMC,QAAQM,GACTA,EAAMuG,KACVC,IACCJ,GAAYF,EAAOG,MAAMC,IAAIE,GAAY1E,EAAIqE,EAAYK,OAK/DJ,IAAaF,EAAOO,UAAW,GAExBN,GCsHH,SAAUO,EACdhC,GAEA,MAAMC,EAAUlB,KACVI,QACJA,EAAUc,EAAQd,QAAO5D,KACzBA,EAAIgC,aACJA,EAAY2C,SACZA,EAAQC,MACRA,GACEH,GAAS,CAAE,EACTiC,EAAgBpD,EAAM2B,OAAOjD,IAC5B7C,EAAOwH,GAAerD,EAAMwB,SACjClB,EAAQgD,UACN5G,EACA0G,EAAcf,UA6BlB,OAzBAtB,GACE,IACET,EAAQ8B,WAAW,CACjB1F,OACA2D,UAAW,CACTkD,QAAQ,GAEVjC,QACAgB,SAAWjC,IACRgB,GACDgC,EACEX,EACEhG,EACA4D,EAAQqC,OACRtC,EAAUkD,QAAUjD,EAAQkD,aAC5B,EACAJ,EAAcf,aAIxB,CAAC3F,EAAM4D,EAASe,EAAUC,IAG5BtB,EAAMiB,WAAU,IAAMX,EAAQmD,qBAEvB5H,CACT,CC7IM,SAAU6H,EAKdvC,GAEA,MAAMC,EAAUlB,KACVxD,KAAEA,EAAI2E,SAAEA,EAAQf,QAAEA,EAAUc,EAAQd,QAAOqD,iBAAEA,GAAqBxC,EAClEyC,EAAepH,EAAmB8D,EAAQqC,OAAOkB,MAAOnH,GACxDb,EAAQsH,EAAS,CACrB7C,UACA5D,OACAgC,aAAcH,EACZ+B,EAAQkD,YACR9G,EACA6B,EAAI+B,EAAQI,eAAgBhE,EAAMyE,EAAMzC,eAE1C4C,OAAO,IAEHjB,EAAYa,EAAa,CAC7BZ,UACA5D,OACA4E,OAAO,IAGHwC,EAAS9D,EAAM2B,OAAOR,GACtB4C,EAAiB/D,EAAM2B,OAC3BrB,EAAQ0D,SAAStH,EAAM,IAClByE,EAAM8C,MACTpI,WACIiD,EAAUqC,EAAME,UAAY,CAAEA,SAAUF,EAAME,UAAa,MAI7D6C,EAAalE,EAAMwC,SACvB,IACE7B,OAAOwD,iBACL,GACA,CACEC,QAAS,CACPC,YAAY,EACZ9F,IAAK,MAAQA,EAAI8B,EAAU8B,OAAQzF,IAErCkF,QAAS,CACPyC,YAAY,EACZ9F,IAAK,MAAQA,EAAI8B,EAAUyB,YAAapF,IAE1C4H,UAAW,CACTD,YAAY,EACZ9F,IAAK,MAAQA,EAAI8B,EAAU0B,cAAerF,IAE5CuF,aAAc,CACZoC,YAAY,EACZ9F,IAAK,MAAQA,EAAI8B,EAAU2B,iBAAkBtF,IAE/C6H,MAAO,CACLF,YAAY,EACZ9F,IAAK,IAAMA,EAAI8B,EAAU8B,OAAQzF,OAIzC,CAAC2D,EAAW3D,IAGR8H,EAAWxE,EAAMyE,aACpBpI,GACC0H,EAAe1B,QAAQmC,SAAS,CAC9BlI,OAAQ,CACNT,MAAOO,EAAcC,GACrBK,KAAMA,GAERf,KAAMiE,KAEV,CAAClD,IAGGgI,EAAS1E,EAAMyE,aACnB,IACEV,EAAe1B,QAAQqC,OAAO,CAC5BpI,OAAQ,CACNT,MAAO0C,EAAI+B,EAAQkD,YAAa9G,GAChCA,KAAMA,GAERf,KAAMiE,KAEV,CAAClD,EAAM4D,EAAQkD,cAGXmB,EAAM3E,EAAMyE,aACfG,IACC,MAAMC,EAAQtG,EAAI+B,EAAQwE,QAASpI,GAE/BmI,GAASD,IACXC,EAAME,GAAGJ,IAAM,CACbK,MAAO,IAAMJ,EAAII,OAASJ,EAAII,QAC9BC,OAAQ,IAAML,EAAIK,QAAUL,EAAIK,SAChCC,kBAAoBC,GAClBP,EAAIM,kBAAkBC,GACxBC,eAAgB,IAAMR,EAAIQ,qBAIhC,CAAC9E,EAAQwE,QAASpI,IAGdmI,EAAQ7E,EAAMwC,SAClB,KAAO,CACL9F,OACAb,WACIiD,EAAUuC,IAAahB,EAAUgB,SACjC,CAAEA,SAAUhB,EAAUgB,UAAYA,GAClC,GACJmD,WACAE,SACAC,SAEF,CAACjI,EAAM2E,EAAUhB,EAAUgB,SAAUmD,EAAUE,EAAQC,EAAK9I,IAoD9D,OAjDAmE,EAAMiB,WAAU,KACd,MAAMoE,EACJ/E,EAAQgF,SAAS3B,kBAAoBA,EAEvCrD,EAAQ0D,SAAStH,EAAM,IAClBoH,EAAOzB,QAAQ4B,SACdnF,EAAUgF,EAAOzB,QAAQhB,UACzB,CAAEA,SAAUyC,EAAOzB,QAAQhB,UAC3B,KAGN,MAAMkE,EAAgB,CAAC7I,EAAyBb,KAC9C,MAAMgJ,EAAetG,EAAI+B,EAAQwE,QAASpI,GAEtCmI,GAASA,EAAME,KACjBF,EAAME,GAAGS,MAAQ3J,IAMrB,GAFA0J,EAAc7I,GAAM,GAEhB2I,EAAwB,CAC1B,MAAMxJ,EAAQsB,EAAYoB,EAAI+B,EAAQgF,SAAS7E,cAAe/D,IAC9D0C,EAAIkB,EAAQI,eAAgBhE,EAAMb,GAC9BuC,EAAYG,EAAI+B,EAAQkD,YAAa9G,KACvC0C,EAAIkB,EAAQkD,YAAa9G,EAAMb,GAMnC,OAFC+H,GAAgBtD,EAAQ0D,SAAStH,GAE3B,MAEHkH,EACIyB,IAA2B/E,EAAQmF,OAAOC,OAC1CL,GAEF/E,EAAQqF,WAAWjJ,GACnB6I,EAAc7I,GAAM,EAAM,CAC/B,GACA,CAACA,EAAM4D,EAASsD,EAAcD,IAEjC3D,EAAMiB,WAAU,KACdX,EAAQsF,kBAAkB,CACxBvE,WACA3E,QACA,GACD,CAAC2E,EAAU3E,EAAM4D,IAEbN,EAAMwC,SACX,KAAO,CACLqC,QACAxE,YACA6D,gBAEF,CAACW,EAAOxE,EAAW6D,GAEvB,CCpLA,MCzCa2B,EAAWC,IACtB,MAAMC,EAAsB,CAAE,EAE9B,IAAK,MAAM/H,KAAO2C,OAAOqF,KAAKF,GAC5B,GAAI9J,EAAa8J,EAAI9H,KAAsB,OAAb8H,EAAI9H,GAAe,CAC/C,MAAMiI,EAASJ,EAAQC,EAAI9H,IAE3B,IAAK,MAAMkI,KAAavF,OAAOqF,KAAKC,GAClCF,EAAO,GAAG/H,KAAOkI,KAAeD,EAAOC,QAGzCH,EAAO/H,GAAO8H,EAAI9H,GAItB,OAAO+H,CAAM,ECbTI,EAAe,OCAN,IAAAC,EAAA,CACb1J,EACA2J,EACAlE,EACAxG,EACAwJ,IAEAkB,EACI,IACKlE,EAAOzF,GACV4J,MAAO,IACDnE,EAAOzF,IAASyF,EAAOzF,GAAO4J,MAAQnE,EAAOzF,GAAO4J,MAAQ,CAAA,EAChE3K,CAACA,GAAOwJ,IAAW,IAGvB,CAAE,ECrBRoB,EAAmB1K,GAAcK,MAAMC,QAAQN,GAASA,EAAQ,CAACA,GCgBjE2K,EAAe,KACb,IAAIC,EAA4B,GAqBhC,MAAO,CACL,aAAIC,GACF,OAAOD,CACR,EACDE,KAvBY9K,IACZ,IAAK,MAAM+K,KAAYH,EACrBG,EAASD,MAAQC,EAASD,KAAK9K,IAsBjCgL,UAlBiBD,IACjBH,EAAWK,KAAKF,GACT,CACLG,YAAa,KACXN,EAAaA,EAAWvI,QAAQ8I,GAAMA,IAAMJ,GAAS,IAezDG,YAVkB,KAClBN,EAAa,EAAE,EAUhB,ECxCHQ,EAAgBpL,GACdE,EAAkBF,KAAWG,EAAaH,GCD9B,SAAUqL,EAAUC,EAAcC,GAC9C,GAAIH,EAAYE,IAAYF,EAAYG,GACtC,OAAOD,IAAYC,EAGrB,GAAIxL,EAAauL,IAAYvL,EAAawL,GACxC,OAAOD,EAAQE,YAAcD,EAAQC,UAGvC,MAAMC,EAAQ3G,OAAOqF,KAAKmB,GACpBI,EAAQ5G,OAAOqF,KAAKoB,GAE1B,GAAIE,EAAM/H,SAAWgI,EAAMhI,OACzB,OAAO,EAGT,IAAK,MAAMvB,KAAOsJ,EAAO,CACvB,MAAME,EAAOL,EAAQnJ,GAErB,IAAKuJ,EAAME,SAASzJ,GAClB,OAAO,EAGT,GAAY,QAARA,EAAe,CACjB,MAAM0J,EAAON,EAAQpJ,GAErB,GACGpC,EAAa4L,IAAS5L,EAAa8L,IACnCzL,EAASuL,IAASvL,EAASyL,IAC3BxL,MAAMC,QAAQqL,IAAStL,MAAMC,QAAQuL,IACjCR,EAAUM,EAAME,GACjBF,IAASE,EAEb,OAAO,GAKb,OAAO,CACT,CCxCA,IAAAC,EAAgB9L,GACdI,EAASJ,KAAW8E,OAAOqF,KAAKnK,GAAO0D,OCHzCqI,EAAgBlM,GACG,SAAjBA,EAAQC,KCHVkM,EAAgBhM,GACG,mBAAVA,ECCMiM,GAACjM,IACd,IAAKkB,EACH,OAAO,EAGT,MAAMgL,EAAQlM,EAAUA,EAAsBmM,cAA6B,EAC3E,OACEnM,aACCkM,GAASA,EAAME,YAAcF,EAAME,YAAYhL,YAAcA,YAAY,ECR/DiL,GAACxM,GACG,oBAAjBA,EAAQC,KCDVwM,GAAgBzM,GACG,UAAjBA,EAAQC,KCCKyM,GAACzD,GAAamD,GAAcnD,IAAQA,EAAI0D,YCsBzC,SAAUC,GAAM9J,EAAaC,GACzC,MAAM8J,EAAQrM,MAAMC,QAAQsC,GACxBA,EACAM,EAAMN,GACJ,CAACA,GACDQ,EAAaR,GAEb+J,EAA+B,IAAjBD,EAAMhJ,OAAef,EA3B3C,SAAiBA,EAAaiK,GAC5B,MAAMlJ,EAASkJ,EAAWC,MAAM,GAAG,GAAInJ,OACvC,IAAIF,EAAQ,EAEZ,KAAOA,EAAQE,GACbf,EAASJ,EAAYI,GAAUa,IAAUb,EAAOiK,EAAWpJ,MAG7D,OAAOb,CACT,CAkBoDmK,CAAQnK,EAAQ+J,GAE5DlJ,EAAQkJ,EAAMhJ,OAAS,EACvBvB,EAAMuK,EAAMlJ,GAclB,OAZImJ,UACKA,EAAYxK,GAIT,IAAVqB,IACEpD,EAASuM,IAAgBb,EAAca,IACtCtM,MAAMC,QAAQqM,IA5BrB,SAAsB1C,GACpB,IAAK,MAAM9H,KAAO8H,EAChB,GAAIA,EAAIhI,eAAeE,KAASI,EAAY0H,EAAI9H,IAC9C,OAAO,EAGX,OAAO,CACT,CAqBqC4K,CAAaJ,KAE9CF,GAAM9J,EAAQ+J,EAAMG,MAAM,GAAK,IAG1BlK,CACT,CCjDe,IAAAqK,GAAIzL,IACjB,IAAK,MAAMY,KAAOZ,EAChB,GAAIyK,EAAWzK,EAAKY,IAClB,OAAO,EAGX,OAAO,CAAK,ECDd,SAAS8K,GAAmB1L,EAAS2L,EAA8B,IACjE,MAAMC,EAAoB9M,MAAMC,QAAQiB,GAExC,GAAInB,EAASmB,IAAS4L,EACpB,IAAK,MAAMhL,KAAOZ,EAEdlB,MAAMC,QAAQiB,EAAKY,KAClB/B,EAASmB,EAAKY,MAAU6K,GAAkBzL,EAAKY,KAEhD+K,EAAO/K,GAAO9B,MAAMC,QAAQiB,EAAKY,IAAQ,GAAK,CAAE,EAChD8K,GAAgB1L,EAAKY,GAAM+K,EAAO/K,KACxBjC,EAAkBqB,EAAKY,MACjC+K,EAAO/K,IAAO,GAKpB,OAAO+K,CACT,CAEA,SAASE,GACP7L,EACAwF,EACAsG,GAKA,MAAMF,EAAoB9M,MAAMC,QAAQiB,GAExC,GAAInB,EAASmB,IAAS4L,EACpB,IAAK,MAAMhL,KAAOZ,EAEdlB,MAAMC,QAAQiB,EAAKY,KAClB/B,EAASmB,EAAKY,MAAU6K,GAAkBzL,EAAKY,IAG9CI,EAAYwE,IACZqE,EAAYiC,EAAsBlL,IAElCkL,EAAsBlL,GAAO9B,MAAMC,QAAQiB,EAAKY,IAC5C8K,GAAgB1L,EAAKY,GAAM,IAC3B,IAAK8K,GAAgB1L,EAAKY,KAE9BiL,GACE7L,EAAKY,GACLjC,EAAkB6G,GAAc,CAAE,EAAGA,EAAW5E,GAChDkL,EAAsBlL,IAI1BkL,EAAsBlL,IAAQkJ,EAAU9J,EAAKY,GAAM4E,EAAW5E,IAKpE,OAAOkL,CACT,CAEA,IAAAC,GAAe,CAAI1I,EAAkBmC,IACnCqG,GACExI,EACAmC,EACAkG,GAAgBlG,IC/DpB,MAAMwG,GAAqC,CACzCvN,OAAO,EACPqG,SAAS,GAGLmH,GAAc,CAAExN,OAAO,EAAMqG,SAAS,GAE7B,IAAAoH,GAACC,IACd,GAAIrN,MAAMC,QAAQoN,GAAU,CAC1B,GAAIA,EAAQhK,OAAS,EAAG,CACtB,MAAMgE,EAASgG,EACZrL,QAAQsL,GAAWA,GAAUA,EAAOjN,UAAYiN,EAAOnI,WACvD2B,KAAKwG,GAAWA,EAAO3N,QAC1B,MAAO,CAAEA,MAAO0H,EAAQrB,UAAWqB,EAAOhE,QAG5C,OAAOgK,EAAQ,GAAGhN,UAAYgN,EAAQ,GAAGlI,SAErCkI,EAAQ,GAAGE,aAAerL,EAAYmL,EAAQ,GAAGE,WAAW5N,OAC1DuC,EAAYmL,EAAQ,GAAG1N,QAA+B,KAArB0N,EAAQ,GAAG1N,MAC1CwN,GACA,CAAExN,MAAO0N,EAAQ,GAAG1N,MAAOqG,SAAS,GACtCmH,GACFD,GAGN,OAAOA,EAAa,EC7BtBM,GAAe,CACb7N,GACE8N,gBAAeC,cAAaC,gBAE9BzL,EAAYvC,GACRA,EACA8N,EACY,KAAV9N,EACEiO,IACAjO,GACGA,EACDA,EACJ+N,GAAenH,EAAS5G,GACtB,IAAIC,KAAKD,GACTgO,EACEA,EAAWhO,GACXA,ECfZ,MAAMkO,GAAkC,CACtC7H,SAAS,EACTrG,MAAO,MAGT,IAAAmO,GAAgBT,GACdrN,MAAMC,QAAQoN,GACVA,EAAQ1K,QACN,CAACoL,EAAUT,IACTA,GAAUA,EAAOjN,UAAYiN,EAAOnI,SAChC,CACEa,SAAS,EACTrG,MAAO2N,EAAO3N,OAEhBoO,GACNF,IAEFA,GCXkB,SAAAG,GAAcnF,GACpC,MAAMJ,EAAMI,EAAGJ,IAEf,OAAIiD,EAAYjD,GACPA,EAAIwF,MAGThC,GAAaxD,GACRqF,GAAcjF,EAAGqF,MAAMvO,MAG5BqM,GAAiBvD,GACZ,IAAIA,EAAI0F,iBAAiBrH,KAAI,EAAGnH,WAAYA,IAGjDyO,EAAW3F,GACN2E,GAAiBvE,EAAGqF,MAAMvO,MAG5B6N,GAAgBtL,EAAYuG,EAAI9I,OAASkJ,EAAGJ,IAAI9I,MAAQ8I,EAAI9I,MAAOkJ,EAC5E,CCpBe,ICXfwF,GAAgB1O,GAAoCA,aAAiB2O,OCSrEC,GACEC,GAEAtM,EAAYsM,GACRA,EACAH,GAAQG,GACNA,EAAKC,OACL1O,EAASyO,GACPH,GAAQG,EAAK7O,OACX6O,EAAK7O,MAAM8O,OACXD,EAAK7O,MACP6O,ECjBKE,GAACC,IAAsC,CACpDC,YAAaD,GAAQA,IAAShL,EAC9BkL,SAAUF,IAAShL,EACnBmL,WAAYH,IAAShL,EACrBoL,QAASJ,IAAShL,EAClBqL,UAAWL,IAAShL,ICJtB,MAAMsL,GAAiB,gBAEvB,ICJeC,GAAA,CACb1O,EACAiG,EACA0I,KAECA,IACA1I,EAAOO,UACNP,EAAOG,MAAMnG,IAAID,IACjB,IAAIiG,EAAOG,OAAOwI,MACfC,GACC7O,EAAK8O,WAAWD,IAChB,SAASvM,KAAKtC,EAAKgM,MAAM6C,EAAUhM,YCT3C,MAAMkM,GAAwB,CAC5B1C,EACArD,EACAgG,EACAC,KAEA,IAAK,MAAM3N,KAAO0N,GAAe/K,OAAOqF,KAAK+C,GAAS,CACpD,MAAMlE,EAAQtG,EAAIwK,EAAQ/K,GAE1B,GAAI6G,EAAO,CACT,MAAME,GAAEA,KAAO6G,GAAiB/G,EAEhC,GAAIE,EAAI,CACN,GAAIA,EAAGqF,MAAQrF,EAAGqF,KAAK,IAAM1E,EAAOX,EAAGqF,KAAK,GAAIpM,KAAS2N,EACvD,OAAO,EACF,GAAI5G,EAAGJ,KAAOe,EAAOX,EAAGJ,IAAKI,EAAGrI,QAAUiP,EAC/C,OAAO,EAEP,GAAIF,GAAsBG,EAAclG,GACtC,WAGC,GAAIzJ,EAAS2P,IACdH,GAAsBG,EAA2BlG,GACnD,OAKR,EC7BsB,SAAAmG,GACtB1J,EACA2C,EACApI,GAKA,MAAM6H,EAAQhG,EAAI4D,EAAQzF,GAE1B,GAAI6H,GAASxF,EAAMrC,GACjB,MAAO,CACL6H,QACA7H,QAIJ,MAAMD,EAAQC,EAAKkC,MAAM,KAEzB,KAAOnC,EAAM8C,QAAQ,CACnB,MAAM0D,EAAYxG,EAAMqP,KAAK,KACvBjH,EAAQtG,EAAIuG,EAAS7B,GACrB8I,EAAaxN,EAAI4D,EAAQc,GAE/B,GAAI4B,IAAU3I,MAAMC,QAAQ0I,IAAUnI,IAASuG,EAC7C,MAAO,CAAEvG,QAGX,GAAIqP,GAAcA,EAAWpQ,KAC3B,MAAO,CACLe,KAAMuG,EACNsB,MAAOwH,GAIX,GAAIA,GAAcA,EAAWC,MAAQD,EAAWC,KAAKrQ,KACnD,MAAO,CACLe,KAAM,GAAGuG,SACTsB,MAAOwH,EAAWC,MAItBvP,EAAMwP,MAGR,MAAO,CACLvP,OAEJ,CC3Ce,ICCfwP,GAAe,CACb/J,EACAoC,EACA7H,KAEA,MAAMyP,EAAmB5F,EAAsBhI,EAAI4D,EAAQzF,IAG3D,OAFA0C,EAAI+M,EAAkB,OAAQ5H,EAAM7H,IACpC0C,EAAI+C,EAAQzF,EAAMyP,GACXhK,CAAM,ECffiK,GAAgBvQ,GAAqC4G,EAAS5G,GCChD,SAAUwQ,GACtB1N,EACAgG,EACAhJ,EAAO,YAEP,GACEyQ,GAAUzN,IACTzC,MAAMC,QAAQwC,IAAWA,EAAO2N,MAAMF,KACtCtN,EAAUH,KAAYA,EAEvB,MAAO,CACLhD,OACAwJ,QAASiH,GAAUzN,GAAUA,EAAS,GACtCgG,MAGN,CChBe,IAAA4H,GAACC,GACdvQ,EAASuQ,KAAoBjC,GAAQiC,GACjCA,EACA,CACE3Q,MAAO2Q,EACPrH,QAAS,ICwBjBsH,GAAeC,MACb7H,EACA8H,EACA/J,EACAyD,EACAuG,EACAC,KAEA,MAAMlI,IACJA,EAAGyF,KACHA,EAAI0C,SACJA,EAAQC,UACRA,EAASC,UACTA,EAASC,IACTA,EAAGC,IACHA,EAAGC,QACHA,EAAOC,SACPA,EAAQ1Q,KACRA,EAAIiN,cACJA,EAAanE,MACbA,GACEX,EAAME,GACJsI,EAA+B9O,EAAIqE,EAAYlG,GACrD,IAAK8I,GAASmH,EAAmBhQ,IAAID,GACnC,MAAO,CAAE,EAEX,MAAM4Q,EAA6BlD,EAAOA,EAAK,GAAMzF,EAC/CO,EAAqBC,IACrByH,GAA6BU,EAASlI,iBACxCkI,EAASpI,kBAAkBpG,EAAUqG,GAAW,GAAKA,GAAW,IAChEmI,EAASlI,mBAGPb,EAA6B,CAAE,EAC/BgJ,EAAUpF,GAAaxD,GACvB2F,EAAa7O,EAAgBkJ,GAC7B6I,EAAoBD,GAAWjD,EAC/BmD,GACF9D,GAAiB/B,EAAYjD,KAC7BvG,EAAYuG,EAAI9I,QAChBuC,EAAYiP,IACbvF,GAAcnD,IAAsB,KAAdA,EAAI9I,OACZ,KAAfwR,GACCnR,MAAMC,QAAQkR,KAAgBA,EAAW9N,OACtCmO,EAAoBtH,EAAauH,KACrC,KACAjR,EACA2J,EACA9B,GAEIqJ,EAAmB,CACvBC,EACAC,EACAC,EACAC,EAAmBlO,EACnBmO,EAAmBnO,KAEnB,MAAMqF,EAAU0I,EAAYC,EAAmBC,EAC/CxJ,EAAM7H,GAAQ,CACZf,KAAMkS,EAAYG,EAAUC,EAC5B9I,UACAR,SACG+I,EAAkBG,EAAYG,EAAUC,EAAS9I,GACrD,EAGH,GACE0H,GACK3Q,MAAMC,QAAQkR,KAAgBA,EAAW9N,OAC1CuN,KACGU,IAAsBC,GAAW1R,EAAkBsR,KACnDvO,EAAUuO,KAAgBA,GAC1B/C,IAAehB,GAAiBc,GAAMlI,SACtCqL,IAAYvD,GAAcI,GAAMlI,SACvC,CACA,MAAMrG,MAAEA,EAAKsJ,QAAEA,GAAYiH,GAAUU,GACjC,CAAEjR,QAASiR,EAAU3H,QAAS2H,GAC9BP,GAAmBO,GAEvB,GAAIjR,IACF0I,EAAM7H,GAAQ,CACZf,KAAMmE,EACNqF,UACAR,IAAK2I,KACFI,EAAkB5N,EAAiCqF,KAEnDkB,GAEH,OADAnB,EAAkBC,GACXZ,EAKb,KAAKkJ,GAAa1R,EAAkBkR,IAASlR,EAAkBmR,IAAO,CACpE,IAAIW,EACAK,EACJ,MAAMC,EAAY5B,GAAmBW,GAC/BkB,EAAY7B,GAAmBU,GAErC,GAAKlR,EAAkBsR,IAAgB1N,MAAM0N,GAUtC,CACL,MAAMgB,EACH1J,EAAyBiF,aAAe,IAAI9N,KAAKuR,GAC9CiB,EAAqBC,GACzB,IAAIzS,MAAK,IAAIA,MAAO0S,eAAiB,IAAMD,GACvCE,EAAqB,QAAZ9J,EAAIhJ,KACb+S,EAAqB,QAAZ/J,EAAIhJ,KAEf8G,EAAS0L,EAAUtS,QAAUwR,IAC/BQ,EAAYY,EACRH,EAAkBjB,GAAciB,EAAkBH,EAAUtS,OAC5D6S,EACErB,EAAac,EAAUtS,MACvBwS,EAAY,IAAIvS,KAAKqS,EAAUtS,QAGnC4G,EAAS2L,EAAUvS,QAAUwR,IAC/Ba,EAAYO,EACRH,EAAkBjB,GAAciB,EAAkBF,EAAUvS,OAC5D6S,EACErB,EAAae,EAAUvS,MACvBwS,EAAY,IAAIvS,KAAKsS,EAAUvS,YA/B2B,CAClE,MAAM8S,EACHhK,EAAyBgF,gBACzB0D,GAAcA,EAAaA,GACzBtR,EAAkBoS,EAAUtS,SAC/BgS,EAAYc,EAAcR,EAAUtS,OAEjCE,EAAkBqS,EAAUvS,SAC/BqS,EAAYS,EAAcP,EAAUvS,OA2BxC,IAAIgS,GAAaK,KACfN,IACIC,EACFM,EAAUhJ,QACViJ,EAAUjJ,QACVrF,EACAA,IAEGuG,GAEH,OADAnB,EAAkBX,EAAM7H,GAAOyI,SACxBZ,EAKb,IACGwI,GAAaC,KACbS,IACAhL,EAAS4K,IAAgBR,GAAgB3Q,MAAMC,QAAQkR,IACxD,CACA,MAAMuB,EAAkBrC,GAAmBQ,GACrC8B,EAAkBtC,GAAmBS,GACrCa,GACH9R,EAAkB6S,EAAgB/S,QACnCwR,EAAW9N,QAAUqP,EAAgB/S,MACjCqS,GACHnS,EAAkB8S,EAAgBhT,QACnCwR,EAAW9N,QAAUsP,EAAgBhT,MAEvC,IAAIgS,GAAaK,KACfN,EACEC,EACAe,EAAgBzJ,QAChB0J,EAAgB1J,UAEbkB,GAEH,OADAnB,EAAkBX,EAAM7H,GAAOyI,SACxBZ,EAKb,GAAI4I,IAAYM,GAAWhL,EAAS4K,GAAa,CAC/C,MAAQxR,MAAOiT,EAAY3J,QAAEA,GAAYoH,GAAmBY,GAE5D,GAAI5C,GAAQuE,KAAkBzB,EAAW0B,MAAMD,KAC7CvK,EAAM7H,GAAQ,CACZf,KAAMmE,EACNqF,UACAR,SACG+I,EAAkB5N,EAAgCqF,KAElDkB,GAEH,OADAnB,EAAkBC,GACXZ,EAKb,GAAI6I,EACF,GAAIvF,EAAWuF,GAAW,CACxB,MACM4B,EAAgB3C,SADDe,EAASC,EAAYzK,GACK0K,GAE/C,GAAI0B,IACFzK,EAAM7H,GAAQ,IACTsS,KACAtB,EACD5N,EACAkP,EAAc7J,WAGbkB,GAEH,OADAnB,EAAkB8J,EAAc7J,SACzBZ,OAGN,GAAItI,EAASmR,GAAW,CAC7B,IAAI6B,EAAmB,CAAgB,EAEvC,IAAK,MAAMjR,KAAOoP,EAAU,CAC1B,IAAKzF,EAAcsH,KAAsB5I,EACvC,MAGF,MAAM2I,EAAgB3C,SACde,EAASpP,GAAKqP,EAAYzK,GAChC0K,EACAtP,GAGEgR,IACFC,EAAmB,IACdD,KACAtB,EAAkB1P,EAAKgR,EAAc7J,UAG1CD,EAAkB8J,EAAc7J,SAE5BkB,IACF9B,EAAM7H,GAAQuS,IAKpB,IAAKtH,EAAcsH,KACjB1K,EAAM7H,GAAQ,CACZiI,IAAK2I,KACF2B,IAEA5I,GACH,OAAO9B,EAOf,OADAW,GAAkB,GACXX,CAAK,ECnMd,MAAM2K,GAAiB,CACrBrE,KAAMhL,EACNsP,eAAgBtP,EAChBuP,kBAAkB,GAGJ,SAAAC,GAKdlO,EAAkE,IAUlE,IAAImE,EAAW,IACV4J,MACA/N,GAEDM,EAAsC,CACxC6N,YAAa,EACb1N,SAAS,EACT2N,SAAS,EACT1N,UAAWgG,EAAWvC,EAAS7E,eAC/BwB,cAAc,EACduN,aAAa,EACbC,cAAc,EACdC,oBAAoB,EACpBxN,SAAS,EACTH,cAAe,CAAE,EACjBD,YAAa,CAAE,EACfE,iBAAkB,CAAE,EACpBG,OAAQmD,EAASnD,QAAU,CAAE,EAC7Bd,SAAUiE,EAASjE,WAAY,GAEjC,MAAMyD,EAAqB,CAAE,EAC7B,IAmBI6K,EAnBAjP,GACFzE,EAASqJ,EAAS7E,gBAAkBxE,EAASqJ,EAAS/B,UAClDpG,EAAYmI,EAAS7E,eAAiB6E,EAAS/B,SAC/C,CAAE,EACJC,EAAc8B,EAAS3B,iBACtB,CAAA,EACAxG,EAAYuD,GACb+E,EAAS,CACXC,QAAQ,EACRF,OAAO,EACP1C,OAAO,GAELH,EAAgB,CAClB6C,MAAO,IAAIhI,IACX6D,SAAU,IAAI7D,IACdoS,QAAS,IAAIpS,IACbqG,MAAO,IAAIrG,IACXsF,MAAO,IAAItF,KAGTqS,EAAQ,EACZ,MAAM/O,EAAiC,CACrCc,SAAS,EACTE,aAAa,EACbE,kBAAkB,EAClBD,eAAe,EACfE,cAAc,EACdC,SAAS,EACTC,QAAQ,GAEV,IAAI2N,EAA2B,IAC1BhP,GAEL,MAAMiP,EAAoC,CACxClM,MAAO2C,IACPwJ,MAAOxJ,KAGHyJ,EACJ3K,EAAS4K,eAAiBrQ,EAStB0C,EAAYmK,MAAOyD,IACvB,IACG7K,EAASjE,WACTP,EAAgBoB,SACf4N,EAAyB5N,SACzBiO,GACF,CACA,MAAMjO,EAAUoD,EAAS8K,SACrBzI,SAAqB0I,KAAclO,cAC7BmO,EAAyBxL,GAAS,GAExC5C,IAAYT,EAAWS,SACzB6N,EAAUC,MAAMrJ,KAAK,CACnBzE,cAMFqO,EAAsB,CAAC9T,EAAkBwF,MAE1CqD,EAASjE,WACTP,EAAgBmB,cACfnB,EAAgBkB,kBAChB8N,EAAyB7N,cACzB6N,EAAyB9N,qBAE1BvF,GAASP,MAAMsU,KAAK7N,EAAO6C,QAAQiL,SAAS/T,IACvCA,IACFuF,EACI7C,EAAIqC,EAAWO,iBAAkBtF,EAAMuF,GACvCqG,GAAM7G,EAAWO,iBAAkBtF,OAI3CqT,EAAUC,MAAMrJ,KAAK,CACnB3E,iBAAkBP,EAAWO,iBAC7BC,cAAe0F,EAAclG,EAAWO,sBA8ExC0O,EAAsB,CAC1BhU,EACAiU,EACA9U,EACA8I,KAEA,MAAME,EAAetG,EAAIuG,EAASpI,GAElC,GAAImI,EAAO,CACT,MAAMnG,EAAeH,EACnBiF,EACA9G,EACA0B,EAAYvC,GAAS0C,EAAImC,EAAgBhE,GAAQb,GAGnDuC,EAAYM,IACXiG,GAAQA,EAAyBiM,gBAClCD,EACIvR,EACEoE,EACA9G,EACAiU,EAAuBjS,EAAewL,GAAcrF,EAAME,KAE5D8L,EAAcnU,EAAMgC,GAExB+G,EAAOD,OAASjD,MAIduO,EAAsB,CAC1BpU,EACAqU,EACA1F,EACA2F,EACAC,KAIA,IAAIC,GAAoB,EACpBC,GAAkB,EACtB,MAAMpL,EAA8D,CAClErJ,QAGF,IAAK4I,EAASjE,SAAU,CACtB,IAAKgK,GAAe2F,EAAa,EAC3BlQ,EAAgBc,SAAWkO,EAAyBlO,WACtDuP,EAAkB1P,EAAWG,QAC7BH,EAAWG,QAAUmE,EAAOnE,QAAUwP,IACtCF,EAAoBC,IAAoBpL,EAAOnE,SAGjD,MAAMyP,EAAyBnK,EAC7B3I,EAAImC,EAAgBhE,GACpBqU,GAGFI,IAAoB5S,EAAIkD,EAAWK,YAAapF,GAChD2U,EACI/I,GAAM7G,EAAWK,YAAapF,GAC9B0C,EAAIqC,EAAWK,YAAapF,GAAM,GACtCqJ,EAAOjE,YAAcL,EAAWK,YAChCoP,EACEA,IACEpQ,EAAgBgB,aAChBgO,EAAyBhO,cACzBqP,KAAqBE,EAG3B,GAAIhG,EAAa,CACf,MAAMiG,EAAyB/S,EAAIkD,EAAWM,cAAerF,GAExD4U,IACHlS,EAAIqC,EAAWM,cAAerF,EAAM2O,GACpCtF,EAAOhE,cAAgBN,EAAWM,cAClCmP,EACEA,IACEpQ,EAAgBiB,eAChB+N,EAAyB/N,gBACzBuP,IAA2BjG,GAInC6F,GAAqBD,GAAgBlB,EAAUC,MAAMrJ,KAAKZ,GAG5D,OAAOmL,EAAoBnL,EAAS,CAAE,CAAA,EAGlCwL,EAAsB,CAC1B7U,EACAwF,EACAqC,EACAL,KAMA,MAAMsN,EAAqBjT,EAAIkD,EAAWU,OAAQzF,GAC5CyT,GACHrP,EAAgBoB,SAAW4N,EAAyB5N,UACrDpD,EAAUoD,IACVT,EAAWS,UAAYA,EAhOzB,IAAqBI,EA6OrB,GAXIgD,EAASmM,YAAclN,GAlONjC,EAmOW,IAzHb,EAAC5F,EAAyB6H,KAC7CnF,EAAIqC,EAAWU,OAAQzF,EAAM6H,GAC7BwL,EAAUC,MAAMrJ,KAAK,CACnBxE,OAAQV,EAAWU,QACnB,EAqHoCuP,CAAahV,EAAM6H,GAAvDoL,EAlODgC,IACCC,aAAa/B,GACbA,EAAQgC,WAAWvP,EAAUqP,EAAK,EAiOlChC,EAAmBrK,EAASmM,cAE5BG,aAAa/B,GACbF,EAAqB,KACrBpL,EACInF,EAAIqC,EAAWU,OAAQzF,EAAM6H,GAC7B+D,GAAM7G,EAAWU,OAAQzF,KAI5B6H,GAAS2C,EAAUsK,EAAoBjN,GAASiN,KAChD7J,EAAczD,IACfiM,EACA,CACA,MAAM2B,EAAmB,IACpB5N,KACCiM,GAAqBrR,EAAUoD,GAAW,CAAEA,WAAY,GAC5DC,OAAQV,EAAWU,OACnBzF,QAGF+E,EAAa,IACRA,KACAqQ,GAGL/B,EAAUC,MAAMrJ,KAAKmL,KAInBzB,EAAa3D,MAAOhQ,IACxB6T,EAAoB7T,GAAM,GAC1B,MAAMiC,QAAe2G,EAAS8K,SAC5B5M,EACA8B,EAASyM,QdzaA,EACbrG,EACA5G,EACAoL,EACAtD,KAEA,MAAM7D,EAAiD,CAAE,EAEzD,IAAK,MAAMrM,KAAQgP,EAAa,CAC9B,MAAM7G,EAAetG,EAAIuG,EAASpI,GAElCmI,GAASzF,EAAI2J,EAAQrM,EAAMmI,EAAME,IAGnC,MAAO,CACLmL,eACAzT,MAAO,IAAIiP,GACX3C,SACA6D,4BACD,EcuZGoF,CACEtV,GAAQiG,EAAO6C,MACfV,EACAQ,EAAS4K,aACT5K,EAASsH,4BAIb,OADA2D,EAAoB7T,GACbiC,CAAM,EAoBT2R,EAA2B5D,MAC/B3D,EACAkJ,EACAF,EAEI,CACFG,OAAO,MAGT,IAAK,MAAMxV,KAAQqM,EAAQ,CACzB,MAAMlE,EAAQkE,EAAOrM,GAErB,GAAImI,EAAO,CACT,MAAME,GAAEA,KAAOgM,GAAelM,EAE9B,GAAIE,EAAI,CACN,MAAMoN,EAAmBxP,EAAOkB,MAAMlH,IAAIoI,EAAGrI,MACvC0V,EACJvN,EAAME,QV7dFsN,EU6d8BxN,EAAgBE,OV3d1DsN,EAAejF,aAEdvF,EAAWwK,EAAejF,WACzBiF,EAAejF,SAASxP,YAAYlB,OAASyO,IAC9ClP,EAASoW,EAAejF,WACvBzM,OAAO4C,OAAO8O,EAAejF,UAAUkF,MACpCC,GACCA,EAAiB3U,YAAYlB,OAASyO,OUsdlCiH,GAAqBtR,EAAgBkB,kBACvCuO,EAAoB,CAAC7T,IAAO,GAG9B,MAAM8V,QAAmB/F,GACvB5H,EACAlC,EAAOtB,SACPmC,EACAyM,EACA3K,EAASsH,4BAA8BqF,EACvCE,GAOF,GAJIC,GAAqBtR,EAAgBkB,kBACvCuO,EAAoB,CAAC7T,IAGnB8V,EAAWzN,EAAGrI,QAChBqV,EAAQG,OAAQ,EACZD,GACF,OAIHA,IACE1T,EAAIiU,EAAYzN,EAAGrI,MAChByV,EACEjG,GACEzK,EAAWU,OACXqQ,EACAzN,EAAGrI,MAEL0C,EAAIqC,EAAWU,OAAQ4C,EAAGrI,KAAM8V,EAAWzN,EAAGrI,OAChD4L,GAAM7G,EAAWU,OAAQ4C,EAAGrI,QAGnCiL,EAAcoJ,UACNT,EACLS,EACAkB,EACAF,IVvgBG,IAACM,EU4gBZ,OAAON,EAAQG,KAAK,EAiBhBd,EAAwB,CAAC1U,EAAMU,KAClCkI,EAASjE,WACT3E,GAAQU,GAAQgC,EAAIoE,EAAa9G,EAAMU,IACvC8J,EAAUuL,IAAa/R,IAEpB4C,EAAyC,CAC7C7G,EACAiC,EACAmE,IAEAH,EACEjG,EACAkG,EACA,IACM8C,EAAOD,MACPhC,EACApF,EAAYM,GACVgC,EACA+B,EAAShG,GACP,CAAEA,CAACA,GAAQiC,GACXA,GAEVmE,EACAnE,GAcEmS,EAAgB,CACpBnU,EACAb,EACA0N,EAA0B,CAAA,KAE1B,MAAM1E,EAAetG,EAAIuG,EAASpI,GAClC,IAAIqU,EAAsBlV,EAE1B,GAAIgJ,EAAO,CACT,MAAMwN,EAAiBxN,EAAME,GAEzBsN,KACDA,EAAehR,UACdjC,EAAIoE,EAAa9G,EAAMgN,GAAgB7N,EAAOwW,IAEhDtB,EACEjJ,GAAcuK,EAAe1N,MAAQ5I,EAAkBF,GACnD,GACAA,EAEFqM,GAAiBmK,EAAe1N,KAClC,IAAI0N,EAAe1N,IAAI4E,SAASkH,SAC7BiC,GACEA,EAAUC,SACT5B,EACAtJ,SAASiL,EAAU7W,SAEhBwW,EAAejI,KACpB3O,EAAgB4W,EAAe1N,KACjC0N,EAAejI,KAAKqG,SAASmC,IACtBA,EAAYhC,gBAAmBgC,EAAYvR,WAC1CnF,MAAMC,QAAQ4U,GAChB6B,EAAYrW,UAAYwU,EAAWuB,MAChClV,GAAiBA,IAASwV,EAAY/W,QAGzC+W,EAAYrW,QACVwU,IAAe6B,EAAY/W,SAAWkV,MAK9CsB,EAAejI,KAAKqG,SACjBoC,GACEA,EAAStW,QAAUsW,EAAShX,QAAUkV,IAGpCnJ,EAAYyK,EAAe1N,KACpC0N,EAAe1N,IAAI9I,MAAQ,IAE3BwW,EAAe1N,IAAI9I,MAAQkV,EAEtBsB,EAAe1N,IAAIhJ,MACtBoU,EAAUC,MAAMrJ,KAAK,CACnBjK,OACA6G,OAAQpG,EAAYqG,QAO7B+F,EAAQyH,aAAezH,EAAQuJ,cAC9BhC,EACEpU,EACAqU,EACAxH,EAAQuJ,YACRvJ,EAAQyH,aACR,GAGJzH,EAAQwJ,gBAAkBC,EAAQtW,EAA2B,EAGzDuW,EAAY,CAKhBvW,EACAb,EACA0N,KAEA,IAAK,MAAM2J,KAAYrX,EAAO,CAC5B,IAAKA,EAAMiC,eAAeoV,GACxB,OAEF,MAAMnC,EAAalV,EAAMqX,GACnBjQ,EAAYvG,EAAO,IAAMwW,EACzBrO,EAAQtG,EAAIuG,EAAS7B,IAE1BN,EAAOkB,MAAMlH,IAAID,IAChBT,EAAS8U,IACRlM,IAAUA,EAAME,MAClBnJ,EAAamV,GACVkC,EAAUhQ,EAAW8N,EAAYxH,GACjCsH,EAAc5N,EAAW8N,EAAYxH,KAIvC4J,EAA0C,CAC9CzW,EACAb,EACA0N,EAAU,CAAA,KAEV,MAAM1E,EAAQtG,EAAIuG,EAASpI,GACrBmQ,EAAelK,EAAOkB,MAAMlH,IAAID,GAChC0W,EAAajW,EAAYtB,GAE/BuD,EAAIoE,EAAa9G,EAAM0W,GAEnBvG,GACFkD,EAAUlM,MAAM8C,KAAK,CACnBjK,OACA6G,OAAQpG,EAAYqG,MAInB1C,EAAgBc,SACfd,EAAgBgB,aAChBgO,EAAyBlO,SACzBkO,EAAyBhO,cAC3ByH,EAAQyH,aAERjB,EAAUC,MAAMrJ,KAAK,CACnBjK,OACAoF,YAAaqH,GAAezI,EAAgB8C,GAC5C5B,QAASwP,EAAU1U,EAAM0W,OAI7BvO,GAAUA,EAAME,IAAOhJ,EAAkBqX,GAErCvC,EAAcnU,EAAM0W,EAAY7J,GADhC0J,EAAUvW,EAAM0W,EAAY7J,GAIlC6B,GAAU1O,EAAMiG,IAAWoN,EAAUC,MAAMrJ,KAAK,IAAKlF,IACrDsO,EAAUC,MAAMrJ,KAAK,CACnBjK,KAAM+I,EAAOD,MAAQ9I,OAAO4B,EAC5BiF,OAAQpG,EAAYqG,IACpB,EAGEgB,EAA0BkI,MAAOrQ,IACrCoJ,EAAOD,OAAQ,EACf,MAAMlJ,EAASD,EAAMC,OACrB,IAAII,EAAeJ,EAAOI,KACtB2W,GAAsB,EAC1B,MAAMxO,EAAetG,EAAIuG,EAASpI,GAC5B4W,EAA8BvC,IAClCsC,EACEE,OAAO5T,MAAMoR,IACZnV,EAAamV,IAAepR,MAAMoR,EAAW1J,YAC9CH,EAAU6J,EAAYxS,EAAIiF,EAAa9G,EAAMqU,GAAY,EAEvDyC,EAA6B5I,GAAmBtF,EAASuF,MACzD4I,EAA4B7I,GAChCtF,EAAS6J,gBAGX,GAAItK,EAAO,CACT,IAAIN,EACArC,EACJ,MAAM6O,EAAazU,EAAOX,KACtBuO,GAAcrF,EAAME,IACpB3I,EAAcC,GACZgP,EACJhP,EAAMV,OAASiE,GAAevD,EAAMV,OAASiE,EACzC8T,KC9uBInK,ED+uBQ1E,EAAME,IC9uBpBS,QACP+D,EAAQuD,UACPvD,EAAQ0D,KACR1D,EAAQ2D,KACR3D,EAAQwD,WACRxD,EAAQyD,WACRzD,EAAQ4D,SACR5D,EAAQ6D,WDwuBD9H,EAAS8K,UACT7R,EAAIkD,EAAWU,OAAQzF,IACvBmI,EAAME,GAAG4O,OElvBL,EACbtI,EACA/G,EACAkL,EACAL,EAIAtE,KAEIA,EAAKI,WAEGuE,GAAe3E,EAAKK,YACrB5G,GAAa+G,IACbmE,EAAcL,EAAepE,SAAWF,EAAKE,WAC9CM,IACCmE,EAAcL,EAAenE,WAAaH,EAAKG,aACjDK,GFkuBHuI,CACEvI,EACA9M,EAAIkD,EAAWM,cAAerF,GAC9B+E,EAAW+N,YACXiE,EACAD,GAEEK,EAAUzI,GAAU1O,EAAMiG,EAAQ0I,GAExCjM,EAAIoE,EAAa9G,EAAMqU,GAEnB1F,GACFxG,EAAME,GAAGL,QAAUG,EAAME,GAAGL,OAAOrI,GACnCsT,GAAsBA,EAAmB,IAChC9K,EAAME,GAAGP,UAClBK,EAAME,GAAGP,SAASnI,GAGpB,MAAM6H,EAAa4M,EAAoBpU,EAAMqU,EAAY1F,GAEnD4F,GAAgBtJ,EAAczD,IAAe2P,EASnD,IAPCxI,GACC0E,EAAUC,MAAMrJ,KAAK,CACnBjK,OACAf,KAAMU,EAAMV,KACZ4H,OAAQpG,EAAYqG,KAGpBkQ,EAWF,OAVI5S,EAAgBoB,SAAW4N,EAAyB5N,WAChC,WAAlBoD,EAASuF,KACPQ,GACF9I,IAEQ8I,GACV9I,KAKF0O,GACAlB,EAAUC,MAAMrJ,KAAK,CAAEjK,UAAUmX,EAAU,CAAA,EAAK3P,IAMpD,IAFCmH,GAAewI,GAAW9D,EAAUC,MAAMrJ,KAAK,IAAKlF,IAEjD6D,EAAS8K,SAAU,CACrB,MAAMjO,OAAEA,SAAiBkO,EAAW,CAAC3T,IAIrC,GAFA4W,EAA2BvC,GAEvBsC,EAAqB,CACvB,MAAMS,EAA4BjI,GAChCpK,EAAWU,OACX2C,EACApI,GAEIqX,EAAoBlI,GACxB1J,EACA2C,EACAgP,EAA0BpX,MAAQA,GAGpC6H,EAAQwP,EAAkBxP,MAC1B7H,EAAOqX,EAAkBrX,KAEzBwF,EAAUyF,EAAcxF,SAG1BoO,EAAoB,CAAC7T,IAAO,GAC5B6H,SACQkI,GACJ5H,EACAlC,EAAOtB,SACPmC,EACAyM,EACA3K,EAASsH,4BAEXlQ,GACF6T,EAAoB,CAAC7T,IAErB4W,EAA2BvC,GAEvBsC,IACE9O,EACFrC,GAAU,GAEVpB,EAAgBoB,SAChB4N,EAAyB5N,WAEzBA,QAAgBoO,EAAyBxL,GAAS,KAKpDuO,IACFxO,EAAME,GAAG4O,MACPX,EACEnO,EAAME,GAAG4O,MAIbpC,EAAoB7U,EAAMwF,EAASqC,EAAOL,IC31BnC,IAACqF,GDg2BRyK,EAAc,CAACrP,EAAU3G,KAC7B,GAAIO,EAAIkD,EAAWU,OAAQnE,IAAQ2G,EAAIK,MAErC,OADAL,EAAIK,QACG,CAET,EAGIgO,EAAwCtG,MAAOhQ,EAAM6M,EAAU,CAAA,KACnE,IAAIrH,EACA+M,EACJ,MAAMgF,EAAa1N,EAAsB7J,GAEzC,GAAI4I,EAAS8K,SAAU,CACrB,MAAMjO,OAhb0BuK,OAAOjQ,IACzC,MAAM0F,OAAEA,SAAiBkO,EAAW5T,GAEpC,GAAIA,EACF,IAAK,MAAMC,KAAQD,EAAO,CACxB,MAAM8H,EAAQhG,EAAI4D,EAAQzF,GAC1B6H,EACInF,EAAIqC,EAAWU,OAAQzF,EAAM6H,GAC7B+D,GAAM7G,EAAWU,OAAQzF,QAG/B+E,EAAWU,OAASA,EAGtB,OAAOA,CAAM,EAkaU+R,CACnB9V,EAAY1B,GAAQA,EAAOuX,GAG7B/R,EAAUyF,EAAcxF,GACxB8M,EAAmBvS,GACduX,EAAW3I,MAAM5O,GAAS6B,EAAI4D,EAAQzF,KACvCwF,OACKxF,GACTuS,SACQkF,QAAQC,IACZH,EAAWjR,KAAI0J,MAAOzJ,IACpB,MAAM4B,EAAQtG,EAAIuG,EAAS7B,GAC3B,aAAaqN,EACXzL,GAASA,EAAME,GAAK,CAAE9B,CAACA,GAAY4B,GAAUA,EAC9C,MAGLyH,MAAMnO,UACL8Q,GAAqBxN,EAAWS,UAAYK,KAE/C0M,EAAmB/M,QAAgBoO,EAAyBxL,GAqB9D,OAlBAiL,EAAUC,MAAMrJ,KAAK,KACdlE,EAAS/F,KACZoE,EAAgBoB,SAAW4N,EAAyB5N,UACpDA,IAAYT,EAAWS,QACrB,CAAA,EACA,CAAExF,WACF4I,EAAS8K,WAAa1T,EAAO,CAAEwF,WAAY,GAC/CC,OAAQV,EAAWU,SAGrBoH,EAAQ8K,cACLpF,GACDxD,GACE3G,EACAkP,EACAtX,EAAOuX,EAAatR,EAAO6C,OAGxByJ,CAAgB,EAGnBwD,EACJwB,IAIA,MAAM1Q,EAAS,IACTkC,EAAOD,MAAQhC,EAAc9C,GAGnC,OAAOtC,EAAY6V,GACf1Q,EACAd,EAASwR,GACP1V,EAAIgF,EAAQ0Q,GACZA,EAAWjR,KAAKtG,GAAS6B,EAAIgF,EAAQ7G,IAAM,EAG7C4X,GAAoD,CACxD5X,EACA2D,KACI,CACJ+D,UAAW7F,GAAK8B,GAAaoB,GAAYU,OAAQzF,GACjDkF,UAAWrD,GAAK8B,GAAaoB,GAAYK,YAAapF,GACtD6H,MAAOhG,GAAK8B,GAAaoB,GAAYU,OAAQzF,GAC7CuF,eAAgB1D,EAAIkD,EAAWO,iBAAkBtF,GACjD4H,YAAa/F,GAAK8B,GAAaoB,GAAYM,cAAerF,KActD6X,GAA0C,CAAC7X,EAAM6H,EAAOgF,KAC5D,MAAM5E,GAAOpG,EAAIuG,EAASpI,EAAM,CAAEqI,GAAI,KAAMA,IAAM,CAAE,GAAEJ,IAChD6P,EAAejW,EAAIkD,EAAWU,OAAQzF,IAAS,CAAE,GAG/CiI,IAAK8P,EAAUtP,QAAEA,EAAOxJ,KAAEA,KAAS+Y,GAAoBF,EAE/DpV,EAAIqC,EAAWU,OAAQzF,EAAM,IACxBgY,KACAnQ,EACHI,QAGFoL,EAAUC,MAAMrJ,KAAK,CACnBjK,OACAyF,OAAQV,EAAWU,OACnBD,SAAS,IAGXqH,GAAWA,EAAQ8K,aAAe1P,GAAOA,EAAIK,OAASL,EAAIK,OAAO,EA4B7D5C,GAA2CjB,GAC/C4O,EAAUC,MAAMnJ,UAAU,CACxBF,KACEtG,IGn/BO,IACb3D,EACAiY,EACArT,EAFA5E,EHw/B8ByE,EAAMzE,KGv/BpCiY,EHu/B0CtU,EAAU3D,KGt/BpD4E,EHs/B0DH,EAAMG,MGp/B/D5E,GACAiY,GACDjY,IAASiY,IACTpO,EAAsB7J,GAAM4O,MACzBsJ,GACCA,IACCtT,EACGsT,IAAgBD,EAChBC,EAAYpJ,WAAWmJ,IACvBA,EAAWnJ,WAAWoJ,QTPjB,EACbC,EAIA/T,EACAS,EACAf,KAEAe,EAAgBsT,GAChB,MAAMnY,KAAEA,KAAS2D,GAAcwU,EAE/B,OACElN,EAActH,IACdM,OAAOqF,KAAK3F,GAAWd,QAAUoB,OAAOqF,KAAKlF,GAAiBvB,QAC9DoB,OAAOqF,KAAK3F,GAAWiS,MACpBtU,GACC8C,EAAgB9C,OACdwC,GAAUX,IACf,EMg+BKiV,CACEzU,EACCc,EAAMd,WAA+BS,EACtCiU,GACA5T,EAAM6T,eAGR7T,EAAMmB,SAAS,CACbiB,OAAQ,IAAKC,MACV/B,KACApB,OAIR0G,YAcCpB,GAA8C,CAACjJ,EAAM6M,EAAU,CAAA,KACnE,IAAK,MAAMtG,KAAavG,EAAO6J,EAAsB7J,GAAQiG,EAAO6C,MAClE7C,EAAO6C,MAAMyP,OAAOhS,GACpBN,EAAOkB,MAAMoR,OAAOhS,GAEfsG,EAAQ2L,YACX5M,GAAMxD,EAAS7B,GACfqF,GAAM9E,EAAaP,KAGpBsG,EAAQ4L,WAAa7M,GAAM7G,EAAWU,OAAQc,IAC9CsG,EAAQ6L,WAAa9M,GAAM7G,EAAWK,YAAamB,IACnDsG,EAAQ8L,aAAe/M,GAAM7G,EAAWM,cAAekB,IACvDsG,EAAQ+L,kBACPhN,GAAM7G,EAAWO,iBAAkBiB,IACpCqC,EAAS3B,mBACP4F,EAAQgM,kBACTjN,GAAM5H,EAAgBuC,GAG1B8M,EAAUC,MAAMrJ,KAAK,CACnBpD,OAAQpG,EAAYqG,KAGtBuM,EAAUC,MAAMrJ,KAAK,IAChBlF,KACE8H,EAAQ6L,UAAiB,CAAExT,QAASwP,KAAhB,CAAA,KAG1B7H,EAAQiM,aAAejT,GAAW,EAG/BqD,GAAgE,EACpEvE,WACA3E,YAGGoC,EAAUuC,IAAaoE,EAAOD,OAC7BnE,GACFsB,EAAOtB,SAAS1E,IAAID,MAEpB2E,EAAWsB,EAAOtB,SAAS0B,IAAIrG,GAAQiG,EAAOtB,SAAS4T,OAAOvY,KAI5DsH,GAA0C,CAACtH,EAAM6M,EAAU,CAAA,KAC/D,IAAI1E,EAAQtG,EAAIuG,EAASpI,GACzB,MAAM+Y,EACJ3W,EAAUyK,EAAQlI,WAAavC,EAAUwG,EAASjE,UAwBpD,OAtBAjC,EAAI0F,EAASpI,EAAM,IACbmI,GAAS,CAAA,EACbE,GAAI,IACEF,GAASA,EAAME,GAAKF,EAAME,GAAK,CAAEJ,IAAK,CAAEjI,SAC5CA,OACA8I,OAAO,KACJ+D,KAGP5G,EAAO6C,MAAMzC,IAAIrG,GAEbmI,EACFe,GAAkB,CAChBvE,SAAUvC,EAAUyK,EAAQlI,UACxBkI,EAAQlI,SACRiE,EAASjE,SACb3E,SAGFgU,EAAoBhU,GAAM,EAAM6M,EAAQ1N,OAGnC,IACD4Z,EACA,CAAEpU,SAAUkI,EAAQlI,UAAYiE,EAASjE,UACzC,MACAiE,EAASoQ,YACT,CACE5I,WAAYvD,EAAQuD,SACpBG,IAAKxC,GAAalB,EAAQ0D,KAC1BC,IAAKzC,GAAalB,EAAQ2D,KAC1BF,UAAWvC,GAAqBlB,EAAQyD,WACxCD,UAAWtC,GAAalB,EAAQwD,WAChCI,QAAS1C,GAAalB,EAAQ4D,UAEhC,GACJzQ,OACA8H,WACAE,OAAQF,EACRG,IAAMA,IACJ,GAAIA,EAAK,CACPX,GAAStH,EAAM6M,GACf1E,EAAQtG,EAAIuG,EAASpI,GAErB,MAAMiZ,EAAWvX,EAAYuG,EAAI9I,QAC7B8I,EAAIiR,kBACDjR,EAAIiR,iBAAiB,yBAAyB,IAEjDjR,EACEkR,EItnCD,CAAClR,GACdwD,GAAaxD,IAAQlJ,EAAgBkJ,GJqnCL6I,CAAkBmI,GACpCvL,EAAOvF,EAAME,GAAGqF,MAAQ,GAE9B,GACEyL,EACIzL,EAAKkI,MAAM9I,GAAgBA,IAAWmM,IACtCA,IAAa9Q,EAAME,GAAGJ,IAE1B,OAGFvF,EAAI0F,EAASpI,EAAM,CACjBqI,GAAI,IACCF,EAAME,MACL8Q,EACA,CACEzL,KAAM,IACDA,EAAKlM,OAAOkK,IACfuN,KACIzZ,MAAMC,QAAQoC,EAAImC,EAAgBhE,IAAS,CAAC,IAAM,IAExDiI,IAAK,CAAEhJ,KAAMga,EAASha,KAAMe,SAE9B,CAAEiI,IAAKgR,MAIfjF,EAAoBhU,GAAM,OAAO4B,EAAWqX,QAE5C9Q,EAAQtG,EAAIuG,EAASpI,EAAM,CAAA,GAEvBmI,EAAME,KACRF,EAAME,GAAGS,OAAQ,IAGlBF,EAAS3B,kBAAoB4F,EAAQ5F,qBAClCnH,EAAmBmG,EAAOkB,MAAOnH,KAAS+I,EAAOC,SACnD/C,EAAOiN,QAAQ7M,IAAIrG,IAG1B,EAGGoZ,GAAc,IAClBxQ,EAAS8J,kBACT3D,GAAsB3G,EAASkP,EAAarR,EAAO6C,OAyB/CuQ,GACJ,CAACC,EAASC,IAAcvJ,MAAOwJ,IAC7B,IAAIC,EACAD,IACFA,EAAEE,gBAAkBF,EAAEE,iBACrBF,EAA+BG,SAC7BH,EAA+BG,WAEpC,IAAIC,EACFnZ,EAAYqG,GAMd,GAJAuM,EAAUC,MAAMrJ,KAAK,CACnB8I,cAAc,IAGZnK,EAAS8K,SAAU,CACrB,MAAMjO,OAAEA,EAAMoB,OAAEA,SAAiB8M,IACjC5O,EAAWU,OAASA,EACpBmU,EAAc/S,aAER+M,EAAyBxL,GAGjC,GAAInC,EAAOtB,SAASkV,KAClB,IAAK,MAAM7Z,KAAQiG,EAAOtB,SACxBjC,EAAIkX,EAAa5Z,OAAM4B,GAM3B,GAFAgK,GAAM7G,EAAWU,OAAQ,QAErBwF,EAAclG,EAAWU,QAAS,CACpC4N,EAAUC,MAAMrJ,KAAK,CACnBxE,OAAQ,CAAE,IAEZ,UACQ6T,EAAQM,EAAmCJ,GACjD,MAAO3R,GACP4R,EAAe5R,QAGb0R,SACIA,EAAU,IAAKxU,EAAWU,QAAU+T,GAE5CJ,KACAjE,WAAWiE,IAUb,GAPA/F,EAAUC,MAAMrJ,KAAK,CACnB6I,aAAa,EACbC,cAAc,EACdC,mBAAoB/H,EAAclG,EAAWU,UAAYgU,EACzD7G,YAAa7N,EAAW6N,YAAc,EACtCnN,OAAQV,EAAWU,SAEjBgU,EACF,MAAMA,GAoCNK,GAAqC,CACzC5T,EACA6T,EAAmB,CAAA,KAEnB,MAAMC,EAAgB9T,EAAazF,EAAYyF,GAAclC,EACvDiW,EAAqBxZ,EAAYuZ,GACjCE,EAAqBjP,EAAc/E,GACnCW,EAASqT,EAAqBlW,EAAiBiW,EAMrD,GAJKF,EAAiBI,oBACpBnW,EAAiBgW,IAGdD,EAAiBK,WAAY,CAChC,GAAIL,EAAiBM,gBAAiB,CACpC,MAAMC,EAAgB,IAAIxZ,IAAI,IACzBmF,EAAO6C,SACP7E,OAAOqF,KAAKmD,GAAezI,EAAgB8C,MAEhD,IAAK,MAAMP,KAAa/G,MAAMsU,KAAKwG,GACjCzY,EAAIkD,EAAWK,YAAamB,GACxB7D,EAAImE,EAAQN,EAAW1E,EAAIiF,EAAaP,IACxCkQ,EACElQ,EACA1E,EAAIgF,EAAQN,QAGf,CACL,GAAIlG,GAASqB,EAAYwE,GACvB,IAAK,MAAMlG,KAAQiG,EAAO6C,MAAO,CAC/B,MAAMX,EAAQtG,EAAIuG,EAASpI,GAC3B,GAAImI,GAASA,EAAME,GAAI,CACrB,MAAMsN,EAAiBnW,MAAMC,QAAQ0I,EAAME,GAAGqF,MAC1CvF,EAAME,GAAGqF,KAAK,GACdvF,EAAME,GAAGJ,IAEb,GAAImD,GAAcuK,GAAiB,CACjC,MAAM4E,EAAO5E,EAAe6E,QAAQ,QACpC,GAAID,EAAM,CACRA,EAAKE,QACL,SAOV,IAAK,MAAMlU,KAAaN,EAAO6C,MAC7B2N,EACElQ,EACA1E,EAAIgF,EAAQN,IAKlBO,EAAcrG,EAAYoG,GAE1BwM,EAAUlM,MAAM8C,KAAK,CACnBpD,OAAQ,IAAKA,KAGfwM,EAAUC,MAAMrJ,KAAK,CACnBpD,OAAQ,IAAKA,KAIjBZ,EAAS,CACP6C,MAAOiR,EAAiBM,gBAAkBpU,EAAO6C,MAAQ,IAAIhI,IAC7DoS,QAAS,IAAIpS,IACbqG,MAAO,IAAIrG,IACX6D,SAAU,IAAI7D,IACdsF,MAAO,IAAItF,IACX0F,UAAU,EACV8B,MAAO,IAGTS,EAAOD,OACJ1E,EAAgBoB,WACfuU,EAAiBjB,eACjBiB,EAAiBM,gBAErBtR,EAAO3C,QAAUwC,EAAS3B,iBAE1BoM,EAAUC,MAAMrJ,KAAK,CACnB2I,YAAamH,EAAiBW,gBAC1B3V,EAAW6N,YACX,EACJ1N,SAASgV,IAELH,EAAiBrB,UACf3T,EAAWG,WAET6U,EAAiBI,mBAChB3P,EAAUtE,EAAYlC,KAE/B8O,cAAaiH,EAAiBY,iBAC1B5V,EAAW+N,YAEf1N,YAAa8U,EACT,CAAA,EACAH,EAAiBM,gBACfN,EAAiBI,mBAAqBrT,EACpC2F,GAAezI,EAAgB8C,GAC/B/B,EAAWK,YACb2U,EAAiBI,mBAAqBjU,EACpCuG,GAAezI,EAAgBkC,GAC/B6T,EAAiBrB,UACf3T,EAAWK,YACX,CAAE,EACZC,cAAe0U,EAAiBpB,YAC5B5T,EAAWM,cACX,CAAE,EACNI,OAAQsU,EAAiBa,WAAa7V,EAAWU,OAAS,CAAE,EAC5DuN,qBAAoB+G,EAAiBc,wBACjC9V,EAAWiO,mBAEfD,cAAc,GACd,EAGE0H,GAAoC,CAACvU,EAAY6T,IACrDD,GACE3O,EAAWjF,GACNA,EAAwBY,GACzBZ,EACJ6T,GAqBE1B,GACJjD,IAEArQ,EAAa,IACRA,KACAqQ,EACJ,EAYG1Q,GAAU,CACdd,QAAS,CACP0D,YACA2B,cACA2O,iBACAyB,gBACAxB,YACAnS,cACAiO,aACAyF,eACAxS,YACA8N,YACA7O,YACAiV,eAhvC0C,CAC5C9a,EACA6G,EAAS,GACTkU,EACAC,EACAC,GAAkB,EAClBC,GAA6B,KAE7B,GAAIF,GAAQD,IAAWnS,EAASjE,SAAU,CAExC,GADAoE,EAAOC,QAAS,EACZkS,GAA8B1b,MAAMC,QAAQoC,EAAIuG,EAASpI,IAAQ,CACnE,MAAM4Z,EAAcmB,EAAOlZ,EAAIuG,EAASpI,GAAOgb,EAAKG,KAAMH,EAAKI,MAC/DH,GAAmBvY,EAAI0F,EAASpI,EAAM4Z,GAGxC,GACEsB,GACA1b,MAAMC,QAAQoC,EAAIkD,EAAWU,OAAQzF,IACrC,CACA,MAAMyF,EAASsV,EACblZ,EAAIkD,EAAWU,OAAQzF,GACvBgb,EAAKG,KACLH,EAAKI,MAEPH,GAAmBvY,EAAIqC,EAAWU,OAAQzF,EAAMyF,GKlPzC,EAAIwC,EAAQjI,MACxBuB,EAAQM,EAAIoG,EAAKjI,IAAO6C,QAAU+I,GAAM3D,EAAKjI,EAAK,ELkP7Cqb,CAAgBtW,EAAWU,OAAQzF,GAGrC,IACGoE,EAAgBiB,eACf+N,EAAyB/N,gBAC3B6V,GACA1b,MAAMC,QAAQoC,EAAIkD,EAAWM,cAAerF,IAC5C,CACA,MAAMqF,EAAgB0V,EACpBlZ,EAAIkD,EAAWM,cAAerF,GAC9Bgb,EAAKG,KACLH,EAAKI,MAEPH,GAAmBvY,EAAIqC,EAAWM,cAAerF,EAAMqF,IAGrDjB,EAAgBgB,aAAegO,EAAyBhO,eAC1DL,EAAWK,YAAcqH,GAAezI,EAAgB8C,IAG1DuM,EAAUC,MAAMrJ,KAAK,CACnBjK,OACAkF,QAASwP,EAAU1U,EAAM6G,GACzBzB,YAAaL,EAAWK,YACxBK,OAAQV,EAAWU,OACnBD,QAAST,EAAWS,eAGtB9C,EAAIoE,EAAa9G,EAAM6G,IA2rCvBqC,qBACAoS,WAjrCgB7V,IAClBV,EAAWU,OAASA,EACpB4N,EAAUC,MAAMrJ,KAAK,CACnBxE,OAAQV,EAAWU,OACnBD,SAAS,GACT,EA6qCA+V,eAn5BFvb,GAEAuB,EACEM,EACEkH,EAAOD,MAAQhC,EAAc9C,EAC7BhE,EACA4I,EAAS3B,iBAAmBpF,EAAImC,EAAgBhE,EAAM,IAAM,KA84B9D8Z,UACA0B,oBA3BwB,IAC1BrQ,EAAWvC,EAAS7E,gBACnB6E,EAAS7E,gBAA6B0X,MAAM5U,IAC3C4T,GAAM5T,EAAQ+B,EAAS8S,cACvBrI,EAAUC,MAAMrJ,KAAK,CACnB9E,WAAW,GACX,IAsBF4B,iBA/7BqB,KACvB,IAAK,MAAM/G,KAAQiG,EAAOiN,QAAS,CACjC,MAAM/K,EAAetG,EAAIuG,EAASpI,GAElCmI,IACGA,EAAME,GAAGqF,KACNvF,EAAME,GAAGqF,KAAKkC,OAAO3H,IAASyD,GAAKzD,MAClCyD,GAAKvD,EAAME,GAAGJ,OACnBgB,GAAWjJ,GAGfiG,EAAOiN,QAAU,IAAIpS,GAAK,EAq7BxB6a,aA3SkBhX,IAChBvC,EAAUuC,KACZ0O,EAAUC,MAAMrJ,KAAK,CAAEtF,aACvBoK,GACE3G,GACA,CAACH,EAAKjI,KACJ,MAAMkP,EAAsBrN,EAAIuG,EAASpI,GACrCkP,IACFjH,EAAItD,SAAWuK,EAAa7G,GAAG1D,UAAYA,EAEvCnF,MAAMC,QAAQyP,EAAa7G,GAAGqF,OAChCwB,EAAa7G,GAAGqF,KAAKqG,SAASnD,IAC5BA,EAASjM,SAAWuK,EAAa7G,GAAG1D,UAAYA,CAAQ,OAKhE,GACA,KA0RF0O,YACAjP,kBACA,WAAIgE,GACF,OAAOA,CACR,EACD,eAAItB,GACF,OAAOA,CACR,EACD,UAAIiC,GACF,OAAOA,CACR,EACD,UAAIA,CAAO5J,GACT4J,EAAS5J,CACV,EACD,kBAAI6E,GACF,OAAOA,CACR,EACD,UAAIiC,GACF,OAAOA,CACR,EACD,UAAIA,CAAO9G,GACT8G,EAAS9G,CACV,EACD,cAAI4F,GACF,OAAOA,CACR,EACD,YAAI6D,GACF,OAAOA,CACR,EACD,YAAIA,CAASzJ,GACXyJ,EAAW,IACNA,KACAzJ,EAEN,GAEHgL,UA9eiD1F,IACjDsE,EAAOD,OAAQ,EACfsK,EAA2B,IACtBA,KACA3O,EAAMd,WAEJ+B,GAAW,IACbjB,EACHd,UAAWyP,KAuebkD,UACAhP,YACA+R,gBACAjT,MAriBwC,CACxCpG,EAIAgC,IAEAmJ,EAAWnL,GACPqT,EAAUC,MAAMnJ,UAAU,CACxBF,KAAO2R,GACL5b,EACE4G,OAAUhF,EAAWI,GACrB4Z,KAONhV,EACE5G,EACAgC,GACA,GAghBNyU,WACAV,YACA0E,SACAoB,WArQkD,CAAC7b,EAAM6M,EAAU,CAAA,KAC/DhL,EAAIuG,EAASpI,KACX0B,EAAYmL,EAAQ7K,cACtByU,EAASzW,EAAMS,EAAYoB,EAAImC,EAAgBhE,MAE/CyW,EACEzW,EACA6M,EAAQ7K,cAEVU,EAAIsB,EAAgBhE,EAAMS,EAAYoM,EAAQ7K,gBAG3C6K,EAAQ8L,aACX/M,GAAM7G,EAAWM,cAAerF,GAG7B6M,EAAQ6L,YACX9M,GAAM7G,EAAWK,YAAapF,GAC9B+E,EAAWG,QAAU2H,EAAQ7K,aACzB0S,EAAU1U,EAAMS,EAAYoB,EAAImC,EAAgBhE,KAChD0U,KAGD7H,EAAQ4L,YACX7M,GAAM7G,EAAWU,OAAQzF,GACzBoE,EAAgBoB,SAAWK,KAG7BwN,EAAUC,MAAMrJ,KAAK,IAAKlF,MA0O5B+W,YA3kBqD9b,IACrDA,GACE6J,EAAsB7J,GAAM+T,SAASgI,GACnCnQ,GAAM7G,EAAWU,OAAQsW,KAG7B1I,EAAUC,MAAMrJ,KAAK,CACnBxE,OAAQzF,EAAO+E,EAAWU,OAAS,CAAE,GACrC,EAokBFwD,cACA4O,YACAmE,SAzG8C,CAAChc,EAAM6M,EAAU,CAAA,KAC/D,MAAM1E,EAAQtG,EAAIuG,EAASpI,GACrB2V,EAAiBxN,GAASA,EAAME,GAEtC,GAAIsN,EAAgB,CAClB,MAAMsD,EAAWtD,EAAejI,KAC5BiI,EAAejI,KAAK,GACpBiI,EAAe1N,IAEfgR,EAAS3Q,QACX2Q,EAAS3Q,QACTuE,EAAQoP,cACN9Q,EAAW8N,EAAS1Q,SACpB0Q,EAAS1Q,YA6FfqP,kBAGF,MAAO,IACFlT,GACHwX,YAAaxX,GAEjB,CM9gDA,IAAAyX,GAAe,KACb,MAAMC,EACmB,oBAAhBC,YAA8Bjd,KAAKkd,MAA4B,IAApBD,YAAYC,MAEhE,MAAO,uCAAuC7Z,QAAQ,SAAU8Z,IAC9D,MAAMC,GAAqB,GAAhBC,KAAKC,SAAgBN,GAAK,GAAK,EAE1C,OAAa,KAALG,EAAWC,EAAS,EAAJA,EAAW,GAAKG,SAAS,GAAG,GACpD,ECLJC,GAAe,CACb5c,EACA2C,EACAkK,EAAiC,CAAA,IAEjCA,EAAQ8K,aAAejW,EAAYmL,EAAQ8K,aACvC9K,EAAQgQ,WACR,GAAG7c,KAAQ0B,EAAYmL,EAAQiQ,YAAcna,EAAQkK,EAAQiQ,cAC7D,GCTNC,GAAe,CAAIrc,EAAWvB,IAAwB,IACjDuB,KACAmJ,EAAsB1K,ICJ3B6d,GAAmB7d,GACjBK,MAAMC,QAAQN,GAASA,EAAMmH,KAAI,KAAe,SAAI1E,ECO9B,SAAAqb,GACtBvc,EACAiC,EACAxD,GAEA,MAAO,IACFuB,EAAKsL,MAAM,EAAGrJ,MACdkH,EAAsB1K,MACtBuB,EAAKsL,MAAMrJ,GAElB,CChBA,IAAAua,GAAe,CACbxc,EACAoT,EACAqJ,IAEK3d,MAAMC,QAAQiB,IAIfgB,EAAYhB,EAAKyc,MACnBzc,EAAKyc,QAAMvb,GAEblB,EAAK0c,OAAOD,EAAI,EAAGzc,EAAK0c,OAAOtJ,EAAM,GAAG,IAEjCpT,GARE,GCNX2c,GAAe,CAAI3c,EAAWvB,IAAwB,IACjD0K,EAAsB1K,MACtB0K,EAAsBnJ,ICY3B,IAAA4c,GAAe,CAAI5c,EAAWiC,IAC5BjB,EAAYiB,GACR,GAdN,SAA4BjC,EAAW6c,GACrC,IAAIC,EAAI,EACR,MAAMC,EAAO,IAAI/c,GAEjB,IAAK,MAAMiC,KAAS4a,EAClBE,EAAKL,OAAOza,EAAQ6a,EAAG,GACvBA,IAGF,OAAOjc,EAAQkc,GAAM5a,OAAS4a,EAAO,EACvC,CAKMC,CACEhd,EACCmJ,EAAsBlH,GAAoBgb,MAAK,CAACC,EAAGC,IAAMD,EAAIC,KCrBtEC,GAAe,CAAIpd,EAAWqd,EAAgBC,MAC3Ctd,EAAKqd,GAASrd,EAAKsd,IAAW,CAACtd,EAAKsd,GAAStd,EAAKqd,GAAQ,ECD7DE,GAAe,CAAIrE,EAAkBjX,EAAexD,KAClDya,EAAYjX,GAASxD,EACdya,gBnDgDPnV,GAEAA,EAAMyZ,OAAOlX,EAAuDvC,WEtBtE,SAGEA,GACA,MAAMC,EAAUlB,KACT2a,EAASC,GAAc9a,EAAMwB,UAAS,IACvClB,QACJA,EAAUc,EAAQd,QAAOya,SACzBA,EAAQC,SACRA,EAAQtV,OACRA,EAAM+R,OACNA,EAAStR,EAAY8U,QACrBA,EAAOC,QACPA,EAAOC,QACPA,EAAOP,OACPA,EAAMQ,UACNA,EAASC,eACTA,KACGC,GACDna,EAEEoa,EAAS7O,MAAOrQ,IACpB,IAAImf,GAAW,EACX7f,EAAO,SAEL2E,EAAQyV,cAAarJ,MAAOtP,IAChC,MAAMqe,EAAW,IAAIC,SACrB,IAAIC,EAAe,GAEnB,IACEA,EAAeC,KAAKC,UAAUze,GAC9B,MAAM0e,GAAA,CAER,MAAMC,EAAoBlW,EAAQvF,EAAQkD,aAE1C,IAAK,MAAMxF,KAAO+d,EAChBN,EAASO,OAAOhe,EAAK+d,EAAkB/d,IAazC,GAVI+c,SACIA,EAAS,CACb3d,OACAf,QACAob,SACAgE,WACAE,iBAIAjW,EACF,IACE,MAAMuW,EAAgC,CACpChB,GAAWA,EAAQ,gBACnBC,GACA5P,MAAMzP,GAAUA,GAASA,EAAM4L,SAAS,UAEpCyU,QAAiBC,MAAMC,OAAO1W,GAAS,CAC3C+R,SACAwD,QAAS,IACJA,KACCC,EAAU,CAAE,eAAgBA,GAAY,CAAA,GAE9CmB,KAAMJ,EAAgCN,EAAeF,IAIrDS,IACCb,GACIA,EAAea,EAASI,QACzBJ,EAASI,OAAS,KAAOJ,EAASI,QAAU,MAEhDd,GAAW,EACXL,GAAWA,EAAQ,CAAEe,aACrBvgB,EAAOygB,OAAOF,EAASI,SAEvBlB,GAAaA,EAAU,CAAEc,aAE3B,MAAO3X,GACPiX,GAAW,EACXL,GAAWA,EAAQ,CAAE5W,aAtDrBjE,CAyDHjE,GAECmf,GAAYra,EAAMb,UACpBa,EAAMb,QAAQyP,UAAUC,MAAMrJ,KAAK,CACjC+I,oBAAoB,IAEtBvO,EAAMb,QAAQiU,SAAS,cAAe,CACpC5Y,WASN,OAJAqE,EAAMiB,WAAU,KACd6Z,GAAW,EAAK,GACf,IAEIF,EACL5a,EAAAuc,cAAAvc,EAAAwc,SAAA,KACG5B,EAAO,CACNW,YAIJvb,EAAAuc,cAAA,OAAA,CACEE,WAAY5B,EACZnV,OAAQA,EACR+R,OAAQA,EACRyD,QAASA,EACTH,SAAUQ,KACND,GAEHN,EAGP,iBVhEE7Z,IAEA,MAAM6Z,SAAEA,KAAa5d,GAAS+D,EAC9B,OACEnB,EAAAuc,cAACxc,EAAgB2c,SAAQ,CAAC7gB,MAAOuB,GAC9B4d,EACwB,4F4DTzB,SAOJ7Z,GAOA,MAAMC,EAAUlB,KACVI,QACJA,EAAUc,EAAQd,QAAO5D,KACzBA,EAAIigB,QACJA,EAAU,KAAIhZ,iBACdA,EAAgBM,MAChBA,GACE9C,GACG4H,EAAQ6T,GAAa5c,EAAMwB,SAASlB,EAAQ2X,eAAevb,IAC5DmgB,EAAM7c,EAAM2B,OAChBrB,EAAQ2X,eAAevb,GAAMsG,IAAI6V,KAE7BiE,EAAY9c,EAAM2B,OAAOoH,GACzBgU,EAAQ/c,EAAM2B,OAAOjF,GACrBsgB,EAAYhd,EAAM2B,QAAO,GAE/Bob,EAAM1a,QAAU3F,EAChBogB,EAAUza,QAAU0G,EACpBzI,EAAQqC,OAAOkB,MAAMd,IAAIrG,GAEzBuH,GACG3D,EAA2D0D,SAC1DtH,EACAuH,GAGJjE,EAAMiB,WACJ,IACEX,EAAQyP,UAAUlM,MAAMgD,UAAU,CAChCF,KAAM,EACJpD,SACA7G,KAAMugB,MAKN,GAAIA,IAAmBF,EAAM1a,UAAY4a,EAAgB,CACvD,MAAM3G,EAAc/X,EAAIgF,EAAQwZ,EAAM1a,SAClCnG,MAAMC,QAAQma,KAChBsG,EAAUtG,GACVuG,EAAIxa,QAAUiU,EAAYtT,IAAI6V,SAInC9R,aACL,CAACzG,IAGH,MAAM4c,EAAeld,EAAMyE,aAMvB0Y,IAEAH,EAAU3a,SAAU,EACpB/B,EAAQkX,eAAe9a,EAAMygB,EAAwB,GAEvD,CAAC7c,EAAS5D,IAqRZ,OA5GAsD,EAAMiB,WAAU,KAQd,GAPAX,EAAQmF,OAAOC,QAAS,EAExB0F,GAAU1O,EAAM4D,EAAQqC,SACtBrC,EAAQyP,UAAUC,MAAMrJ,KAAK,IACxBrG,EAAQmB,aAIbub,EAAU3a,WACRuI,GAAmBtK,EAAQgF,SAASuF,MAAMC,YAC1CxK,EAAQmB,WAAW+N,eACpB5E,GAAmBtK,EAAQgF,SAAS6J,gBAAgBrE,WAErD,GAAIxK,EAAQgF,SAAS8K,SACnB9P,EAAQ+P,WAAW,CAAC3T,IAAOyb,MAAMxZ,IAC/B,MAAM4F,EAAQhG,EAAII,EAAOwD,OAAQzF,GAC3B0gB,EAAgB7e,EAAI+B,EAAQmB,WAAWU,OAAQzF,IAGnD0gB,GACM7Y,GAAS6Y,EAAczhB,MACxB4I,IACE6Y,EAAczhB,OAAS4I,EAAM5I,MAC5ByhB,EAAcjY,UAAYZ,EAAMY,SACpCZ,GAASA,EAAM5I,QAEnB4I,EACInF,EAAIkB,EAAQmB,WAAWU,OAAQzF,EAAM6H,GACrC+D,GAAMhI,EAAQmB,WAAWU,OAAQzF,GACrC4D,EAAQyP,UAAUC,MAAMrJ,KAAK,CAC3BxE,OAAQ7B,EAAQmB,WAAWU,iBAI5B,CACL,MAAM0C,EAAetG,EAAI+B,EAAQwE,QAASpI,IAExCmI,IACAA,EAAME,IAEJ6F,GAAmBtK,EAAQgF,SAAS6J,gBAAgBrE,YACpDF,GAAmBtK,EAAQgF,SAASuF,MAAMC,YAG5C2B,GACE5H,EACAvE,EAAQqC,OAAOtB,SACff,EAAQkD,YACRlD,EAAQgF,SAAS4K,eAAiBrQ,EAClCS,EAAQgF,SAASsH,2BACjB,GACAuL,MACC5T,IACEoD,EAAcpD,IACfjE,EAAQyP,UAAUC,MAAMrJ,KAAK,CAC3BxE,OAAQ+J,GACN5L,EAAQmB,WAAWU,OACnBoC,EACA7H,OAQd4D,EAAQyP,UAAUC,MAAMrJ,KAAK,CAC3BjK,OACA6G,OAAQpG,EAAYmD,EAAQkD,eAG9BlD,EAAQqC,OAAOqC,OACbyG,GAAsBnL,EAAQwE,SAAS,CAACH,EAAK3G,KAC3C,GACEsC,EAAQqC,OAAOqC,OACfhH,EAAIwN,WAAWlL,EAAQqC,OAAOqC,QAC9BL,EAAIK,MAGJ,OADAL,EAAIK,QACG,CAET,IAGJ1E,EAAQqC,OAAOqC,MAAQ,GAEvB1E,EAAQiC,YACRya,EAAU3a,SAAU,CAAK,GACxB,CAAC0G,EAAQrM,EAAM4D,IAElBN,EAAMiB,WAAU,MACb1C,EAAI+B,EAAQkD,YAAa9G,IAAS4D,EAAQkX,eAAe9a,GAEnD,KAQL4D,EAAQgF,SAAS3B,kBAAoBA,EACjCrD,EAAQqF,WAAWjJ,GARD,EAACA,EAAyBb,KAC9C,MAAMgJ,EAAetG,EAAI+B,EAAQwE,QAASpI,GACtCmI,GAASA,EAAME,KACjBF,EAAME,GAAGS,MAAQ3J,IAMjB0J,CAAc7I,GAAM,EAAM,IAE/B,CAACA,EAAM4D,EAASqc,EAAShZ,IAErB,CACL0Z,KAAMrd,EAAMyE,aAlMD,CAACgW,EAAgBC,KAC5B,MAAMyC,EAA0B7c,EAAQ2X,eAAevb,GACvD8d,GAAY2C,EAAyB1C,EAAQC,GAC7CF,GAAYqC,EAAIxa,QAASoY,EAAQC,GACjCwC,EAAaC,GACbP,EAAUO,GACV7c,EAAQkX,eACN9a,EACAygB,EACA3C,GACA,CACE3C,KAAM4C,EACN3C,KAAM4C,IAER,EACD,GAmL6B,CAACwC,EAAcxgB,EAAM4D,IACnDgd,KAAMtd,EAAMyE,aAjLD,CAAC+L,EAAcqJ,KAC1B,MAAMsD,EAA0B7c,EAAQ2X,eAAevb,GACvDkd,GAAYuD,EAAyB3M,EAAMqJ,GAC3CD,GAAYiD,EAAIxa,QAASmO,EAAMqJ,GAC/BqD,EAAaC,GACbP,EAAUO,GACV7c,EAAQkX,eACN9a,EACAygB,EACAvD,GACA,CACE/B,KAAMrH,EACNsH,KAAM+B,IAER,EACD,GAkK6B,CAACqD,EAAcxgB,EAAM4D,IACnDid,QAASvd,EAAMyE,aA7PD,CACd5I,EAGA0N,KAEA,MAAMiU,EAAejX,EAAsBpJ,EAAYtB,IACjDshB,EAA0BpD,GAC9BzZ,EAAQ2X,eAAevb,GACvB8gB,GAEFld,EAAQqC,OAAOqC,MAAQsU,GAAkB5c,EAAM,EAAG6M,GAClDsT,EAAIxa,QAAU0X,GAAU8C,EAAIxa,QAASmb,EAAaxa,IAAI6V,KACtDqE,EAAaC,GACbP,EAAUO,GACV7c,EAAQkX,eAAe9a,EAAMygB,EAAyBpD,GAAW,CAC/DlC,KAAM6B,GAAe7d,IACrB,GA4OkC,CAACqhB,EAAcxgB,EAAM4D,IACzD0b,OAAQhc,EAAMyE,aAtRD,CACb5I,EAGA0N,KAEA,MAAMkU,EAAclX,EAAsBpJ,EAAYtB,IAChDshB,EAA0B1D,GAC9BnZ,EAAQ2X,eAAevb,GACvB+gB,GAEFnd,EAAQqC,OAAOqC,MAAQsU,GACrB5c,EACAygB,EAAwB5d,OAAS,EACjCgK,GAEFsT,EAAIxa,QAAUoX,GAASoD,EAAIxa,QAASob,EAAYza,IAAI6V,KACpDqE,EAAaC,GACbP,EAAUO,GACV7c,EAAQkX,eAAe9a,EAAMygB,EAAyB1D,GAAU,CAC9D5B,KAAM6B,GAAe7d,IACrB,GAiQgC,CAACqhB,EAAcxgB,EAAM4D,IACvDod,OAAQ1d,EAAMyE,aA3OApF,IACd,MAAM8d,EAEAnD,GAAc1Z,EAAQ2X,eAAevb,GAAO2C,GAClDwd,EAAIxa,QAAU2X,GAAc6C,EAAIxa,QAAShD,GACzC6d,EAAaC,GACbP,EAAUO,IACTjhB,MAAMC,QAAQoC,EAAI+B,EAAQwE,QAASpI,KAClC0C,EAAIkB,EAAQwE,QAASpI,OAAM4B,GAC7BgC,EAAQkX,eAAe9a,EAAMygB,EAAyBnD,GAAe,CACnEnC,KAAMxY,GACN,GAgOgC,CAAC6d,EAAcxgB,EAAM4D,IACvDqZ,OAAQ3Z,EAAMyE,aA9ND,CACbpF,EACAxD,EAGA0N,KAEA,MAAMoU,EAAcpX,EAAsBpJ,EAAYtB,IAChDshB,EAA0BS,GAC9Btd,EAAQ2X,eAAevb,GACvB2C,EACAse,GAEFrd,EAAQqC,OAAOqC,MAAQsU,GAAkB5c,EAAM2C,EAAOkK,GACtDsT,EAAIxa,QAAUub,GAASf,EAAIxa,QAAShD,EAAOse,EAAY3a,IAAI6V,KAC3DqE,EAAaC,GACbP,EAAUO,GACV7c,EAAQkX,eAAe9a,EAAMygB,EAAyBS,GAAU,CAC9D/F,KAAMxY,EACNyY,KAAM4B,GAAe7d,IACrB,GA0MgC,CAACqhB,EAAcxgB,EAAM4D,IACvDud,OAAQ7d,EAAMyE,aApKD,CACbpF,EACAxD,KAEA,MAAMwH,EAAclG,EAAYtB,GAC1BshB,EAA0BxC,GAC9Bra,EAAQ2X,eAENvb,GACF2C,EACAgE,GAEFwZ,EAAIxa,QAAU,IAAI8a,GAAyBna,KAAI,CAAC8a,EAAM5D,IACnD4D,GAAQ5D,IAAM7a,EAAuBwd,EAAIxa,QAAQ6X,GAA3BrB,OAEzBqE,EAAaC,GACbP,EAAU,IAAIO,IACd7c,EAAQkX,eACN9a,EACAygB,EACAxC,GACA,CACE9C,KAAMxY,EACNyY,KAAMzU,IAER,GACA,EACD,GAyIiC,CAAC6Z,EAAcxgB,EAAM4D,IACvDnB,QAASa,EAAMyE,aAtIf5I,IAIA,MAAMshB,EAA0B5W,EAAsBpJ,EAAYtB,IAClEghB,EAAIxa,QAAU8a,EAAwBna,IAAI6V,IAC1CqE,EAAa,IAAIC,IACjBP,EAAU,IAAIO,IACd7c,EAAQkX,eACN9a,EACA,IAAIygB,IACA/f,GAAeA,GACnB,IACA,GACA,EACD,GAuHmC,CAAC8f,EAAcxgB,EAAM4D,IACzDyI,OAAQ/I,EAAMwC,SACZ,IACEuG,EAAO/F,KAAI,CAAC6B,EAAOxF,KAAW,IACzBwF,EACH8X,CAACA,GAAUE,EAAIxa,QAAQhD,IAAUwZ,UAErC,CAAC9P,EAAQ4T,IAGf,YCzZgB,SAKdxb,EAAkE,IAElE,MAAM4c,EAAe/d,EAAM2B,YAEzBrD,GACI0f,EAAUhe,EAAM2B,YAA4BrD,IAC3C+B,EAAWkB,GAAmBvB,EAAMwB,SAAkC,CAC3EI,SAAS,EACTK,cAAc,EACdJ,UAAWgG,EAAW1G,EAAMV,eAC5B+O,aAAa,EACbC,cAAc,EACdC,oBAAoB,EACpBxN,SAAS,EACToN,YAAa,EACbxN,YAAa,CAAE,EACfC,cAAe,CAAE,EACjBC,iBAAkB,CAAE,EACpBG,OAAQhB,EAAMgB,QAAU,CAAE,EAC1Bd,SAAUF,EAAME,WAAY,EAC5BkO,SAAS,EACT9O,cAAeoH,EAAW1G,EAAMV,oBAC5BnC,EACA6C,EAAMV,gBAGPsd,EAAa1b,UAChB0b,EAAa1b,QAAU,IACjBlB,EAAMyX,YAAczX,EAAMyX,YAAcvJ,GAAkBlO,GAC9Dd,aAIAc,EAAMyX,aACNzX,EAAMV,gBACLoH,EAAW1G,EAAMV,gBAElBU,EAAMyX,YAAYzB,MAAMhW,EAAMV,cAAeU,EAAMiX,eAIvD,MAAM9X,EAAUyd,EAAa1b,QAAQ/B,QAqFrC,OApFAA,EAAQgF,SAAWnE,EAEnBJ,GAA0B,KACxB,MAAMkd,EAAM3d,EAAQ8B,WAAW,CAC7B/B,UAAWC,EAAQQ,gBACnBwB,SAAU,IAAMf,EAAgB,IAAKjB,EAAQmB,aAC7CuT,cAAc,IAUhB,OAPAzT,GAAiBnE,IAAU,IACtBA,EACHmS,SAAS,MAGXjP,EAAQmB,WAAW8N,SAAU,EAEtB0O,CAAG,GACT,CAAC3d,IAEJN,EAAMiB,WACJ,IAAMX,EAAQ+X,aAAalX,EAAME,WACjC,CAACf,EAASa,EAAME,WAGlBrB,EAAMiB,WAAU,KACVE,EAAM0J,OACRvK,EAAQgF,SAASuF,KAAO1J,EAAM0J,MAE5B1J,EAAMgO,iBACR7O,EAAQgF,SAAS6J,eAAiBhO,EAAMgO,kBAEzC,CAAC7O,EAASa,EAAM0J,KAAM1J,EAAMgO,iBAE/BnP,EAAMiB,WAAU,KACVE,EAAMgB,SACR7B,EAAQ0X,WAAW7W,EAAMgB,QACzB7B,EAAQwV,iBAET,CAACxV,EAASa,EAAMgB,SAEnBnC,EAAMiB,WAAU,KACdE,EAAMwC,kBACJrD,EAAQyP,UAAUC,MAAMrJ,KAAK,CAC3BpD,OAAQjD,EAAQgD,aAChB,GACH,CAAChD,EAASa,EAAMwC,mBAEnB3D,EAAMiB,WAAU,KACd,GAAIX,EAAQQ,gBAAgBc,QAAS,CACnC,MAAMA,EAAUtB,EAAQ8Q,YACpBxP,IAAYvB,EAAUuB,SACxBtB,EAAQyP,UAAUC,MAAMrJ,KAAK,CAC3B/E,eAIL,CAACtB,EAASD,EAAUuB,UAEvB5B,EAAMiB,WAAU,KACVE,EAAMoC,SAAW2D,EAAU/F,EAAMoC,OAAQya,EAAQ3b,UACnD/B,EAAQkW,OAAOrV,EAAMoC,OAAQjD,EAAQgF,SAAS8S,cAC9C4F,EAAQ3b,QAAUlB,EAAMoC,OACxBhC,GAAiByO,IAAK,IAAWA,OAEjC1P,EAAQ4X,wBAET,CAAC5X,EAASa,EAAMoC,SAEnBvD,EAAMiB,WAAU,KACTX,EAAQmF,OAAOD,QAClBlF,EAAQiC,YACRjC,EAAQmF,OAAOD,OAAQ,GAGrBlF,EAAQmF,OAAO3C,QACjBxC,EAAQmF,OAAO3C,OAAQ,EACvBxC,EAAQyP,UAAUC,MAAMrJ,KAAK,IAAKrG,EAAQmB,cAG5CnB,EAAQmD,kBAAkB,IAG5Bsa,EAAa1b,QAAQhC,UAAYD,EAAkBC,EAAWC,GAEvDyd,EAAa1b,OACtB"}