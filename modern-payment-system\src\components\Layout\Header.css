.app-header {
  position: relative;
  z-index: var(--z-sticky);
  background: var(--surface);
  border-bottom: 1px solid var(--border);
  padding: var(--spacing-2) 0;
  height: 56px;
  flex-shrink: 0;
}

.header-content {
  height: 100%;
  width: 100%;
  padding: 0 var(--spacing-6);
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: var(--spacing-4);
}

.header-left {
  display: flex;
  align-items: center;
  gap: var(--spacing-4);
  flex: 0 0 auto;
}

.logo {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

.logo-icon {
  width: 28px;
  height: 28px;
  background: var(--text-primary);
  border-radius: var(--radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--surface);
}

.logo-text h1 {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  margin: 0;
  line-height: 1;
}

.logo-text span {
  font-size: var(--font-size-xs);
  color: var(--text-secondary);
  font-weight: var(--font-weight-medium);
}

.header-right {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  flex: 0 0 auto;
}

/* User Account */
.user-account {
  position: relative;
}

.user-profile-btn {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  padding: var(--spacing-2) var(--spacing-3);
  border: 1px solid var(--border);
  background: var(--surface);
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all var(--transition-normal);
  color: var(--text-primary);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
}

.user-profile-btn:hover {
  background: var(--text-primary);
  color: var(--surface);
}

.theme-toggle {
  width: 32px;
  height: 32px;
  border-radius: var(--radius-md);
  background: var(--surface);
  border: 1px solid var(--border);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all var(--transition-normal);
  color: var(--text-primary);
}

.theme-toggle:hover {
  background: var(--text-primary);
  color: var(--surface);
}

.theme-toggle:active {
  transform: scale(0.95);
}

/* Dark theme adjustments */
[data-theme="dark"] .app-header {
  background: rgba(31, 41, 55, 0.9);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

[data-theme="dark"] .theme-toggle {
  background: var(--gray-800);
  border-color: var(--gray-700);
}

[data-theme="dark"] .theme-toggle:hover {
  background: var(--gray-700);
  border-color: var(--primary-blue);
}

/* Responsive design */
@media (max-width: 768px) {
  .header-content {
    padding: 0 var(--spacing-3);
  }
  
  .header-center {
    display: none;
  }
  
  .logo-text h1 {
    font-size: var(--font-size-xl);
  }
  
  .logo-text span {
    display: none;
  }
}

@media (max-width: 480px) {
  .header-content {
    padding: 0 var(--spacing-2);
  }
  
  .logo {
    gap: var(--spacing-2);
  }
  
  .logo-icon {
    width: 40px;
    height: 40px;
  }
  
  .logo-text h1 {
    font-size: var(--font-size-lg);
  }
  
  .theme-toggle {
    width: 40px;
    height: 40px;
  }
}
