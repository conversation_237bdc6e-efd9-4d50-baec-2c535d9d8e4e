{"version": 3, "names": ["_t", "require", "addComment", "_addComment", "addComments", "_addComments", "shareCommentsWithSiblings", "key", "node", "trailing", "trailingComments", "leading", "leadingComments", "prev", "getSibling", "next", "has<PERSON>rev", "Boolean", "hasNext", "removeIfExisting", "list", "toRemove", "length", "set", "Set", "filter", "el", "has", "type", "content", "line", "comments"], "sources": ["../../src/path/comments.ts"], "sourcesContent": ["// This file contains methods responsible for dealing with comments.\nimport type * as t from \"@babel/types\";\nimport type NodePath from \"./index.ts\";\nimport {\n  addComment as _addComment,\n  addComments as _addComments,\n} from \"@babel/types\";\n\n/**\n * Share comments amongst siblings.\n */\n\nexport function shareCommentsWithSiblings(this: NodePath) {\n  // NOTE: this assumes numbered keys\n  if (typeof this.key === \"string\") return;\n\n  const node = this.node;\n  if (!node) return;\n\n  const trailing = node.trailingComments;\n  const leading = node.leadingComments;\n  if (!trailing && !leading) return;\n\n  const prev = this.getSibling(this.key - 1);\n  const next = this.getSibling(this.key + 1);\n  const hasPrev = Boolean(prev.node);\n  const hasNext = Boolean(next.node);\n\n  if (hasPrev) {\n    if (leading) {\n      prev.addComments(\n        \"trailing\",\n        removeIfExisting(leading, prev.node.trailingComments),\n      );\n    }\n    if (trailing && !hasNext) prev.addComments(\"trailing\", trailing);\n  }\n  if (hasNext) {\n    if (trailing) {\n      next.addComments(\n        \"leading\",\n        removeIfExisting(trailing, next.node.leadingComments),\n      );\n    }\n    if (leading && !hasPrev) next.addComments(\"leading\", leading);\n  }\n}\n\nfunction removeIfExisting<T>(list: T[], toRemove?: T[]): T[] {\n  if (!toRemove?.length) return list;\n  const set = new Set(toRemove);\n  return list.filter(el => {\n    return !set.has(el);\n  });\n}\n\nexport function addComment(\n  this: NodePath,\n  type: t.CommentTypeShorthand,\n  content: string,\n  line?: boolean,\n) {\n  _addComment(this.node, type, content, line);\n}\n\n/**\n * Give node `comments` of the specified `type`.\n */\n\nexport function addComments(\n  this: NodePath,\n  type: t.CommentTypeShorthand,\n  comments: t.Comment[],\n) {\n  _addComments(this.node, type, comments);\n}\n"], "mappings": ";;;;;;;;AAGA,IAAAA,EAAA,GAAAC,OAAA;AAGsB;EAFpBC,UAAU,EAAIC,WAAW;EACzBC,WAAW,EAAIC;AAAY,IAAAL,EAAA;AAOtB,SAASM,yBAAyBA,CAAA,EAAiB;EAExD,IAAI,OAAO,IAAI,CAACC,GAAG,KAAK,QAAQ,EAAE;EAElC,MAAMC,IAAI,GAAG,IAAI,CAACA,IAAI;EACtB,IAAI,CAACA,IAAI,EAAE;EAEX,MAAMC,QAAQ,GAAGD,IAAI,CAACE,gBAAgB;EACtC,MAAMC,OAAO,GAAGH,IAAI,CAACI,eAAe;EACpC,IAAI,CAACH,QAAQ,IAAI,CAACE,OAAO,EAAE;EAE3B,MAAME,IAAI,GAAG,IAAI,CAACC,UAAU,CAAC,IAAI,CAACP,GAAG,GAAG,CAAC,CAAC;EAC1C,MAAMQ,IAAI,GAAG,IAAI,CAACD,UAAU,CAAC,IAAI,CAACP,GAAG,GAAG,CAAC,CAAC;EAC1C,MAAMS,OAAO,GAAGC,OAAO,CAACJ,IAAI,CAACL,IAAI,CAAC;EAClC,MAAMU,OAAO,GAAGD,OAAO,CAACF,IAAI,CAACP,IAAI,CAAC;EAElC,IAAIQ,OAAO,EAAE;IACX,IAAIL,OAAO,EAAE;MACXE,IAAI,CAACT,WAAW,CACd,UAAU,EACVe,gBAAgB,CAACR,OAAO,EAAEE,IAAI,CAACL,IAAI,CAACE,gBAAgB,CACtD,CAAC;IACH;IACA,IAAID,QAAQ,IAAI,CAACS,OAAO,EAAEL,IAAI,CAACT,WAAW,CAAC,UAAU,EAAEK,QAAQ,CAAC;EAClE;EACA,IAAIS,OAAO,EAAE;IACX,IAAIT,QAAQ,EAAE;MACZM,IAAI,CAACX,WAAW,CACd,SAAS,EACTe,gBAAgB,CAACV,QAAQ,EAAEM,IAAI,CAACP,IAAI,CAACI,eAAe,CACtD,CAAC;IACH;IACA,IAAID,OAAO,IAAI,CAACK,OAAO,EAAED,IAAI,CAACX,WAAW,CAAC,SAAS,EAAEO,OAAO,CAAC;EAC/D;AACF;AAEA,SAASQ,gBAAgBA,CAAIC,IAAS,EAAEC,QAAc,EAAO;EAC3D,IAAI,EAACA,QAAQ,YAARA,QAAQ,CAAEC,MAAM,GAAE,OAAOF,IAAI;EAClC,MAAMG,GAAG,GAAG,IAAIC,GAAG,CAACH,QAAQ,CAAC;EAC7B,OAAOD,IAAI,CAACK,MAAM,CAACC,EAAE,IAAI;IACvB,OAAO,CAACH,GAAG,CAACI,GAAG,CAACD,EAAE,CAAC;EACrB,CAAC,CAAC;AACJ;AAEO,SAASxB,UAAUA,CAExB0B,IAA4B,EAC5BC,OAAe,EACfC,IAAc,EACd;EACA3B,WAAW,CAAC,IAAI,CAACK,IAAI,EAAEoB,IAAI,EAAEC,OAAO,EAAEC,IAAI,CAAC;AAC7C;AAMO,SAAS1B,WAAWA,CAEzBwB,IAA4B,EAC5BG,QAAqB,EACrB;EACA1B,YAAY,CAAC,IAAI,CAACG,IAAI,EAAEoB,IAAI,EAAEG,QAAQ,CAAC;AACzC", "ignoreList": []}