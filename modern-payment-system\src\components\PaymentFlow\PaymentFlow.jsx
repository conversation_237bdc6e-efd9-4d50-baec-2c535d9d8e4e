import React, { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { toast } from 'react-hot-toast'
import { ArrowLeft, ArrowRight, CreditCard, CheckCircle } from 'lucide-react'
import StepIndicator from './StepIndicator'
import PaymentMethodStep from './PaymentMethodStep'
import DeliveryOptionsStep from './DeliveryOptionsStep'
import ProductConfigStep from './ProductConfigStep'
import ReviewStep from './ReviewStep'
import SuccessStep from './SuccessStep'
import './PaymentFlow.css'

const PaymentFlow = () => {
  const [currentStep, setCurrentStep] = useState(1)
  const [paymentData, setPaymentData] = useState({
    // Payment method data
    selectedMethod: null,
    savedMethods: [
      {
        id: 1,
        type: 'card',
        brand: 'VISA',
        last4: '4242',
        expiryMonth: '12',
        expiryYear: '2025',
        isDefault: true
      },
      {
        id: 2,
        type: 'upi',
        vpa: 'user@paytm',
        isDefault: false
      }
    ],
    
    // Delivery options
    deliveryOption: 'standard',
    appliedOffers: [],
    
    // Product configuration
    products: [
      {
        id: 1,
        name: 'Premium Wireless Headphones',
        price: 12999,
        quantity: 1,
        image: '/api/placeholder/80/80',
        paymentMethod: null
      },
      {
        id: 2,
        name: 'Smart Watch Series 8',
        price: 24999,
        quantity: 1,
        image: '/api/placeholder/80/80',
        paymentMethod: null
      }
    ],
    
    // Order summary
    subtotal: 37998,
    tax: 6839.64,
    deliveryFee: 0,
    discount: 0,
    total: 44837.64,
    
    // Preferences
    notifications: {
      email: true,
      sms: true,
      push: false
    }
  })

  const steps = [
    {
      id: 1,
      title: 'Payment Method',
      description: 'Choose how you want to pay',
      icon: 'credit_card'
    },
    {
      id: 2,
      title: 'Delivery Options',
      description: 'Select delivery speed and offers',
      icon: 'local_shipping'
    },
    {
      id: 3,
      title: 'Product Config',
      description: 'Configure payment per product',
      icon: 'inventory_2'
    },
    {
      id: 4,
      title: 'Review & Pay',
      description: 'Review and complete payment',
      icon: 'receipt_long'
    },
    {
      id: 5,
      title: 'Success',
      description: 'Payment completed successfully',
      icon: 'check_circle'
    }
  ]

  const updatePaymentData = (updates) => {
    setPaymentData(prev => ({
      ...prev,
      ...updates
    }))
  }

  const calculateTotals = () => {
    const subtotal = paymentData.products.reduce((sum, product) => 
      sum + (product.price * product.quantity), 0
    )
    
    const tax = subtotal * 0.18 // 18% GST
    const deliveryFee = paymentData.deliveryOption === 'express' ? 299 : 
                       paymentData.deliveryOption === 'priority' ? 149 : 0
    
    const discount = paymentData.appliedOffers.reduce((sum, offer) => 
      sum + offer.discount, 0
    )
    
    const total = subtotal + tax + deliveryFee - discount
    
    updatePaymentData({
      subtotal,
      tax,
      deliveryFee,
      discount,
      total
    })
  }

  useEffect(() => {
    calculateTotals()
  }, [paymentData.products, paymentData.deliveryOption, paymentData.appliedOffers])

  const canProceedToNext = () => {
    switch (currentStep) {
      case 1:
        return paymentData.selectedMethod !== null
      case 2:
        return paymentData.deliveryOption !== null
      case 3:
        return paymentData.products.every(product => 
          product.paymentMethod !== null
        )
      case 4:
        return true
      default:
        return false
    }
  }

  const handleNext = () => {
    if (!canProceedToNext()) {
      toast.error('Please complete the current step before proceeding')
      return
    }

    if (currentStep < steps.length) {
      setCurrentStep(currentStep + 1)
    }
  }

  const handlePrevious = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1)
    }
  }

  const handleStepClick = (stepNumber) => {
    // Allow clicking on completed steps or current step
    if (stepNumber <= currentStep) {
      setCurrentStep(stepNumber)
    }
  }

  const handlePayment = async () => {
    try {
      toast.loading('Processing payment...', { id: 'payment' })
      
      // Simulate payment processing
      await new Promise(resolve => setTimeout(resolve, 3000))
      
      toast.success('Payment successful!', { id: 'payment' })
      setCurrentStep(5) // Move to success step
      
    } catch (error) {
      toast.error('Payment failed. Please try again.', { id: 'payment' })
    }
  }

  const renderCurrentStep = () => {
    switch (currentStep) {
      case 1:
        return (
          <PaymentMethodStep
            paymentData={paymentData}
            onUpdate={updatePaymentData}
          />
        )
      case 2:
        return (
          <DeliveryOptionsStep
            paymentData={paymentData}
            onUpdate={updatePaymentData}
          />
        )
      case 3:
        return (
          <ProductConfigStep
            paymentData={paymentData}
            onUpdate={updatePaymentData}
          />
        )
      case 4:
        return (
          <ReviewStep
            paymentData={paymentData}
            onUpdate={updatePaymentData}
            onPayment={handlePayment}
          />
        )
      case 5:
        return (
          <SuccessStep
            paymentData={paymentData}
            onReset={() => {
              setCurrentStep(1)
              setPaymentData(prev => ({
                ...prev,
                selectedMethod: null,
                deliveryOption: 'standard',
                appliedOffers: [],
                products: prev.products.map(p => ({ ...p, paymentMethod: null }))
              }))
            }}
          />
        )
      default:
        return null
    }
  }

  return (
    <div className="payment-flow">
      <div className="payment-container">
        {/* Step Indicator */}
        <div className="step-indicator-section">
          <StepIndicator
            steps={steps}
            currentStep={currentStep}
            onStepClick={handleStepClick}
          />
        </div>

        {/* Main Content */}
        <div className="payment-content">
          <AnimatePresence mode="wait">
            <motion.div
              key={currentStep}
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              transition={{ duration: 0.3 }}
              className="step-content"
            >
              {renderCurrentStep()}
            </motion.div>
          </AnimatePresence>

          {/* Navigation Buttons */}
          {currentStep < 5 && (
            <div className="payment-navigation">
              {currentStep > 1 && (
                <button
                  className="nav-button secondary"
                  onClick={handlePrevious}
                >
                  <ArrowLeft size={16} />
                  Previous
                </button>
              )}

              <div className="nav-spacer"></div>

              {currentStep < 4 ? (
                <button
                  className={`nav-button primary ${!canProceedToNext() ? 'disabled' : ''}`}
                  onClick={handleNext}
                  disabled={!canProceedToNext()}
                >
                  Next Step
                  <ArrowRight size={16} />
                </button>
              ) : (
                <button
                  className="nav-button success"
                  onClick={handlePayment}
                >
                  <CreditCard size={16} />
                  Pay ₹{paymentData.total.toLocaleString()}
                </button>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default PaymentFlow
