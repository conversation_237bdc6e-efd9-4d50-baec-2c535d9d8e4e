import React from 'react'
import { motion } from 'framer-motion'
import { Truck, Clock, Zap, Gift } from 'lucide-react'
import './Steps.css'

const DeliveryOptionsStep = ({ paymentData, onUpdate }) => {
  const deliveryOptions = [
    {
      id: 'express',
      name: 'Express Delivery',
      description: 'Get it today within 2-4 hours',
      icon: <Zap size={24} />,
      price: 299,
      time: '2-4 hours'
    },
    {
      id: 'priority',
      name: 'Priority Delivery',
      description: 'Next day delivery',
      icon: <Clock size={24} />,
      price: 149,
      time: '24 hours'
    },
    {
      id: 'standard',
      name: 'Standard Delivery',
      description: 'Free delivery in 3-5 days',
      icon: <Truck size={24} />,
      price: 0,
      time: '3-5 days'
    }
  ]

  const handleOptionSelect = (optionId) => {
    onUpdate({ deliveryOption: optionId })
  }

  return (
    <div className="delivery-options-step">
      <div className="step-header">
        <h2 className="step-title">Delivery Options</h2>
        <p className="step-description">
          Choose your preferred delivery speed and timing
        </p>
      </div>

      <div className="delivery-options-grid">
        {deliveryOptions.map((option) => (
          <motion.div
            key={option.id}
            className={`delivery-option-card ${paymentData.deliveryOption === option.id ? 'selected' : ''}`}
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            onClick={() => handleOptionSelect(option.id)}
          >
            <div className="option-icon">
              {option.icon}
            </div>
            <h3 className="option-name">{option.name}</h3>
            <p className="option-description">{option.description}</p>
            <div className="option-details">
              <span className="option-price">
                {option.price === 0 ? 'Free' : `₹${option.price}`}
              </span>
              <span className="option-time">{option.time}</span>
            </div>
          </motion.div>
        ))}
      </div>

      <div className="offers-section">
        <h3>Available Offers</h3>
        <div className="offer-card">
          <Gift size={20} />
          <div>
            <h4>First Time Buyer</h4>
            <p>Get 10% off on your first order</p>
          </div>
          <button className="apply-btn">Apply</button>
        </div>
      </div>
    </div>
  )
}

export default DeliveryOptionsStep
