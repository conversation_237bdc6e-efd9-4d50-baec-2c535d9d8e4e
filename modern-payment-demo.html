<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Modern Payment Page - Enhanced UX</title>
    <script src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
            padding: 20px;
        }

        .payment-page {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #ffffff;
            border-radius: 16px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            color: #000000;
        }

        /* Progress Indicator */
        .progress-indicator {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 12px;
            border: 1px solid #e0e0e0;
        }

        .progress-step {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 8px;
            font-size: 12px;
            font-weight: 500;
            color: #666666;
        }

        .progress-step.active {
            color: #000000;
            font-weight: 600;
        }

        .progress-step.completed {
            color: #28a745;
        }

        .step-circle {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            font-weight: 600;
            background: #e9ecef;
            color: #666666;
        }

        .progress-step.active .step-circle {
            background: #000000;
            color: #ffffff;
        }

        .progress-step.completed .step-circle {
            background: #28a745;
            color: #ffffff;
        }

        .progress-line {
            width: 60px;
            height: 2px;
            background: #28a745;
            margin: 0 10px;
        }

        .progress-line.inactive {
            background: #e0e0e0;
        }

        /* Smart Suggestion */
        .smart-suggestion {
            background: linear-gradient(135deg, #000000 0%, #333333 100%);
            color: #ffffff;
            padding: 16px 20px;
            border-radius: 12px;
            margin-bottom: 25px;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .suggestion-text {
            flex: 1;
        }

        .quick-pay-btn {
            background: #ffffff;
            color: #000000;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            font-size: 12px;
            font-weight: 600;
            cursor: pointer;
        }

        /* Quick Methods */
        .quick-method {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 16px 20px;
            border: 2px solid #e0e0e0;
            border-radius: 12px;
            background: #ffffff;
            cursor: pointer;
            margin-bottom: 12px;
            transition: all 0.3s ease;
        }

        .quick-method:hover {
            border-color: #000000;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }

        .quick-method.recommended {
            border-color: #28a745;
            background: linear-gradient(135deg, #f8fff9 0%, #ffffff 100%);
        }

        .method-header {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .method-icon {
            font-size: 24px;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #f8f9fa;
            border-radius: 8px;
        }

        .method-name {
            font-weight: 600;
            font-size: 16px;
            color: #000000;
            margin-bottom: 4px;
        }

        .recommended-badge {
            font-size: 10px;
            padding: 2px 6px;
            border-radius: 4px;
            background: #28a745;
            color: #ffffff;
            margin-left: 8px;
        }

        .method-meta {
            display: flex;
            gap: 12px;
            font-size: 12px;
            color: #666666;
        }

        /* Payment Method Cards */
        .payment-method-card {
            border: 2px solid #e0e0e0;
            border-radius: 12px;
            padding: 20px;
            background: #ffffff;
            cursor: pointer;
            margin-bottom: 16px;
            transition: all 0.3s ease;
        }

        .payment-method-card:hover {
            border-color: #000000;
            transform: translateY(-2px);
            box-shadow: 0 8px 24px rgba(0,0,0,0.1);
        }

        .payment-method-card.selected {
            border-color: #000000;
            background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
        }

        /* Order Summary */
        .order-summary-modern {
            background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
            border: 2px solid #e0e0e0;
            border-radius: 16px;
            padding: 24px;
            margin-bottom: 20px;
        }

        .summary-total {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 16px;
            font-weight: 600;
            padding-top: 12px;
            border-top: 2px solid #e0e0e0;
        }

        .total-amount {
            font-size: 20px;
            font-weight: 700;
            color: #000000;
        }

        /* Pay Button */
        .pay-now-btn-modern {
            width: 100%;
            padding: 18px 24px;
            background: linear-gradient(135deg, #000000 0%, #333333 100%);
            color: #ffffff;
            border: none;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            box-shadow: 0 4px 16px rgba(0,0,0,0.2);
            transition: all 0.3s ease;
        }

        .pay-now-btn-modern:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.3);
        }

        .section {
            margin-bottom: 30px;
        }

        .section-title {
            font-size: 18px;
            font-weight: 600;
            color: #000000;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
    </style>
</head>
<body>
    <div id="root"></div>

    <script type="text/babel">
        const { useState } = React;

        const ModernPaymentPage = () => {
            const [selectedPaymentMethod, setSelectedPaymentMethod] = useState('upi');
            const [isProcessing, setIsProcessing] = useState(false);

            const handlePayNow = () => {
                setIsProcessing(true);
                setTimeout(() => {
                    setIsProcessing(false);
                    alert('✅ Payment successful! Order confirmed.');
                }, 2000);
            };

            return (
                <div className="payment-page">
                    {/* Progress Indicator */}
                    <div className="progress-indicator">
                        <div className="progress-step completed">
                            <div className="step-circle">✓</div>
                            <span>Shipping</span>
                        </div>
                        <div className="progress-line"></div>
                        <div className="progress-step active">
                            <div className="step-circle">2</div>
                            <span>Payment</span>
                        </div>
                        <div className="progress-line inactive"></div>
                        <div className="progress-step">
                            <div className="step-circle">3</div>
                            <span>Confirm</span>
                        </div>
                    </div>

                    {/* Smart Suggestion */}
                    <div className="smart-suggestion">
                        <span>💡</span>
                        <div className="suggestion-text">
                            <strong>Smart Suggestion:</strong> UPI is recommended for faster checkout
                        </div>
                        <button className="quick-pay-btn" onClick={handlePayNow}>
                            Quick Pay
                        </button>
                    </div>

                    {/* Quick Pay Methods */}
                    <div className="section">
                        <h3 className="section-title">
                            <span>⚡</span>
                            Quick Pay
                        </h3>
                        
                        <div className="quick-method recommended">
                            <div className="method-header">
                                <span className="method-icon">📱</span>
                                <div>
                                    <div className="method-name">
                                        UPI user@upi
                                        <span className="recommended-badge">Recommended</span>
                                    </div>
                                    <div className="method-meta">
                                        <span>Last used: Yesterday</span>
                                        <span style={{color: '#28a745', fontWeight: '500'}}>Instant transfer</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div className="quick-method">
                            <div className="method-header">
                                <span className="method-icon">💳</span>
                                <div>
                                    <div className="method-name">VISA **** 1234</div>
                                    <div className="method-meta">
                                        <span>Last used: 2 days ago</span>
                                        <span style={{color: '#28a745', fontWeight: '500'}}>2% cashback</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Order Summary */}
                    <div className="section">
                        <div className="order-summary-modern">
                            <h3 className="section-title">
                                <span>📋</span>
                                Order Summary
                            </h3>
                            
                            <div className="summary-total">
                                <span>Total Amount</span>
                                <span className="total-amount">€42.99</span>
                            </div>
                        </div>
                    </div>

                    {/* Pay Button */}
                    <div className="section">
                        <button 
                            className="pay-now-btn-modern" 
                            onClick={handlePayNow}
                            disabled={isProcessing}
                        >
                            {isProcessing ? (
                                <>Processing...</>
                            ) : (
                                <>
                                    <span>🔒</span>
                                    Pay €42.99
                                </>
                            )}
                        </button>
                    </div>
                </div>
            );
        };

        ReactDOM.render(<ModernPaymentPage />, document.getElementById('root'));
    </script>
</body>
</html>
