<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Modern Payment Page - Enhanced UX</title>
    <script src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
            padding: 20px;
        }

        .payment-page {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #ffffff;
            border-radius: 16px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            color: #000000;
        }

        /* Progress Indicator */
        .progress-indicator {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 12px;
            border: 1px solid #e0e0e0;
        }

        .progress-step {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 8px;
            font-size: 12px;
            font-weight: 500;
            color: #666666;
        }

        .progress-step.active {
            color: #000000;
            font-weight: 600;
        }

        .progress-step.completed {
            color: #28a745;
        }

        .step-circle {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            font-weight: 600;
            background: #e9ecef;
            color: #666666;
        }

        .progress-step.active .step-circle {
            background: #000000;
            color: #ffffff;
        }

        .progress-step.completed .step-circle {
            background: #28a745;
            color: #ffffff;
        }

        .progress-line {
            width: 60px;
            height: 2px;
            background: #28a745;
            margin: 0 10px;
        }

        .progress-line.inactive {
            background: #e0e0e0;
        }

        /* Smart Suggestion */
        .smart-suggestion {
            background: linear-gradient(135deg, #000000 0%, #333333 100%);
            color: #ffffff;
            padding: 16px 20px;
            border-radius: 12px;
            margin-bottom: 25px;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .suggestion-text {
            flex: 1;
        }

        .quick-pay-btn {
            background: #ffffff;
            color: #000000;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            font-size: 12px;
            font-weight: 600;
            cursor: pointer;
        }

        /* Quick Methods */
        .quick-method {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 16px 20px;
            border: 2px solid #e0e0e0;
            border-radius: 12px;
            background: #ffffff;
            cursor: pointer;
            margin-bottom: 12px;
            transition: all 0.3s ease;
        }

        .quick-method:hover {
            border-color: #000000;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }

        .quick-method.recommended {
            border-color: #28a745;
            background: linear-gradient(135deg, #f8fff9 0%, #ffffff 100%);
        }

        .quick-method.default {
            border-color: #007bff;
            background: linear-gradient(135deg, #f8fbff 0%, #ffffff 100%);
        }

        .method-header {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .default-badge, .recommended-badge, .verified-badge {
            font-size: 10px;
            padding: 2px 6px;
            border-radius: 4px;
            font-weight: 500;
            margin-left: 6px;
        }

        .default-badge {
            background: #007bff;
            color: #ffffff;
        }

        .recommended-badge {
            background: #28a745;
            color: #ffffff;
        }

        .verified-badge {
            background: #17a2b8;
            color: #ffffff;
        }

        .method-meta {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            font-size: 12px;
            color: #666666;
        }

        .method-icon {
            font-size: 24px;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #f8f9fa;
            border-radius: 8px;
        }

        .method-name {
            font-weight: 600;
            font-size: 16px;
            color: #000000;
            margin-bottom: 4px;
        }

        .recommended-badge {
            font-size: 10px;
            padding: 2px 6px;
            border-radius: 4px;
            background: #28a745;
            color: #ffffff;
            margin-left: 8px;
        }

        .method-meta {
            display: flex;
            gap: 12px;
            font-size: 12px;
            color: #666666;
        }

        /* Payment Method Cards */
        .payment-method-card {
            border: 2px solid #e0e0e0;
            border-radius: 12px;
            padding: 20px;
            background: #ffffff;
            cursor: pointer;
            margin-bottom: 16px;
            transition: all 0.3s ease;
        }

        .payment-method-card:hover {
            border-color: #000000;
            transform: translateY(-2px);
            box-shadow: 0 8px 24px rgba(0,0,0,0.1);
        }

        .payment-method-card.selected {
            border-color: #000000;
            background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
        }

        /* Order Summary */
        .order-summary-modern {
            background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
            border: 2px solid #e0e0e0;
            border-radius: 16px;
            padding: 24px;
            margin-bottom: 20px;
        }

        .summary-total {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 16px;
            font-weight: 600;
            padding-top: 12px;
            border-top: 2px solid #e0e0e0;
        }

        .total-amount {
            font-size: 20px;
            font-weight: 700;
            color: #000000;
        }

        /* Pay Button */
        .pay-now-btn-modern {
            width: 100%;
            padding: 18px 24px;
            background: linear-gradient(135deg, #000000 0%, #333333 100%);
            color: #ffffff;
            border: none;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            box-shadow: 0 4px 16px rgba(0,0,0,0.2);
            transition: all 0.3s ease;
        }

        .pay-now-btn-modern:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.3);
        }

        .section {
            margin-bottom: 30px;
        }

        .section-title {
            font-size: 18px;
            font-weight: 600;
            color: #000000;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
    </style>
</head>
<body>
    <div id="root"></div>

    <script type="text/babel">
        const { useState } = React;

        const ModernPaymentPage = () => {
            const [selectedPaymentMethod, setSelectedPaymentMethod] = useState('upi');
            const [isProcessing, setIsProcessing] = useState(false);
            const [savedMethodExpanded, setSavedMethodExpanded] = useState(false);

            const savedPaymentMethods = [
                {
                    id: 1,
                    type: 'VISA',
                    details: '**** 1234',
                    icon: '💳',
                    isDefault: true,
                    lastUsed: '2 days ago',
                    cashback: '2% cashback',
                    recommended: false,
                    expiryDate: '12/26'
                },
                {
                    id: 2,
                    type: 'UPI',
                    details: 'user@upi',
                    icon: '📱',
                    isDefault: false,
                    lastUsed: 'Yesterday',
                    cashback: 'Instant transfer',
                    recommended: true,
                    provider: 'Google Pay'
                },
                {
                    id: 3,
                    type: 'Mastercard',
                    details: '**** 5678',
                    icon: '💳',
                    isDefault: false,
                    lastUsed: '1 week ago',
                    cashback: '1.5% cashback',
                    recommended: false,
                    expiryDate: '08/25'
                },
                {
                    id: 4,
                    type: 'PayPal',
                    details: '<EMAIL>',
                    icon: '🅿️',
                    isDefault: false,
                    lastUsed: '3 days ago',
                    cashback: 'Buyer protection',
                    recommended: false,
                    verified: true
                }
            ];

            const paymentMethods = [
                {
                    id: 'upi',
                    label: 'UPI',
                    icon: '📱',
                    processingTime: 'Instant',
                    fees: 'Free',
                    popularity: 95,
                    recommended: true,
                    description: 'Pay instantly using UPI apps',
                    benefits: ['Instant transfer', 'No fees', '24/7 available'],
                    providers: ['Google Pay', 'PhonePe', 'Paytm', 'BHIM']
                },
                {
                    id: 'card',
                    label: 'Credit/Debit Card',
                    icon: '💳',
                    processingTime: '2-3 mins',
                    fees: '2.5%',
                    popularity: 88,
                    recommended: false,
                    description: 'Visa, Mastercard, RuPay accepted',
                    benefits: ['EMI available', 'Reward points', 'Secure payments'],
                    providers: ['Visa', 'Mastercard', 'RuPay', 'American Express']
                },
                {
                    id: 'wallet',
                    label: 'Wallet',
                    icon: '👛',
                    processingTime: 'Instant',
                    fees: 'Free',
                    popularity: 72,
                    recommended: false,
                    description: 'Pay from your digital wallet',
                    benefits: ['Quick checkout', 'Cashback offers'],
                    providers: ['Paytm', 'PhonePe', 'Amazon Pay', 'MobiKwik']
                },
                {
                    id: 'netbanking',
                    label: 'Net Banking',
                    icon: '🏦',
                    processingTime: '3-5 mins',
                    fees: 'Free',
                    popularity: 65,
                    recommended: false,
                    description: 'Pay directly from your bank account',
                    benefits: ['Direct bank transfer', 'High security'],
                    providers: ['SBI', 'HDFC', 'ICICI', 'Axis Bank']
                },
                {
                    id: 'cod',
                    label: 'Cash on Delivery',
                    icon: '💵',
                    processingTime: 'On delivery',
                    fees: '₹40',
                    popularity: 45,
                    recommended: false,
                    description: 'Pay when you receive your order',
                    benefits: ['No advance payment', 'Inspect before paying'],
                    providers: ['Cash', 'Card on delivery']
                },
                {
                    id: 'bnpl',
                    label: 'Buy Now, Pay Later',
                    icon: '📅',
                    processingTime: 'Instant',
                    fees: 'Varies',
                    popularity: 38,
                    recommended: false,
                    description: 'Split payments into easy EMIs',
                    benefits: ['No interest EMI', 'Flexible tenure'],
                    providers: ['Simpl', 'LazyPay', 'ZestMoney', 'Slice']
                },
                {
                    id: 'crypto',
                    label: 'Cryptocurrency',
                    icon: '₿',
                    processingTime: '10-30 mins',
                    fees: '1%',
                    popularity: 12,
                    recommended: false,
                    description: 'Pay with Bitcoin, Ethereum & more',
                    benefits: ['Decentralized', 'Global payments'],
                    providers: ['Bitcoin', 'Ethereum', 'USDT', 'Litecoin']
                },
                {
                    id: 'gift-card',
                    label: 'Gift Card',
                    icon: '🎁',
                    processingTime: 'Instant',
                    fees: 'Free',
                    popularity: 25,
                    recommended: false,
                    description: 'Redeem your gift cards',
                    benefits: ['Use existing balance', 'No expiry'],
                    providers: ['Amazon', 'Flipkart', 'Store Credit']
                }
            ];

            const handlePayNow = () => {
                setIsProcessing(true);
                setTimeout(() => {
                    setIsProcessing(false);
                    alert('✅ Payment successful! Order confirmed.');
                }, 2000);
            };

            return (
                <div className="payment-page">
                    {/* Progress Indicator */}
                    <div className="progress-indicator">
                        <div className="progress-step completed">
                            <div className="step-circle">✓</div>
                            <span>Shipping</span>
                        </div>
                        <div className="progress-line"></div>
                        <div className="progress-step active">
                            <div className="step-circle">2</div>
                            <span>Payment</span>
                        </div>
                        <div className="progress-line inactive"></div>
                        <div className="progress-step">
                            <div className="step-circle">3</div>
                            <span>Confirm</span>
                        </div>
                    </div>

                    {/* Smart Suggestion */}
                    <div className="smart-suggestion">
                        <span>💡</span>
                        <div className="suggestion-text">
                            <strong>Smart Suggestion:</strong> UPI is recommended for faster checkout
                        </div>
                        <button className="quick-pay-btn" onClick={handlePayNow}>
                            Quick Pay
                        </button>
                    </div>

                    {/* Quick Pay Methods */}
                    <div className="section">
                        <div style={{display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '15px'}}>
                            <h3 className="section-title">
                                <span>⚡</span>
                                Quick Pay
                            </h3>
                            <button
                                style={{
                                    background: 'none',
                                    border: '1px solid #e0e0e0',
                                    color: '#666666',
                                    padding: '6px 12px',
                                    borderRadius: '6px',
                                    fontSize: '12px',
                                    cursor: 'pointer'
                                }}
                                onClick={() => setSavedMethodExpanded(!savedMethodExpanded)}
                            >
                                {savedMethodExpanded ? 'Show Less' : 'Show All'}
                            </button>
                        </div>

                        {savedPaymentMethods.slice(0, savedMethodExpanded ? savedPaymentMethods.length : 2).map((method) => (
                            <div key={method.id} className={`quick-method ${method.recommended ? 'recommended' : ''} ${method.isDefault ? 'default' : ''}`}>
                                <div className="method-header">
                                    <span className="method-icon">{method.icon}</span>
                                    <div>
                                        <div className="method-name">
                                            {method.type} {method.details}
                                            {method.isDefault && <span className="default-badge">Default</span>}
                                            {method.recommended && <span className="recommended-badge">Recommended</span>}
                                            {method.verified && <span className="verified-badge">✓ Verified</span>}
                                        </div>
                                        <div className="method-meta">
                                            <span>Last used: {method.lastUsed}</span>
                                            <span style={{color: '#28a745', fontWeight: '500'}}>{method.cashback}</span>
                                            {method.expiryDate && <span style={{color: '#dc3545'}}>Expires: {method.expiryDate}</span>}
                                            {method.provider && <span style={{color: '#007bff', fontWeight: '500'}}>{method.provider}</span>}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        ))}

                        <div style={{
                            background: 'none',
                            border: '2px dashed #e0e0e0',
                            color: '#666666',
                            padding: '16px',
                            borderRadius: '12px',
                            textAlign: 'center',
                            cursor: 'pointer',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            gap: '8px',
                            marginTop: '12px'
                        }}>
                            <span style={{fontSize: '16px', fontWeight: 'bold'}}>+</span>
                            Add new payment method
                        </div>
                    </div>

                    {/* All Payment Methods */}
                    <div className="section">
                        <h3 className="section-title">
                            <span>💳</span>
                            Choose Payment Method
                        </h3>

                        {/* Popular Methods */}
                        <div style={{marginBottom: '30px'}}>
                            <h4 style={{
                                fontSize: '16px',
                                fontWeight: '600',
                                color: '#000000',
                                marginBottom: '16px',
                                display: 'flex',
                                alignItems: 'center',
                                gap: '8px'
                            }}>
                                <span>🔥</span>
                                Popular Methods
                            </h4>
                            <div style={{display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(320px, 1fr))', gap: '16px'}}>
                                {paymentMethods.filter(method => method.popularity >= 70).map((method) => (
                                    <div
                                        key={method.id}
                                        className={`payment-method-card ${selectedPaymentMethod === method.id ? 'selected' : ''} ${method.recommended ? 'recommended' : ''}`}
                                        onClick={() => setSelectedPaymentMethod(method.id)}
                                    >
                                        {method.recommended && (
                                            <div className="recommended-tag">
                                                <span>⭐ Recommended</span>
                                            </div>
                                        )}

                                        <div style={{display: 'flex', flexDirection: 'column', gap: '16px'}}>
                                            <div style={{display: 'flex', alignItems: 'center', gap: '12px'}}>
                                                <span style={{fontSize: '24px', width: '48px', height: '48px', display: 'flex', alignItems: 'center', justifyContent: 'center', background: '#f8f9fa', borderRadius: '8px'}}>{method.icon}</span>
                                                <div style={{flex: 1}}>
                                                    <h4 style={{fontSize: '16px', fontWeight: '600', color: '#000000', margin: '0 0 4px 0'}}>{method.label}</h4>
                                                    <p style={{fontSize: '12px', color: '#666666', margin: 0}}>{method.description}</p>
                                                </div>
                                                <div style={{width: '20px', height: '20px'}}>
                                                    <div style={{
                                                        width: '20px',
                                                        height: '20px',
                                                        border: `2px solid ${selectedPaymentMethod === method.id ? '#000000' : '#e0e0e0'}`,
                                                        borderRadius: '50%',
                                                        position: 'relative',
                                                        background: selectedPaymentMethod === method.id ? '#000000' : 'transparent'
                                                    }}>
                                                        {selectedPaymentMethod === method.id && (
                                                            <div style={{
                                                                position: 'absolute',
                                                                top: '50%',
                                                                left: '50%',
                                                                transform: 'translate(-50%, -50%)',
                                                                width: '8px',
                                                                height: '8px',
                                                                background: '#ffffff',
                                                                borderRadius: '50%'
                                                            }}></div>
                                                        )}
                                                    </div>
                                                </div>
                                            </div>

                                            <div style={{display: 'flex', flexDirection: 'column', gap: '12px'}}>
                                                <div style={{display: 'flex', gap: '16px'}}>
                                                    <div style={{display: 'flex', flexDirection: 'column', gap: '2px', flex: 1}}>
                                                        <span style={{color: '#666666', fontSize: '10px', textTransform: 'uppercase', letterSpacing: '0.5px'}}>Processing:</span>
                                                        <span style={{color: '#000000', fontWeight: '500', fontSize: '12px'}}>{method.processingTime}</span>
                                                    </div>
                                                    <div style={{display: 'flex', flexDirection: 'column', gap: '2px', flex: 1}}>
                                                        <span style={{color: '#666666', fontSize: '10px', textTransform: 'uppercase', letterSpacing: '0.5px'}}>Fees:</span>
                                                        <span style={{color: '#000000', fontWeight: '500', fontSize: '12px'}}>{method.fees}</span>
                                                    </div>
                                                </div>

                                                <div style={{display: 'flex', flexWrap: 'wrap', gap: '6px'}}>
                                                    {method.benefits.slice(0, 2).map((benefit, index) => (
                                                        <span key={index} style={{
                                                            background: '#e8f5e8',
                                                            color: '#28a745',
                                                            padding: '2px 6px',
                                                            borderRadius: '4px',
                                                            fontSize: '10px',
                                                            fontWeight: '500'
                                                        }}>✓ {benefit}</span>
                                                    ))}
                                                </div>

                                                <div style={{position: 'relative', height: '4px', background: '#e0e0e0', borderRadius: '2px', overflow: 'hidden', marginTop: '4px'}}>
                                                    <div style={{
                                                        height: '100%',
                                                        background: 'linear-gradient(90deg, #28a745 0%, #20c997 100%)',
                                                        borderRadius: '2px',
                                                        width: `${method.popularity}%`,
                                                        transition: 'width 0.3s ease'
                                                    }}></div>
                                                    <span style={{fontSize: '10px', color: '#666666', marginTop: '4px', display: 'block'}}>{method.popularity}% users prefer</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        </div>

                        {/* Other Methods */}
                        <div style={{marginBottom: '30px'}}>
                            <h4 style={{
                                fontSize: '16px',
                                fontWeight: '600',
                                color: '#000000',
                                marginBottom: '16px',
                                display: 'flex',
                                alignItems: 'center',
                                gap: '8px'
                            }}>
                                <span>💼</span>
                                Other Payment Options
                            </h4>
                            <div style={{display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(280px, 1fr))', gap: '12px'}}>
                                {paymentMethods.filter(method => method.popularity < 70).map((method) => (
                                    <div
                                        key={method.id}
                                        className={`payment-method-card compact ${selectedPaymentMethod === method.id ? 'selected' : ''}`}
                                        onClick={() => setSelectedPaymentMethod(method.id)}
                                        style={{padding: '16px'}}
                                    >
                                        <div style={{display: 'flex', flexDirection: 'column', gap: '12px'}}>
                                            <div style={{display: 'flex', alignItems: 'center', gap: '12px'}}>
                                                <span style={{fontSize: '24px', width: '48px', height: '48px', display: 'flex', alignItems: 'center', justifyContent: 'center', background: '#f8f9fa', borderRadius: '8px'}}>{method.icon}</span>
                                                <div style={{flex: 1}}>
                                                    <h4 style={{fontSize: '16px', fontWeight: '600', color: '#000000', margin: '0 0 4px 0'}}>{method.label}</h4>
                                                    <p style={{fontSize: '12px', color: '#666666', margin: 0}}>{method.description}</p>
                                                </div>
                                                <div style={{width: '20px', height: '20px'}}>
                                                    <div style={{
                                                        width: '20px',
                                                        height: '20px',
                                                        border: `2px solid ${selectedPaymentMethod === method.id ? '#000000' : '#e0e0e0'}`,
                                                        borderRadius: '50%',
                                                        position: 'relative',
                                                        background: selectedPaymentMethod === method.id ? '#000000' : 'transparent'
                                                    }}>
                                                        {selectedPaymentMethod === method.id && (
                                                            <div style={{
                                                                position: 'absolute',
                                                                top: '50%',
                                                                left: '50%',
                                                                transform: 'translate(-50%, -50%)',
                                                                width: '8px',
                                                                height: '8px',
                                                                background: '#ffffff',
                                                                borderRadius: '50%'
                                                            }}></div>
                                                        )}
                                                    </div>
                                                </div>
                                            </div>

                                            <div style={{display: 'flex', flexDirection: 'column', gap: '8px'}}>
                                                <div style={{display: 'flex', gap: '16px'}}>
                                                    <div style={{display: 'flex', flexDirection: 'column', gap: '2px', flex: 1}}>
                                                        <span style={{color: '#666666', fontSize: '10px', textTransform: 'uppercase', letterSpacing: '0.5px'}}>Processing:</span>
                                                        <span style={{color: '#000000', fontWeight: '500', fontSize: '12px'}}>{method.processingTime}</span>
                                                    </div>
                                                    <div style={{display: 'flex', flexDirection: 'column', gap: '2px', flex: 1}}>
                                                        <span style={{color: '#666666', fontSize: '10px', textTransform: 'uppercase', letterSpacing: '0.5px'}}>Fees:</span>
                                                        <span style={{color: '#000000', fontWeight: '500', fontSize: '12px'}}>{method.fees}</span>
                                                    </div>
                                                </div>

                                                <div style={{display: 'flex', flexDirection: 'column', gap: '6px'}}>
                                                    <span style={{fontSize: '10px', color: '#666666', textTransform: 'uppercase', letterSpacing: '0.5px'}}>Supported:</span>
                                                    <div style={{display: 'flex', flexWrap: 'wrap', gap: '4px'}}>
                                                        {method.providers.slice(0, 3).map((provider, index) => (
                                                            <span key={index} style={{
                                                                background: '#f8f9fa',
                                                                color: '#666666',
                                                                padding: '2px 6px',
                                                                borderRadius: '4px',
                                                                fontSize: '10px',
                                                                border: '1px solid #e0e0e0'
                                                            }}>{provider}</span>
                                                        ))}
                                                        {method.providers.length > 3 && (
                                                            <span style={{
                                                                background: '#e9ecef',
                                                                color: '#495057',
                                                                padding: '2px 6px',
                                                                borderRadius: '4px',
                                                                fontSize: '10px',
                                                                fontWeight: '500',
                                                                border: '1px solid #e0e0e0'
                                                            }}>+{method.providers.length - 3} more</span>
                                                        )}
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        </div>
                    </div>

                    {/* Order Summary */}
                    <div className="section">
                        <div className="order-summary-modern">
                            <h3 className="section-title">
                                <span>📋</span>
                                Order Summary
                            </h3>

                            <div className="summary-total">
                                <span>Total Amount</span>
                                <span className="total-amount">€42.99</span>
                            </div>
                        </div>
                    </div>

                    {/* Pay Button */}
                    <div className="section">
                        <button 
                            className="pay-now-btn-modern" 
                            onClick={handlePayNow}
                            disabled={isProcessing}
                        >
                            {isProcessing ? (
                                <>Processing...</>
                            ) : (
                                <>
                                    <span>🔒</span>
                                    Pay €42.99
                                </>
                            )}
                        </button>
                    </div>
                </div>
            );
        };

        ReactDOM.render(<ModernPaymentPage />, document.getElementById('root'));
    </script>
</body>
</html>
