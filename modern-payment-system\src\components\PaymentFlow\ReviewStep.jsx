import React from 'react'
import { motion } from 'framer-motion'
import { ShoppingCart, CreditCard, Truck, Receipt } from 'lucide-react'
import './Steps.css'

const ReviewStep = ({ paymentData, onUpdate, onPayment }) => {
  return (
    <div className="review-step">
      <div className="step-header">
        <h2 className="step-title">Review & Pay</h2>
        <p className="step-description">
          Review your order details and complete the payment
        </p>
      </div>

      <div className="review-sections">
        <div className="review-section">
          <h3>
            <ShoppingCart size={20} />
            Order Items
          </h3>
          {paymentData.products.map((product) => (
            <div key={product.id} className="order-item">
              <span>{product.name}</span>
              <span>₹{product.price.toLocaleString()}</span>
            </div>
          ))}
        </div>

        <div className="review-section">
          <h3>
            <CreditCard size={20} />
            Payment Method
          </h3>
          <p>{paymentData.selectedMethod || 'Not selected'}</p>
        </div>

        <div className="review-section">
          <h3>
            <Truck size={20} />
            Delivery
          </h3>
          <p>{paymentData.deliveryOption || 'Standard'} delivery</p>
        </div>

        <div className="review-section">
          <h3>
            <Receipt size={20} />
            Price Breakdown
          </h3>
          <div className="price-breakdown">
            <div className="price-item">
              <span>Subtotal</span>
              <span>₹{paymentData.subtotal.toLocaleString()}</span>
            </div>
            <div className="price-item">
              <span>Tax</span>
              <span>₹{paymentData.tax.toLocaleString()}</span>
            </div>
            <div className="price-item">
              <span>Delivery</span>
              <span>₹{paymentData.deliveryFee.toLocaleString()}</span>
            </div>
            <div className="price-item total">
              <span>Total</span>
              <span>₹{paymentData.total.toLocaleString()}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default ReviewStep
