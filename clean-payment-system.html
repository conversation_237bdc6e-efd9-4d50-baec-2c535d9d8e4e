<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Clean Payment System - Black & White</title>
    <script src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
            color: #000000;
            line-height: 1.6;
        }

        .payment-container {
            max-width: 1000px;
            margin: 20px auto;
            background: #ffffff;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .header {
            background: #000000;
            color: #ffffff;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 28px;
            font-weight: 600;
            margin-bottom: 8px;
        }

        .header p {
            font-size: 16px;
            opacity: 0.9;
        }

        .progress-steps {
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 20px;
            background: #f8f9fa;
            border-bottom: 1px solid #e0e0e0;
        }

        .step {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 8px;
            font-size: 14px;
            color: #666666;
        }

        .step.active {
            color: #000000;
            font-weight: 600;
        }

        .step.completed {
            color: #000000;
        }

        .step-circle {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: #e0e0e0;
            color: #666666;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            font-size: 14px;
        }

        .step.active .step-circle {
            background: #000000;
            color: #ffffff;
        }

        .step.completed .step-circle {
            background: #000000;
            color: #ffffff;
        }

        .step-line {
            width: 60px;
            height: 2px;
            background: #e0e0e0;
            margin: 0 15px;
        }

        .step-line.completed {
            background: #000000;
        }

        .main-content {
            display: grid;
            grid-template-columns: 1fr 350px;
            gap: 30px;
            padding: 30px;
        }

        .payment-section {
            display: flex;
            flex-direction: column;
            gap: 30px;
        }

        .section-title {
            font-size: 20px;
            font-weight: 600;
            color: #000000;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .quick-pay-section {
            background: #f8f9fa;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 20px;
        }

        .quick-methods {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .quick-method {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 16px;
            background: #ffffff;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .quick-method:hover {
            border-color: #000000;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .quick-method.recommended {
            border-color: #000000;
            background: #f8f9fa;
        }

        .method-info {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .method-icon {
            width: 40px;
            height: 40px;
            background: #f0f0f0;
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
        }

        .method-details h4 {
            font-size: 16px;
            font-weight: 600;
            color: #000000;
            margin-bottom: 4px;
        }

        .method-meta {
            display: flex;
            gap: 12px;
            font-size: 12px;
            color: #666666;
        }

        .recommended-badge {
            background: #000000;
            color: #ffffff;
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 10px;
            font-weight: 500;
            margin-left: 8px;
        }

        .payment-methods-section {
            background: #ffffff;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 20px;
        }

        .payment-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 16px;
        }

        .payment-card {
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            padding: 20px;
            cursor: pointer;
            transition: all 0.2s ease;
            background: #ffffff;
        }

        .payment-card:hover {
            border-color: #000000;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .payment-card.selected {
            border-color: #000000;
            background: #f8f9fa;
        }

        .card-header {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 16px;
        }

        .card-icon {
            width: 48px;
            height: 48px;
            background: #f0f0f0;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
        }

        .card-title {
            font-size: 18px;
            font-weight: 600;
            color: #000000;
            margin-bottom: 4px;
        }

        .card-description {
            font-size: 14px;
            color: #666666;
            line-height: 1.4;
        }

        .card-stats {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 12px;
            margin-bottom: 16px;
        }

        .stat-item {
            text-align: center;
            padding: 12px;
            background: #f8f9fa;
            border-radius: 6px;
        }

        .stat-label {
            font-size: 11px;
            color: #666666;
            text-transform: uppercase;
            margin-bottom: 4px;
        }

        .stat-value {
            font-size: 14px;
            font-weight: 600;
            color: #000000;
        }

        .benefits {
            display: flex;
            flex-wrap: wrap;
            gap: 6px;
        }

        .benefit-tag {
            background: #f0f0f0;
            color: #000000;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 11px;
            font-weight: 500;
        }

        .sidebar {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .order-summary {
            background: #ffffff;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 20px;
        }

        .summary-items {
            display: flex;
            flex-direction: column;
            gap: 12px;
            margin-bottom: 20px;
        }

        .summary-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 16px;
        }

        .summary-item.total {
            border-top: 1px solid #e0e0e0;
            padding-top: 12px;
            font-size: 18px;
            font-weight: 600;
        }

        .pay-button {
            width: 100%;
            padding: 16px;
            background: #000000;
            color: #ffffff;
            border: none;
            border-radius: 6px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            margin-bottom: 16px;
        }

        .pay-button:hover {
            background: #333333;
        }

        .pay-button:disabled {
            background: #cccccc;
            cursor: not-allowed;
        }

        .security-info {
            display: flex;
            justify-content: center;
            gap: 16px;
            font-size: 12px;
            color: #666666;
        }

        .security-item {
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .smart-suggestions {
            background: #f8f9fa;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 16px;
        }

        .suggestion-header {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 12px;
        }

        .suggestion-content {
            font-size: 14px;
            color: #666666;
            margin-bottom: 12px;
        }

        .quick-pay-btn {
            background: #000000;
            color: #ffffff;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            font-size: 14px;
            cursor: pointer;
            transition: background 0.2s ease;
        }

        .quick-pay-btn:hover {
            background: #333333;
        }

        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
                gap: 20px;
                padding: 20px;
            }
            
            .payment-grid {
                grid-template-columns: 1fr;
            }
            
            .progress-steps {
                padding: 15px;
            }
            
            .step-line {
                width: 40px;
            }
        }
    </style>
</head>
<body>
    <div id="root"></div>

    <script type="text/babel">
        const { useState, useEffect } = React;

        const CleanPaymentSystem = () => {
            const [selectedMethod, setSelectedMethod] = useState('');
            const [isProcessing, setIsProcessing] = useState(false);
            const [showAllMethods, setShowAllMethods] = useState(false);

            const savedMethods = [
                {
                    id: 1,
                    type: 'VISA',
                    details: '**** 1234',
                    icon: '💳',
                    lastUsed: '2 days ago',
                    benefit: '2% cashback',
                    recommended: false
                },
                {
                    id: 2,
                    type: 'UPI',
                    details: 'user@upi',
                    icon: '📱',
                    lastUsed: 'Yesterday',
                    benefit: 'Instant transfer',
                    recommended: true
                }
            ];

            const paymentMethods = [
                {
                    id: 'upi',
                    name: 'UPI',
                    icon: '📱',
                    description: 'Pay instantly using UPI apps like Google Pay, PhonePe',
                    processingTime: 'Instant',
                    fees: 'Free',
                    popularity: 95,
                    benefits: ['Instant transfer', 'No fees', '24/7 available']
                },
                {
                    id: 'card',
                    name: 'Credit/Debit Card',
                    icon: '💳',
                    description: 'Visa, Mastercard, RuPay and other cards accepted',
                    processingTime: '2-3 mins',
                    fees: '2.5%',
                    popularity: 88,
                    benefits: ['EMI available', 'Reward points', 'Global acceptance']
                },
                {
                    id: 'wallet',
                    name: 'Digital Wallet',
                    icon: '👛',
                    description: 'Pay from Paytm, PhonePe, Amazon Pay wallets',
                    processingTime: 'Instant',
                    fees: 'Free',
                    popularity: 72,
                    benefits: ['Quick checkout', 'Cashback offers', 'No card needed']
                },
                {
                    id: 'netbanking',
                    name: 'Net Banking',
                    icon: '🏦',
                    description: 'Direct payment from your bank account',
                    processingTime: '3-5 mins',
                    fees: 'Free',
                    popularity: 65,
                    benefits: ['Direct transfer', 'High security', 'All banks']
                },
                {
                    id: 'cod',
                    name: 'Cash on Delivery',
                    icon: '💵',
                    description: 'Pay when your order is delivered to you',
                    processingTime: 'On delivery',
                    fees: '₹40',
                    popularity: 45,
                    benefits: ['No advance payment', 'Inspect first', 'Cash or card']
                },
                {
                    id: 'bnpl',
                    name: 'Buy Now, Pay Later',
                    icon: '📅',
                    description: 'Split your payment into easy installments',
                    processingTime: 'Instant',
                    fees: 'Varies',
                    popularity: 38,
                    benefits: ['No interest EMI', 'Flexible tenure', 'Quick approval']
                }
            ];

            const handleQuickPay = (methodId) => {
                setSelectedMethod(methodId);
                setTimeout(() => {
                    handlePayment();
                }, 500);
            };

            const handlePayment = () => {
                if (!selectedMethod) {
                    alert('Please select a payment method');
                    return;
                }

                setIsProcessing(true);
                setTimeout(() => {
                    setIsProcessing(false);
                    alert('✅ Payment successful! Your order has been confirmed.');
                }, 2500);
            };

            const getRecommendedMethod = () => {
                return paymentMethods.find(method => method.id === 'upi');
            };

            return (
                <div className="payment-container">
                    {/* Header */}
                    <div className="header">
                        <h1>Secure Payment</h1>
                        <p>Complete your order with your preferred payment method</p>
                    </div>

                    {/* Progress Steps */}
                    <div className="progress-steps">
                        <div className="step completed">
                            <div className="step-circle">✓</div>
                            <span>Shipping</span>
                        </div>
                        <div className="step-line completed"></div>
                        <div className="step active">
                            <div className="step-circle">2</div>
                            <span>Payment</span>
                        </div>
                        <div className="step-line"></div>
                        <div className="step">
                            <div className="step-circle">3</div>
                            <span>Confirm</span>
                        </div>
                    </div>

                    <div className="main-content">
                        <div className="payment-section">
                            {/* Smart Suggestions */}
                            <div className="smart-suggestions">
                                <div className="suggestion-header">
                                    <span>💡</span>
                                    <h3>Smart Suggestion</h3>
                                </div>
                                <div className="suggestion-content">
                                    UPI is recommended for faster checkout with instant processing and no fees
                                </div>
                                <button
                                    className="quick-pay-btn"
                                    onClick={() => handleQuickPay('upi')}
                                >
                                    Quick Pay with UPI
                                </button>
                            </div>

                            {/* Quick Pay Section */}
                            <div className="quick-pay-section">
                                <h2 className="section-title">
                                    <span>⚡</span>
                                    Quick Pay
                                </h2>

                                <div className="quick-methods">
                                    {savedMethods.map((method) => (
                                        <div
                                            key={method.id}
                                            className={`quick-method ${method.recommended ? 'recommended' : ''}`}
                                            onClick={() => handleQuickPay(method.type.toLowerCase())}
                                        >
                                            <div className="method-info">
                                                <div className="method-icon">{method.icon}</div>
                                                <div className="method-details">
                                                    <h4>
                                                        {method.type} {method.details}
                                                        {method.recommended && <span className="recommended-badge">Recommended</span>}
                                                    </h4>
                                                    <div className="method-meta">
                                                        <span>Last used: {method.lastUsed}</span>
                                                        <span>{method.benefit}</span>
                                                    </div>
                                                </div>
                                            </div>
                                            <button style={{
                                                background: 'none',
                                                border: '1px solid #e0e0e0',
                                                padding: '6px 12px',
                                                borderRadius: '4px',
                                                cursor: 'pointer',
                                                fontSize: '12px'
                                            }}>
                                                Pay Now
                                            </button>
                                        </div>
                                    ))}
                                </div>
                            </div>

                            {/* All Payment Methods */}
                            <div className="payment-methods-section">
                                <h2 className="section-title">
                                    <span>💳</span>
                                    Choose Payment Method
                                </h2>

                                <div className="payment-grid">
                                    {paymentMethods.map((method) => (
                                        <div
                                            key={method.id}
                                            className={`payment-card ${selectedMethod === method.id ? 'selected' : ''}`}
                                            onClick={() => setSelectedMethod(method.id)}
                                        >
                                            <div className="card-header">
                                                <div className="card-icon">{method.icon}</div>
                                                <div>
                                                    <h3 className="card-title">{method.name}</h3>
                                                    <p className="card-description">{method.description}</p>
                                                </div>
                                            </div>

                                            <div className="card-stats">
                                                <div className="stat-item">
                                                    <div className="stat-label">Processing</div>
                                                    <div className="stat-value">{method.processingTime}</div>
                                                </div>
                                                <div className="stat-item">
                                                    <div className="stat-label">Fees</div>
                                                    <div className="stat-value">{method.fees}</div>
                                                </div>
                                            </div>

                                            <div className="benefits">
                                                {method.benefits.map((benefit, index) => (
                                                    <span key={index} className="benefit-tag">
                                                        ✓ {benefit}
                                                    </span>
                                                ))}
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            </div>
                        </div>

                        {/* Sidebar */}
                        <div className="sidebar">
                            {/* Order Summary */}
                            <div className="order-summary">
                                <h3 className="section-title">
                                    <span>📋</span>
                                    Order Summary
                                </h3>

                                <div className="summary-items">
                                    <div className="summary-item">
                                        <span>Subtotal</span>
                                        <span>€39.99</span>
                                    </div>
                                    <div className="summary-item">
                                        <span>Shipping</span>
                                        <span>€2.99</span>
                                    </div>
                                    <div className="summary-item">
                                        <span>Tax</span>
                                        <span>€3.20</span>
                                    </div>
                                    <div className="summary-item">
                                        <span style={{color: '#666'}}>Discount</span>
                                        <span style={{color: '#666'}}>-€3.19</span>
                                    </div>
                                    <div className="summary-item total">
                                        <span>Total</span>
                                        <span>€42.99</span>
                                    </div>
                                </div>

                                {selectedMethod && (
                                    <div style={{
                                        background: '#f8f9fa',
                                        padding: '12px',
                                        borderRadius: '6px',
                                        marginBottom: '16px',
                                        border: '1px solid #e0e0e0'
                                    }}>
                                        <div style={{fontSize: '14px', fontWeight: '600', marginBottom: '4px'}}>
                                            Selected: {paymentMethods.find(m => m.id === selectedMethod)?.name}
                                        </div>
                                        <div style={{fontSize: '12px', color: '#666666'}}>
                                            Processing: {paymentMethods.find(m => m.id === selectedMethod)?.processingTime}
                                        </div>
                                    </div>
                                )}

                                <button
                                    className="pay-button"
                                    onClick={handlePayment}
                                    disabled={isProcessing || !selectedMethod}
                                >
                                    {isProcessing ? 'Processing...' : 'Pay €42.99 Securely'}
                                </button>

                                <div className="security-info">
                                    <div className="security-item">
                                        <span>🔒</span>
                                        <span>SSL Secured</span>
                                    </div>
                                    <div className="security-item">
                                        <span>🛡️</span>
                                        <span>PCI Compliant</span>
                                    </div>
                                    <div className="security-item">
                                        <span>✓</span>
                                        <span>Verified</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            );
        };

        ReactDOM.render(<CleanPaymentSystem />, document.getElementById('root'));
    </script>
</body>
</html>
