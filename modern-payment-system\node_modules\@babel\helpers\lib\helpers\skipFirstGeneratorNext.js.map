{"version": 3, "names": ["_skipFirstGeneratorNext", "fn", "it", "apply", "arguments", "next"], "sources": ["../../src/helpers/skipFirstGeneratorNext.js"], "sourcesContent": ["/* @minVersion 7.0.0-beta.0 */\n\nexport default function _skipFirstGeneratorNext(fn) {\n  return function () {\n    var it = fn.apply(this, arguments);\n    it.next();\n    return it;\n  };\n}\n"], "mappings": ";;;;;;AAEe,SAASA,uBAAuBA,CAACC,EAAE,EAAE;EAClD,OAAO,YAAY;IACjB,IAAIC,EAAE,GAAGD,EAAE,CAACE,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;IAClCF,EAAE,CAACG,IAAI,CAAC,CAAC;IACT,OAAOH,EAAE;EACX,CAAC;AACH", "ignoreList": []}