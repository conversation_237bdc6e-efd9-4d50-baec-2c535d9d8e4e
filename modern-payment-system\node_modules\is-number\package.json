{"name": "is-number", "description": "Returns true if a number or string value is a finite number. Useful for regex matches, parsing, user input, etc.", "version": "7.0.0", "homepage": "https://github.com/jonschlinkert/is-number", "author": "<PERSON> (https://github.com/jonschlinkert)", "contributors": ["<PERSON> (http://twitter.com/jonschlink<PERSON>)", "<PERSON><PERSON><PERSON> (https://i.am.charlike.online)", "<PERSON><PERSON><PERSON> (www.rouvenwessling.de)"], "repository": "jonschlinkert/is-number", "bugs": {"url": "https://github.com/jonschlinkert/is-number/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.12.0"}, "scripts": {"test": "mocha"}, "devDependencies": {"ansi": "^0.3.1", "benchmark": "^2.1.4", "gulp-format-md": "^1.0.0", "mocha": "^3.5.3"}, "keywords": ["cast", "check", "coerce", "coercion", "finite", "integer", "is", "isnan", "is-nan", "is-num", "is-number", "isnumber", "isfinite", "istype", "kind", "math", "nan", "num", "number", "numeric", "parseFloat", "parseInt", "test", "type", "typeof", "value"], "verb": {"toc": false, "layout": "default", "tasks": ["readme"], "related": {"list": ["is-plain-object", "is-primitive", "isobject", "kind-of"]}, "plugins": ["gulp-format-md"], "lint": {"reflinks": true}}}