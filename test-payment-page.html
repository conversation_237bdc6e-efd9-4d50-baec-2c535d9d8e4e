<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payment Page - React Test</title>
    <script src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    <style>
        /* Payment Page Styles - Clean Black/White Theme */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
            padding: 20px;
        }

        .payment-page {
            max-width: 700px;
            margin: 0 auto;
            padding: 20px;
            background: #ffffff;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            color: #000000;
        }

        .nav-tabs {
            display: flex;
            margin-bottom: 30px;
            border-bottom: 1px solid #e0e0e0;
        }

        .nav-tab {
            padding: 12px 24px;
            font-size: 16px;
            font-weight: 500;
            color: #666666;
            cursor: pointer;
            border-bottom: 2px solid transparent;
            transition: all 0.2s ease;
        }

        .nav-tab.active {
            color: #000000;
            border-bottom-color: #000000;
            font-weight: 600;
        }

        .payment-content {
            display: flex;
            flex-direction: column;
            gap: 25px;
        }

        .section {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .section-title {
            font-size: 18px;
            font-weight: 600;
            color: #000000;
            margin: 0;
        }

        .saved-methods {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .saved-method {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 16px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            background: #ffffff;
        }

        .method-info {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .method-type {
            font-weight: 600;
            font-size: 16px;
            color: #000000;
        }

        .method-details {
            color: #666666;
            font-size: 14px;
        }

        .remove-btn {
            background: none;
            border: none;
            color: #666666;
            cursor: pointer;
            font-size: 14px;
            text-decoration: underline;
            padding: 4px 8px;
        }

        .remove-btn:hover {
            color: #000000;
        }

        .add-payment-link {
            background: none;
            border: none;
            color: #000000;
            cursor: pointer;
            font-size: 14px;
            text-decoration: underline;
            text-align: left;
            padding: 0;
            margin-top: 8px;
        }

        .payment-methods {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 12px;
        }

        .payment-method {
            display: flex;
            align-items: center;
            gap: 8px;
            cursor: pointer;
            font-size: 14px;
            color: #000000;
        }

        .payment-method input[type="radio"] {
            width: 16px;
            height: 16px;
            margin: 0;
        }

        .payment-variation {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .variation-select, .product-payment-select {
            padding: 8px 12px;
            border: 1px solid #e0e0e0;
            border-radius: 4px;
            font-size: 14px;
            background: #ffffff;
            color: #000000;
            min-width: 120px;
        }

        .checkbox-label {
            display: flex;
            align-items: center;
            gap: 8px;
            cursor: pointer;
            font-size: 14px;
            color: #000000;
        }

        .checkbox-label input[type="checkbox"] {
            width: 16px;
            height: 16px;
            margin: 0;
        }

        .order-summary {
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 20px;
            background: #ffffff;
        }

        .summary-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            padding-bottom: 15px;
            border-bottom: 1px solid #e0e0e0;
        }

        .summary-title {
            font-size: 16px;
            font-weight: 600;
            color: #000000;
        }

        .summary-amount {
            font-size: 18px;
            font-weight: 700;
            color: #000000;
        }

        .pay-now-btn {
            width: 100%;
            padding: 16px;
            background: #000000;
            color: #ffffff;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: background-color 0.2s ease;
        }

        .pay-now-btn:hover {
            background: #333333;
        }

        .navigation-buttons {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 20px;
        }

        .nav-btn {
            background: none;
            border: none;
            color: #000000;
            cursor: pointer;
            font-size: 14px;
            padding: 8px 16px;
            border-radius: 4px;
            transition: background-color 0.2s ease;
        }

        .nav-btn:hover {
            background: #f5f5f5;
        }
    </style>
</head>
<body>
    <div id="root"></div>

    <script type="text/babel">
        const { useState } = React;

        const PaymentPage = () => {
            const [selectedPaymentMethod, setSelectedPaymentMethod] = useState('');
            const [paymentVariation, setPaymentVariation] = useState('same-day');
            const [applyBestOffer, setApplyBestOffer] = useState(false);
            const [productPaymentOption, setProductPaymentOption] = useState('');
            const [sendNotification, setSendNotification] = useState(true);

            const savedPaymentMethods = [
                { id: 1, type: 'VISA', details: '**** 1234', icon: 'VISA' },
                { id: 2, type: 'UPI', details: 'user@upi', icon: 'UPI' }
            ];

            const paymentMethods = [
                { id: 'upi', label: 'UPI' },
                { id: 'card', label: 'Credit/Debit Card' },
                { id: 'wallet', label: 'Wallet' },
                { id: 'cod', label: 'Cash on Delivery' },
                { id: 'bnpl', label: 'Buy Now, Pay Later' }
            ];

            const handlePayNow = () => {
                if (!selectedPaymentMethod) {
                    alert('Please select a payment method');
                    return;
                }
                alert('Payment processing... (Demo)');
            };

            return (
                <div className="payment-page">
                    <div className="nav-tabs">
                        <div className="nav-tab">Shipping</div>
                        <div className="nav-tab active">Payment</div>
                    </div>

                    <div className="payment-content">
                        <div className="section">
                            <h3 className="section-title">Saved Payment Methods</h3>
                            <div className="saved-methods">
                                {savedPaymentMethods.map((method) => (
                                    <div key={method.id} className="saved-method">
                                        <div className="method-info">
                                            <span className="method-type">{method.type}</span>
                                            <span className="method-details">{method.details}</span>
                                        </div>
                                        <button className="remove-btn">Remove</button>
                                    </div>
                                ))}
                            </div>
                            <button className="add-payment-link">Add payment method</button>
                        </div>

                        <div className="section">
                            <h3 className="section-title">Payment Method</h3>
                            <div className="payment-methods">
                                {paymentMethods.map((method) => (
                                    <label key={method.id} className="payment-method">
                                        <input
                                            type="radio"
                                            name="paymentMethod"
                                            value={method.id}
                                            checked={selectedPaymentMethod === method.id}
                                            onChange={(e) => setSelectedPaymentMethod(e.target.value)}
                                        />
                                        <span>{method.label}</span>
                                    </label>
                                ))}
                            </div>
                        </div>

                        <div className="section">
                            <h3 className="section-title">Payment Variation</h3>
                            <div className="payment-variation">
                                <select 
                                    value={paymentVariation}
                                    onChange={(e) => setPaymentVariation(e.target.value)}
                                    className="variation-select"
                                >
                                    <option value="same-day">Same day</option>
                                    <option value="next-day">Next day</option>
                                    <option value="standard">Standard</option>
                                </select>
                                <label className="checkbox-label">
                                    <input
                                        type="checkbox"
                                        checked={applyBestOffer}
                                        onChange={(e) => setApplyBestOffer(e.target.checked)}
                                    />
                                    <span>Apply best offer</span>
                                </label>
                            </div>
                        </div>

                        <div className="section">
                            <h3 className="section-title">Product-Level Payment</h3>
                            <select 
                                value={productPaymentOption}
                                onChange={(e) => setProductPaymentOption(e.target.value)}
                                className="product-payment-select"
                            >
                                <option value="">Payment Option</option>
                                <option value="full">Full Payment</option>
                                <option value="partial">Partial Payment</option>
                                <option value="installment">Installment</option>
                            </select>
                        </div>

                        <div className="section">
                            <div className="order-summary">
                                <div className="summary-header">
                                    <span className="summary-title">Order Summary</span>
                                    <span className="summary-amount">€42.99</span>
                                </div>
                                <label className="checkbox-label">
                                    <input
                                        type="checkbox"
                                        checked={sendNotification}
                                        onChange={(e) => setSendNotification(e.target.checked)}
                                    />
                                    <span>Send payment notification</span>
                                </label>
                            </div>
                        </div>

                        <div className="section">
                            <button className="pay-now-btn" onClick={handlePayNow}>
                                Pay Now
                            </button>
                        </div>

                        <div className="navigation-buttons">
                            <button className="nav-btn">← Back</button>
                            <button className="nav-btn">Next</button>
                        </div>
                    </div>
                </div>
            );
        };

        ReactDOM.render(<PaymentPage />, document.getElementById('root'));
    </script>
</body>
</html>
