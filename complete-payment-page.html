<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Complete Payment System - Professional Design</title>
    <script src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
            color: #000000;
            line-height: 1.6;
        }

        .payment-container {
            max-width: 1200px;
            margin: 20px auto;
            background: #ffffff;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .header {
            background: #000000;
            color: #ffffff;
            padding: 40px;
            text-align: center;
        }

        .header h1 {
            font-size: 32px;
            font-weight: 700;
            margin-bottom: 8px;
        }

        .header p {
            font-size: 18px;
            opacity: 0.9;
        }

        .progress-steps {
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 30px;
            background: #f8f9fa;
            border-bottom: 1px solid #e0e0e0;
        }

        .step {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 8px;
            font-size: 14px;
            color: #666666;
            font-weight: 500;
        }

        .step.active {
            color: #000000;
            font-weight: 600;
        }

        .step.completed {
            color: #000000;
        }

        .step-circle {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #e0e0e0;
            color: #666666;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            font-size: 16px;
            transition: all 0.3s ease;
        }

        .step.active .step-circle {
            background: #000000;
            color: #ffffff;
            transform: scale(1.1);
        }

        .step.completed .step-circle {
            background: #000000;
            color: #ffffff;
        }

        .step-line {
            width: 80px;
            height: 3px;
            background: #e0e0e0;
            margin: 0 20px;
            border-radius: 2px;
        }

        .step-line.completed {
            background: #000000;
        }

        .main-content {
            display: grid;
            grid-template-columns: 1fr 400px;
            gap: 40px;
            padding: 40px;
        }

        .payment-section {
            display: flex;
            flex-direction: column;
            gap: 30px;
        }

        .section-title {
            font-size: 24px;
            font-weight: 700;
            color: #000000;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .smart-suggestion {
            background: #f8f9fa;
            border: 1px solid #e0e0e0;
            border-radius: 12px;
            padding: 24px;
            margin-bottom: 20px;
        }

        .suggestion-header {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 16px;
        }

        .suggestion-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .suggestion-text {
            flex: 1;
        }

        .suggestion-title {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 4px;
        }

        .suggestion-desc {
            font-size: 14px;
            color: #666666;
        }

        .quick-pay-btn {
            background: #000000;
            color: #ffffff;
            border: none;
            padding: 12px 20px;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .quick-pay-btn:hover {
            background: #333333;
            transform: translateY(-1px);
        }

        .quick-pay-section {
            background: #ffffff;
            border: 1px solid #e0e0e0;
            border-radius: 12px;
            padding: 24px;
        }

        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .expand-btn {
            background: none;
            border: 1px solid #e0e0e0;
            color: #666666;
            padding: 8px 16px;
            border-radius: 6px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .expand-btn:hover {
            border-color: #000000;
            color: #000000;
        }

        .quick-methods {
            display: flex;
            flex-direction: column;
            gap: 12px;
            margin-bottom: 16px;
        }

        .quick-method {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 16px;
            background: #ffffff;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .quick-method:hover {
            border-color: #000000;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            transform: translateY(-2px);
        }

        .quick-method.recommended {
            border-color: #000000;
            background: #f8f9fa;
        }

        .quick-method.selected {
            border-color: #000000;
            background: #f0f8ff;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .method-info {
            display: flex;
            align-items: center;
            gap: 12px;
            flex: 1;
        }

        .method-icon {
            width: 48px;
            height: 48px;
            background: #f0f0f0;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
        }

        .method-details h4 {
            font-size: 16px;
            font-weight: 600;
            color: #000000;
            margin-bottom: 4px;
        }

        .method-meta {
            display: flex;
            gap: 12px;
            font-size: 12px;
            color: #666666;
        }

        .recommended-badge {
            background: #000000;
            color: #ffffff;
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 10px;
            font-weight: 500;
            margin-left: 8px;
        }

        .method-actions {
            display: flex;
            gap: 8px;
            align-items: center;
        }

        .btn {
            padding: 8px 16px;
            border: 1px solid #e0e0e0;
            background: #ffffff;
            color: #000000;
            border-radius: 6px;
            cursor: pointer;
            font-size: 12px;
            font-weight: 500;
            transition: all 0.2s ease;
        }

        .btn:hover {
            border-color: #000000;
            transform: translateY(-1px);
        }

        .remove-btn {
            background: none;
            border: none;
            color: #dc3545;
            cursor: pointer;
            font-size: 18px;
            padding: 4px 8px;
            border-radius: 4px;
            transition: all 0.2s ease;
        }

        .remove-btn:hover {
            background: #dc3545;
            color: #ffffff;
        }

        .add-payment-link {
            background: none;
            border: 2px dashed #e0e0e0;
            color: #666666;
            cursor: pointer;
            font-size: 14px;
            padding: 16px;
            border-radius: 8px;
            text-align: center;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .add-payment-link:hover {
            border-color: #000000;
            color: #000000;
            background: #f8f9fa;
        }

        .payment-methods-section {
            background: #ffffff;
            border: 1px solid #e0e0e0;
            border-radius: 12px;
            padding: 24px;
        }

        .payment-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .payment-card {
            border: 2px solid #e0e0e0;
            border-radius: 12px;
            padding: 24px;
            cursor: pointer;
            transition: all 0.3s ease;
            background: #ffffff;
            position: relative;
        }

        .payment-card:hover {
            border-color: #000000;
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
            transform: translateY(-4px);
        }

        .payment-card.selected {
            border-color: #000000;
            background: #f8f9fa;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
        }

        .card-header {
            display: flex;
            align-items: center;
            gap: 16px;
            margin-bottom: 20px;
        }

        .card-icon {
            width: 56px;
            height: 56px;
            background: #f0f0f0;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
        }

        .card-title {
            font-size: 18px;
            font-weight: 600;
            color: #000000;
            margin-bottom: 4px;
        }

        .card-description {
            font-size: 14px;
            color: #666666;
            line-height: 1.4;
        }

        .card-stats {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 16px;
            margin-bottom: 20px;
        }

        .stat-item {
            text-align: center;
            padding: 12px;
            background: #f8f9fa;
            border-radius: 8px;
            transition: all 0.3s ease;
        }

        .payment-card:hover .stat-item {
            background: rgba(0, 0, 0, 0.05);
        }

        .stat-label {
            font-size: 11px;
            color: #666666;
            text-transform: uppercase;
            margin-bottom: 4px;
            letter-spacing: 0.5px;
        }

        .stat-value {
            font-size: 14px;
            font-weight: 600;
            color: #000000;
        }

        .benefits {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }

        .benefit-tag {
            background: #f0f0f0;
            color: #000000;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 11px;
            font-weight: 500;
        }

        .card-form {
            background: #f8f9fa;
            border: 1px solid #e0e0e0;
            border-radius: 12px;
            padding: 24px;
            margin-top: 20px;
            animation: slideDown 0.3s ease;
        }

        @keyframes slideDown {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .form-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 16px;
            margin-bottom: 16px;
        }

        .form-group {
            display: flex;
            flex-direction: column;
            gap: 6px;
        }

        .form-group.full-width {
            grid-column: 1 / -1;
        }

        .form-label {
            font-size: 14px;
            font-weight: 500;
            color: #000000;
        }

        .form-input {
            padding: 12px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            font-size: 14px;
            outline: none;
            transition: all 0.2s ease;
        }

        .form-input:focus {
            border-color: #000000;
            box-shadow: 0 0 0 3px rgba(0, 0, 0, 0.1);
        }

        .form-input:hover {
            border-color: #666666;
        }

        .checkbox-group {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 20px;
        }

        .form-actions {
            display: flex;
            gap: 12px;
        }

        .btn-primary {
            background: #000000;
            color: #ffffff;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            transition: all 0.2s ease;
        }

        .btn-primary:hover {
            background: #333333;
            transform: translateY(-1px);
        }

        .btn-secondary {
            background: none;
            color: #666666;
            border: 1px solid #e0e0e0;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.2s ease;
        }

        .btn-secondary:hover {
            border-color: #000000;
            color: #000000;
        }

        .sidebar {
            display: flex;
            flex-direction: column;
            gap: 24px;
        }

        .order-summary {
            background: #ffffff;
            border: 1px solid #e0e0e0;
            border-radius: 12px;
            padding: 24px;
            position: sticky;
            top: 20px;
        }

        .summary-items {
            display: flex;
            flex-direction: column;
            gap: 12px;
            margin-bottom: 20px;
        }

        .summary-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 16px;
        }

        .summary-item.total {
            border-top: 2px solid #e0e0e0;
            padding-top: 12px;
            font-size: 20px;
            font-weight: 700;
        }

        .discount {
            color: #28a745;
            font-weight: 500;
        }

        .pay-button {
            width: 100%;
            padding: 18px;
            background: #000000;
            color: #ffffff;
            border: none;
            border-radius: 12px;
            font-size: 18px;
            font-weight: 700;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 16px;
        }

        .pay-button:hover:not(:disabled) {
            background: #333333;
            transform: translateY(-2px);
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
        }

        .pay-button:disabled {
            background: #cccccc;
            cursor: not-allowed;
            transform: none;
        }

        .security-info {
            display: flex;
            justify-content: center;
            gap: 16px;
            font-size: 12px;
            color: #666666;
            margin-bottom: 20px;
        }

        .security-item {
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .info-section, .support-section, .activity-section, .trust-section {
            background: #ffffff;
            border: 1px solid #e0e0e0;
            border-radius: 12px;
            padding: 20px;
        }

        .support-section, .trust-section {
            background: #f8f9fa;
        }

        .tips-list {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .tip-item {
            display: flex;
            align-items: flex-start;
            gap: 8px;
            font-size: 14px;
            color: #666666;
        }

        .tip-item span:first-child {
            color: #000000;
            font-weight: bold;
        }

        .support-options {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .support-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 14px;
            color: #000000;
        }

        .support-value {
            font-weight: 600;
        }

        .support-email {
            font-size: 12px;
            color: #666666;
        }

        .chat-btn {
            background: #000000;
            color: #ffffff;
            border: none;
            padding: 6px 12px;
            border-radius: 6px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .chat-btn:hover {
            background: #333333;
        }

        .activity-list {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .activity-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #f0f0f0;
        }

        .activity-item:last-child {
            border-bottom: none;
        }

        .activity-title {
            font-size: 14px;
            font-weight: 500;
        }

        .activity-method {
            font-size: 12px;
            color: #666666;
        }

        .activity-details {
            text-align: right;
        }

        .activity-amount {
            font-size: 14px;
            font-weight: 600;
        }

        .activity-date {
            font-size: 12px;
            color: #666666;
        }

        .trust-stats {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 16px;
            margin-bottom: 16px;
        }

        .trust-stat {
            text-align: center;
        }

        .stat-number {
            font-size: 24px;
            font-weight: 700;
            color: #000000;
        }

        .stat-label {
            font-size: 12px;
            color: #666666;
        }

        .trust-badges {
            display: flex;
            justify-content: center;
            gap: 16px;
            font-size: 12px;
            color: #666666;
        }

        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
                gap: 20px;
                padding: 20px;
            }
            
            .payment-grid {
                grid-template-columns: 1fr;
            }
            
            .form-grid {
                grid-template-columns: 1fr;
            }
            
            .progress-steps {
                padding: 20px;
            }
            
            .step-line {
                width: 60px;
            }
            
            .header {
                padding: 30px 20px;
            }
            
            .header h1 {
                font-size: 24px;
            }
        }
    </style>
</head>
<body>
    <div id="root"></div>

    <script type="text/babel">
        const { useState, useEffect } = React;

        const CompletePaymentPage = () => {
            const [selectedMethod, setSelectedMethod] = useState('');
            const [isProcessing, setIsProcessing] = useState(false);
            const [showAllMethods, setShowAllMethods] = useState(false);
            const [showCardForm, setShowCardForm] = useState(false);
            const [cardDetails, setCardDetails] = useState({
                cardNumber: '',
                expiryDate: '',
                cvv: '',
                cardholderName: '',
                saveCard: false
            });

            const [savedMethods, setSavedMethods] = useState([
                {
                    id: 1,
                    type: 'VISA',
                    details: '**** 1234',
                    icon: '💳',
                    lastUsed: '2 days ago',
                    benefit: '2% cashback',
                    recommended: false,
                    expiryDate: '12/26'
                },
                {
                    id: 2,
                    type: 'UPI',
                    details: 'user@upi',
                    icon: '📱',
                    lastUsed: 'Yesterday',
                    benefit: 'Instant transfer',
                    recommended: true
                },
                {
                    id: 3,
                    type: 'Mastercard',
                    details: '**** 5678',
                    icon: '💳',
                    lastUsed: '1 week ago',
                    benefit: '1.5% cashback',
                    recommended: false,
                    expiryDate: '08/25'
                }
            ]);

            const paymentMethods = [
                {
                    id: 'upi',
                    name: 'UPI',
                    icon: '📱',
                    description: 'Pay instantly using UPI apps like Google Pay, PhonePe',
                    processingTime: 'Instant',
                    fees: 'Free',
                    popularity: 95,
                    benefits: ['Instant transfer', 'No fees', '24/7 available']
                },
                {
                    id: 'card',
                    name: 'Credit/Debit Card',
                    icon: '💳',
                    description: 'Visa, Mastercard, RuPay and other cards accepted',
                    processingTime: '2-3 mins',
                    fees: '2.5%',
                    popularity: 88,
                    benefits: ['EMI available', 'Reward points', 'Global acceptance']
                },
                {
                    id: 'wallet',
                    name: 'Digital Wallet',
                    icon: '👛',
                    description: 'Pay from Paytm, PhonePe, Amazon Pay wallets',
                    processingTime: 'Instant',
                    fees: 'Free',
                    popularity: 72,
                    benefits: ['Quick checkout', 'Cashback offers', 'No card needed']
                },
                {
                    id: 'netbanking',
                    name: 'Net Banking',
                    icon: '🏦',
                    description: 'Direct payment from your bank account',
                    processingTime: '3-5 mins',
                    fees: 'Free',
                    popularity: 65,
                    benefits: ['Direct transfer', 'High security', 'All banks supported']
                },
                {
                    id: 'cod',
                    name: 'Cash on Delivery',
                    icon: '💵',
                    description: 'Pay when your order is delivered to you',
                    processingTime: 'On delivery',
                    fees: '₹40',
                    popularity: 45,
                    benefits: ['No advance payment', 'Inspect first', 'Cash or card']
                },
                {
                    id: 'bnpl',
                    name: 'Buy Now, Pay Later',
                    icon: '📅',
                    description: 'Split your payment into easy installments',
                    processingTime: 'Instant',
                    fees: 'Varies',
                    popularity: 38,
                    benefits: ['No interest EMI', 'Flexible tenure', 'Quick approval']
                }
            ];

            const handleMethodSelect = (methodId) => {
                setSelectedMethod(methodId);
                if (methodId === 'card') {
                    setShowCardForm(true);
                } else {
                    setShowCardForm(false);
                }
            };

            const handleSavedMethodSelect = (method) => {
                // If it's a card (VISA, Mastercard), show the card form
                if (method.type === 'VISA' || method.type === 'Mastercard') {
                    setSelectedMethod('card');
                    setShowCardForm(true);
                    // Pre-fill some details if available
                    setCardDetails(prev => ({
                        ...prev,
                        cardNumber: '', // Don't pre-fill for security
                        expiryDate: method.expiryDate || '',
                        cvv: '',
                        cardholderName: '',
                        saveCard: false
                    }));
                } else {
                    setSelectedMethod(method.type.toLowerCase());
                    setShowCardForm(false);
                }
            };

            const handleQuickPay = (methodId, savedMethod = null) => {
                if (savedMethod) {
                    // Handle saved method payment
                    if (savedMethod.type === 'VISA' || savedMethod.type === 'Mastercard') {
                        // For saved cards, we need card details
                        handleSavedMethodSelect(savedMethod);
                        return;
                    } else {
                        setSelectedMethod(savedMethod.type.toLowerCase());
                    }
                } else {
                    setSelectedMethod(methodId);
                }

                // Only proceed with immediate payment for non-card methods
                if (methodId !== 'card' && (!savedMethod || (savedMethod.type !== 'VISA' && savedMethod.type !== 'Mastercard'))) {
                    setTimeout(() => {
                        handlePayment();
                    }, 500);
                }
            };

            const handleCardInputChange = (field, value) => {
                setCardDetails(prev => ({
                    ...prev,
                    [field]: value
                }));
            };

            const formatCardNumber = (value) => {
                const v = value.replace(/\s+/g, '').replace(/[^0-9]/gi, '');
                const matches = v.match(/\d{4,16}/g);
                const match = matches && matches[0] || '';
                const parts = [];
                for (let i = 0, len = match.length; i < len; i += 4) {
                    parts.push(match.substring(i, i + 4));
                }
                if (parts.length) {
                    return parts.join(' ');
                } else {
                    return v;
                }
            };

            const formatExpiryDate = (value) => {
                const v = value.replace(/\s+/g, '').replace(/[^0-9]/gi, '');
                if (v.length >= 2) {
                    return v.substring(0, 2) + '/' + v.substring(2, 4);
                }
                return v;
            };

            const removeSavedMethod = (methodId) => {
                if (confirm('Remove this payment method?')) {
                    setSavedMethods(prev => prev.filter(method => method.id !== methodId));
                }
            };

            const addNewCard = () => {
                if (cardDetails.cardNumber && cardDetails.expiryDate && cardDetails.cvv && cardDetails.cardholderName) {
                    const newCard = {
                        id: Date.now(),
                        type: cardDetails.cardNumber.startsWith('4') ? 'VISA' : 'Mastercard',
                        details: '**** ' + cardDetails.cardNumber.slice(-4),
                        icon: '💳',
                        lastUsed: 'Just now',
                        benefit: '2% cashback',
                        recommended: false,
                        expiryDate: cardDetails.expiryDate
                    };

                    if (cardDetails.saveCard) {
                        setSavedMethods(prev => [newCard, ...prev]);
                    }

                    setCardDetails({
                        cardNumber: '',
                        expiryDate: '',
                        cvv: '',
                        cardholderName: '',
                        saveCard: false
                    });

                    setShowCardForm(false);
                    alert('✅ Card details saved successfully!');
                } else {
                    alert('Please fill all card details');
                }
            };

            const handlePayment = () => {
                if (!selectedMethod) {
                    alert('Please select a payment method');
                    return;
                }

                if (selectedMethod === 'card' && (!cardDetails.cardNumber || !cardDetails.expiryDate || !cardDetails.cvv || !cardDetails.cardholderName)) {
                    alert('Please fill all card details to complete the payment');
                    return;
                }

                setIsProcessing(true);
                setTimeout(() => {
                    setIsProcessing(false);
                    alert('🎉 Payment successful! Your order has been confirmed.');
                    setShowCardForm(false);

                    // Reset form after successful payment
                    setCardDetails({
                        cardNumber: '',
                        expiryDate: '',
                        cvv: '',
                        cardholderName: '',
                        saveCard: false
                    });
                }, 2500);
            };

            return (
                <div className="payment-container">
                    {/* Header */}
                    <div className="header">
                        <h1>Secure Payment</h1>
                        <p>Complete your order with your preferred payment method</p>
                    </div>

                    {/* Progress Steps */}
                    <div className="progress-steps">
                        <div className="step completed">
                            <div className="step-circle">✓</div>
                            <span>Cart</span>
                        </div>
                        <div className="step-line completed"></div>
                        <div className="step completed">
                            <div className="step-circle">✓</div>
                            <span>Shipping</span>
                        </div>
                        <div className="step-line completed"></div>
                        <div className="step active">
                            <div className="step-circle">3</div>
                            <span>Payment</span>
                        </div>
                        <div className="step-line"></div>
                        <div className="step">
                            <div className="step-circle">4</div>
                            <span>Confirm</span>
                        </div>
                    </div>

                    <div className="main-content">
                        <div className="payment-section">
                            {/* Smart Suggestion */}
                            <div className="smart-suggestion">
                                <div className="suggestion-header">
                                    <span style={{fontSize: '20px'}}>🧠</span>
                                    <h3>Smart Recommendation</h3>
                                </div>
                                <div className="suggestion-content">
                                    <div className="suggestion-text">
                                        <div className="suggestion-title">UPI is recommended for faster checkout</div>
                                        <div className="suggestion-desc">Instant processing with no fees and highest success rate</div>
                                    </div>
                                    <button
                                        className="quick-pay-btn"
                                        onClick={() => handleQuickPay('upi')}
                                    >
                                        Quick Pay with UPI
                                    </button>
                                </div>
                            </div>

                            {/* Quick Pay Section */}
                            <div className="quick-pay-section">
                                <div className="section-header">
                                    <h2 className="section-title">
                                        <span>⚡</span>
                                        Quick Pay
                                    </h2>
                                    <button
                                        className="expand-btn"
                                        onClick={() => setShowAllMethods(!showAllMethods)}
                                    >
                                        {showAllMethods ? 'Show Less' : 'Show All'}
                                    </button>
                                </div>

                                <div className="quick-methods">
                                    {savedMethods.slice(0, showAllMethods ? savedMethods.length : 2).map((method) => (
                                        <div
                                            key={method.id}
                                            className={`quick-method ${method.recommended ? 'recommended' : ''} ${
                                                (selectedMethod === 'card' && (method.type === 'VISA' || method.type === 'Mastercard')) ||
                                                selectedMethod === method.type.toLowerCase() ? 'selected' : ''
                                            }`}
                                        >
                                            <div className="method-info" onClick={() => handleSavedMethodSelect(method)}>
                                                <div className="method-icon">{method.icon}</div>
                                                <div className="method-details">
                                                    <h4>
                                                        {method.type} {method.details}
                                                        {method.recommended && <span className="recommended-badge">Recommended</span>}
                                                    </h4>
                                                    <div className="method-meta">
                                                        <span>Last used: {method.lastUsed}</span>
                                                        <span>{method.benefit}</span>
                                                        {method.expiryDate && <span>Expires: {method.expiryDate}</span>}
                                                    </div>
                                                </div>
                                            </div>
                                            <div className="method-actions">
                                                <button
                                                    className="btn"
                                                    onClick={() => handleQuickPay(method.type.toLowerCase(), method)}
                                                >
                                                    {(method.type === 'VISA' || method.type === 'Mastercard') ? 'Select' : 'Pay Now'}
                                                </button>
                                                <button
                                                    className="remove-btn"
                                                    onClick={(e) => {
                                                        e.stopPropagation();
                                                        removeSavedMethod(method.id);
                                                    }}
                                                    title="Remove payment method"
                                                >
                                                    ×
                                                </button>
                                            </div>
                                        </div>
                                    ))}
                                </div>

                                <button
                                    className="add-payment-link"
                                    onClick={() => {
                                        setSelectedMethod('card');
                                        setShowCardForm(true);
                                        // Clear any existing card details
                                        setCardDetails({
                                            cardNumber: '',
                                            expiryDate: '',
                                            cvv: '',
                                            cardholderName: '',
                                            saveCard: false
                                        });
                                    }}
                                >
                                    <span style={{fontSize: '16px', fontWeight: 'bold'}}>+</span>
                                    Add new payment method
                                </button>
                            </div>

                            {/* All Payment Methods */}
                            <div className="payment-methods-section">
                                <h2 className="section-title">
                                    <span>💳</span>
                                    Choose Payment Method
                                </h2>

                                <div className="payment-grid">
                                    {paymentMethods.map((method) => (
                                        <div
                                            key={method.id}
                                            className={`payment-card ${selectedMethod === method.id ? 'selected' : ''}`}
                                            onClick={() => handleMethodSelect(method.id)}
                                        >
                                            <div className="card-header">
                                                <div className="card-icon">{method.icon}</div>
                                                <div>
                                                    <h3 className="card-title">{method.name}</h3>
                                                    <p className="card-description">{method.description}</p>
                                                </div>
                                            </div>

                                            <div className="card-stats">
                                                <div className="stat-item">
                                                    <div className="stat-label">Processing</div>
                                                    <div className="stat-value">{method.processingTime}</div>
                                                </div>
                                                <div className="stat-item">
                                                    <div className="stat-label">Fees</div>
                                                    <div className="stat-value">{method.fees}</div>
                                                </div>
                                            </div>

                                            <div className="benefits">
                                                {method.benefits.map((benefit, index) => (
                                                    <span key={index} className="benefit-tag">
                                                        ✓ {benefit}
                                                    </span>
                                                ))}
                                            </div>
                                        </div>
                                    ))}
                                </div>

                                {/* Card Details Form */}
                                {showCardForm && selectedMethod === 'card' && (
                                    <div className="card-form">
                                        <div style={{
                                            background: '#e8f4fd',
                                            border: '1px solid #bee5eb',
                                            borderRadius: '6px',
                                            padding: '12px',
                                            marginBottom: '20px',
                                            fontSize: '14px',
                                            color: '#0c5460'
                                        }}>
                                            <strong>Security Notice:</strong> For your security, please enter your complete card details including CVV for verification.
                                        </div>

                                        <h3 style={{
                                            fontSize: '18px',
                                            fontWeight: '600',
                                            color: '#000000',
                                            marginBottom: '20px',
                                            display: 'flex',
                                            alignItems: 'center',
                                            gap: '8px'
                                        }}>
                                            <span>💳</span>
                                            Enter Card Details
                                        </h3>

                                        <div className="form-grid">
                                            <div className="form-group full-width">
                                                <label className="form-label">Cardholder Name</label>
                                                <input
                                                    type="text"
                                                    className="form-input"
                                                    placeholder="John Doe"
                                                    value={cardDetails.cardholderName}
                                                    onChange={(e) => handleCardInputChange('cardholderName', e.target.value)}
                                                />
                                            </div>

                                            <div className="form-group full-width">
                                                <label className="form-label">Card Number</label>
                                                <input
                                                    type="text"
                                                    className="form-input"
                                                    placeholder="1234 5678 9012 3456"
                                                    value={cardDetails.cardNumber}
                                                    onChange={(e) => handleCardInputChange('cardNumber', formatCardNumber(e.target.value))}
                                                    maxLength="19"
                                                    style={{letterSpacing: '1px'}}
                                                />
                                            </div>

                                            <div className="form-group">
                                                <label className="form-label">Expiry Date</label>
                                                <input
                                                    type="text"
                                                    className="form-input"
                                                    placeholder="MM/YY"
                                                    value={cardDetails.expiryDate}
                                                    onChange={(e) => handleCardInputChange('expiryDate', formatExpiryDate(e.target.value))}
                                                    maxLength="5"
                                                />
                                            </div>

                                            <div className="form-group">
                                                <label className="form-label">CVV</label>
                                                <input
                                                    type="text"
                                                    className="form-input"
                                                    placeholder="123"
                                                    value={cardDetails.cvv}
                                                    onChange={(e) => handleCardInputChange('cvv', e.target.value.replace(/\D/g, '').slice(0, 3))}
                                                    maxLength="3"
                                                />
                                            </div>
                                        </div>

                                        <div className="checkbox-group">
                                            <input
                                                type="checkbox"
                                                id="saveCard"
                                                checked={cardDetails.saveCard}
                                                onChange={(e) => handleCardInputChange('saveCard', e.target.checked)}
                                                style={{width: '16px', height: '16px'}}
                                            />
                                            <label htmlFor="saveCard" style={{fontSize: '14px', color: '#666666', cursor: 'pointer'}}>
                                                Save this card for future payments
                                            </label>
                                        </div>

                                        <div className="form-actions">
                                            <button className="btn-primary" onClick={addNewCard}>
                                                Save Card Details
                                            </button>
                                            <button className="btn-secondary" onClick={() => setShowCardForm(false)}>
                                                Cancel
                                            </button>
                                        </div>
                                    </div>
                                )}
                            </div>
                        </div>

                        {/* Sidebar */}
                        <div className="sidebar">
                            {/* Order Summary */}
                            <div className="order-summary">
                                <h3 className="section-title">
                                    <span>📋</span>
                                    Order Summary
                                </h3>

                                <div className="summary-items">
                                    <div className="summary-item">
                                        <span>Subtotal</span>
                                        <span>€39.99</span>
                                    </div>
                                    <div className="summary-item">
                                        <span>Shipping</span>
                                        <span>€2.99</span>
                                    </div>
                                    <div className="summary-item">
                                        <span>Tax</span>
                                        <span>€3.20</span>
                                    </div>
                                    <div className="summary-item discount">
                                        <span>Discount</span>
                                        <span>-€3.19</span>
                                    </div>
                                    <div className="summary-item total">
                                        <span>Total</span>
                                        <span>€42.99</span>
                                    </div>
                                </div>

                                {selectedMethod && (
                                    <div style={{
                                        background: '#f8f9fa',
                                        padding: '16px',
                                        borderRadius: '8px',
                                        marginBottom: '20px',
                                        border: '1px solid #e0e0e0'
                                    }}>
                                        <div style={{fontSize: '14px', fontWeight: '600', marginBottom: '8px'}}>
                                            Selected: {paymentMethods.find(m => m.id === selectedMethod)?.name}
                                        </div>
                                        <div style={{fontSize: '12px', color: '#666666'}}>
                                            Processing: {paymentMethods.find(m => m.id === selectedMethod)?.processingTime}
                                        </div>
                                    </div>
                                )}

                                <button
                                    className="pay-button"
                                    onClick={handlePayment}
                                    disabled={isProcessing || !selectedMethod}
                                >
                                    {isProcessing ? 'Processing...' : 'Pay €42.99 Securely'}
                                </button>

                                <div className="security-info">
                                    <div className="security-item">
                                        <span>🔒</span>
                                        <span>SSL Secured</span>
                                    </div>
                                    <div className="security-item">
                                        <span>🛡️</span>
                                        <span>PCI Compliant</span>
                                    </div>
                                    <div className="security-item">
                                        <span>✓</span>
                                        <span>Verified</span>
                                    </div>
                                </div>
                            </div>

                            {/* Payment Tips */}
                            <div className="info-section">
                                <h3 className="section-title">
                                    <span>💡</span>
                                    Payment Tips
                                </h3>
                                <div className="tips-list">
                                    <div className="tip-item">
                                        <span>•</span>
                                        <span>UPI payments are processed instantly and are completely free</span>
                                    </div>
                                    <div className="tip-item">
                                        <span>•</span>
                                        <span>Cash on Delivery available with ₹40 handling fee</span>
                                    </div>
                                    <div className="tip-item">
                                        <span>•</span>
                                        <span>Credit cards offer EMI options for purchases above ₹1000</span>
                                    </div>
                                    <div className="tip-item">
                                        <span>•</span>
                                        <span>All transactions are secured with 256-bit encryption</span>
                                    </div>
                                </div>
                            </div>

                            {/* Customer Support */}
                            <div className="support-section">
                                <h3 className="section-title">
                                    <span>🎧</span>
                                    Need Help?
                                </h3>
                                <div className="support-options">
                                    <div className="support-item">
                                        <span>24/7 Support</span>
                                        <span className="support-value">1800-123-4567</span>
                                    </div>
                                    <div className="support-item">
                                        <span>Live Chat</span>
                                        <button className="chat-btn">Start Chat</button>
                                    </div>
                                    <div className="support-item">
                                        <span>Email Support</span>
                                        <span className="support-email"><EMAIL></span>
                                    </div>
                                </div>
                            </div>

                            {/* Recent Activity */}
                            <div className="activity-section">
                                <h3 className="section-title">
                                    <span>📊</span>
                                    Recent Activity
                                </h3>
                                <div className="activity-list">
                                    <div className="activity-item">
                                        <div>
                                            <div className="activity-title">Order #12345</div>
                                            <div className="activity-method">UPI Payment</div>
                                        </div>
                                        <div className="activity-details">
                                            <div className="activity-amount">€29.99</div>
                                            <div className="activity-date">2 days ago</div>
                                        </div>
                                    </div>
                                    <div className="activity-item">
                                        <div>
                                            <div className="activity-title">Order #12344</div>
                                            <div className="activity-method">Card Payment</div>
                                        </div>
                                        <div className="activity-details">
                                            <div className="activity-amount">€15.50</div>
                                            <div className="activity-date">1 week ago</div>
                                        </div>
                                    </div>
                                    <div className="activity-item">
                                        <div>
                                            <div className="activity-title">Order #12343</div>
                                            <div className="activity-method">Cash on Delivery</div>
                                        </div>
                                        <div className="activity-details">
                                            <div className="activity-amount">€67.25</div>
                                            <div className="activity-date">2 weeks ago</div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            {/* Trust Indicators */}
                            <div className="trust-section">
                                <h3 className="section-title">
                                    <span>🏆</span>
                                    Trusted by Millions
                                </h3>
                                <div className="trust-stats">
                                    <div className="trust-stat">
                                        <div className="stat-number">10M+</div>
                                        <div className="stat-label">Happy Customers</div>
                                    </div>
                                    <div className="trust-stat">
                                        <div className="stat-number">99.9%</div>
                                        <div className="stat-label">Success Rate</div>
                                    </div>
                                </div>
                                <div className="trust-badges">
                                    <span>⭐ 4.8/5 Rating</span>
                                    <span>🛡️ Bank Grade Security</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            );
        };

        ReactDOM.render(<CompletePaymentPage />, document.getElementById('root'));
    </script>
</body>
</html>
