.payment-method-step {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  max-width: 1200px;
  margin: 0 auto;
}

.step-header {
  text-align: center;
  margin-bottom: var(--spacing-8);
  flex-shrink: 0;
}

.step-title {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  margin-bottom: var(--spacing-3);
}

.step-description {
  font-size: var(--font-size-lg);
  color: var(--text-secondary);
  line-height: var(--line-height-relaxed);
  max-width: 600px;
  margin: 0 auto;
}

.saved-methods-section,
.new-methods-section {
  margin-bottom: var(--spacing-8);
}

.section-title {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin-bottom: var(--spacing-6);
}

.saved-methods-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: var(--spacing-5);
  margin-bottom: var(--spacing-8);
}

.saved-method-card {
  background: var(--surface);
  border: 2px solid var(--border);
  border-radius: var(--radius-xl);
  padding: var(--spacing-6);
  cursor: pointer;
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
  box-shadow: var(--shadow-sm);
}

.saved-method-card:hover {
  border-color: var(--text-primary);
  background: var(--text-primary);
  color: var(--surface);
}

.saved-method-card.selected {
  border-color: var(--text-primary);
  background: var(--text-primary);
  color: var(--surface);
}

.saved-method-card.default {
  border-color: var(--text-primary);
}

.saved-method-card.default::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: var(--text-primary);
}

.default-badge {
  position: absolute;
  top: var(--spacing-3);
  right: var(--spacing-3);
  display: flex;
  align-items: center;
  gap: var(--spacing-1);
  background: var(--accent-orange);
  color: var(--white);
  padding: var(--spacing-1) var(--spacing-2);
  border-radius: var(--radius-full);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-semibold);
}

.method-header {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  margin-bottom: var(--spacing-3);
}

.method-icon {
  width: 40px;
  height: 40px;
  background: var(--gray-100);
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--primary-blue);
  flex-shrink: 0;
}

.method-details {
  flex: 1;
}

.method-name {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin-bottom: var(--spacing-1);
}

.method-type {
  font-size: var(--font-size-xs);
  color: var(--text-secondary);
  font-weight: var(--font-weight-medium);
  letter-spacing: 0.5px;
}

.remove-btn {
  position: absolute;
  top: var(--spacing-3);
  right: var(--spacing-3);
  background: none;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  padding: var(--spacing-1);
  border-radius: var(--radius-base);
  transition: all var(--transition-fast);
  opacity: 0.6;
}

.remove-btn:hover {
  background: rgba(239, 68, 68, 0.1);
  color: var(--error-red);
  opacity: 1;
}

.payment-methods-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: var(--spacing-6);
}

.payment-method-card {
  background: var(--surface);
  border: 2px solid var(--border);
  border-radius: var(--radius-xl);
  padding: var(--spacing-8);
  cursor: pointer;
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
  box-shadow: var(--shadow-sm);
}

.payment-method-card:hover {
  border-color: var(--text-primary);
  background: var(--text-primary);
  color: var(--surface);
}

.payment-method-card.selected {
  border-color: var(--text-primary);
  background: var(--text-primary);
  color: var(--surface);
}

.payment-method-card.recommended {
  border-color: var(--text-primary);
}

.payment-method-card.recommended::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: var(--text-primary);
}

.recommended-badge {
  position: absolute;
  top: var(--spacing-4);
  right: var(--spacing-4);
  display: flex;
  align-items: center;
  gap: var(--spacing-1);
  background: var(--accent-orange);
  color: var(--white);
  padding: var(--spacing-1) var(--spacing-2);
  border-radius: var(--radius-full);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-semibold);
  z-index: 2;
}

.method-info {
  flex: 1;
}

.method-description {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  line-height: var(--line-height-normal);
}

.method-details {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2);
  margin: var(--spacing-4) 0;
}

.detail-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
}

.detail-item svg {
  color: var(--method-color, var(--primary-blue));
}

.method-features {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-2);
  margin-bottom: var(--spacing-4);
}

.feature-tag {
  background: var(--gray-100);
  color: var(--text-primary);
  padding: var(--spacing-1) var(--spacing-2);
  border-radius: var(--radius-full);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
}

.popularity-section {
  margin-top: var(--spacing-4);
}

.popularity-bar {
  margin-bottom: var(--spacing-2);
}

.popularity-track {
  height: 4px;
  background: var(--gray-200);
  border-radius: 2px;
  overflow: hidden;
}

.popularity-fill {
  height: 100%;
  border-radius: 2px;
  transition: width 1s ease-out;
}

.popularity-text {
  font-size: var(--font-size-xs);
  color: var(--text-secondary);
  font-weight: var(--font-weight-medium);
}

.selection-indicator {
  position: absolute;
  top: var(--spacing-4);
  left: var(--spacing-4);
  width: 24px;
  height: 24px;
  background: var(--method-color, var(--primary-blue));
  color: var(--white);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-bold);
  z-index: 2;
}

.selected-summary {
  background: linear-gradient(135deg, var(--gray-50), var(--surface));
  border: 1px solid var(--border);
  border-radius: var(--radius-xl);
  padding: var(--spacing-6);
  margin-top: var(--spacing-6);
}

.summary-header {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  margin-bottom: var(--spacing-4);
}

.summary-header h4 {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
}

.summary-details {
  display: flex;
  align-items: center;
  gap: var(--spacing-4);
}

.summary-icon {
  width: 48px;
  height: 48px;
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.summary-text {
  flex: 1;
}

.summary-text h5 {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin-bottom: var(--spacing-1);
}

.summary-text p {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
}

.summary-badge {
  background: var(--success-green);
  color: var(--white);
  padding: var(--spacing-1) var(--spacing-3);
  border-radius: var(--radius-full);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-semibold);
}

.summary-stats {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-1);
  text-align: right;
}

.summary-stats span {
  font-size: var(--font-size-xs);
  color: var(--text-secondary);
  font-weight: var(--font-weight-medium);
}

/* Dark theme adjustments */
[data-theme="dark"] .saved-method-card,
[data-theme="dark"] .payment-method-card {
  background: var(--gray-800);
  border-color: var(--gray-700);
}

[data-theme="dark"] .method-icon {
  background: var(--gray-700);
}

[data-theme="dark"] .feature-tag {
  background: var(--gray-700);
  color: var(--gray-200);
}

[data-theme="dark"] .selected-summary {
  background: linear-gradient(135deg, var(--gray-800), var(--gray-700));
  border-color: var(--gray-600);
}

/* Responsive design */
@media (max-width: 768px) {
  .saved-methods-grid,
  .payment-methods-grid {
    grid-template-columns: 1fr;
  }
  
  .summary-details {
    flex-direction: column;
    text-align: center;
  }
  
  .summary-stats {
    text-align: center;
  }
}

@media (max-width: 480px) {
  .step-title {
    font-size: var(--font-size-2xl);
  }
  
  .step-description {
    font-size: var(--font-size-base);
  }
  
  .payment-method-card,
  .saved-method-card {
    padding: var(--spacing-4);
  }
}
