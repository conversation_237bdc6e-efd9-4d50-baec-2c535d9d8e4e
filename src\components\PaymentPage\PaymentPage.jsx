import React, { useState, useEffect } from 'react'
import './PaymentPage.css'

const PaymentPage = () => {
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState('')
  const [paymentVariation, setPaymentVariation] = useState('same-day')
  const [applyBestOffer, setApplyBestOffer] = useState(false)
  const [productPaymentOption, setProductPaymentOption] = useState('')
  const [sendNotification, setSendNotification] = useState(true)
  const [isProcessing, setIsProcessing] = useState(false)
  const [showQuickPay, setShowQuickPay] = useState(false)
  const [savedMethodExpanded, setSavedMethodExpanded] = useState(false)
  const [smartSuggestions, setSmartSuggestions] = useState(true)

  const savedPaymentMethods = [
    {
      id: 1,
      type: 'VISA',
      details: '**** 1234',
      icon: '💳',
      isDefault: true,
      lastUsed: '2 days ago',
      cashback: '2% cashback',
      recommended: false,
      expiryDate: '12/26',
      cardHolder: '<PERSON>'
    },
    {
      id: 2,
      type: 'UPI',
      details: 'user@upi',
      icon: '📱',
      isDefault: false,
      lastUsed: 'Yesterday',
      cashback: 'Instant transfer',
      recommended: true,
      provider: 'Google Pay'
    },
    {
      id: 3,
      type: 'Mastercard',
      details: '**** 5678',
      icon: '💳',
      isDefault: false,
      lastUsed: '1 week ago',
      cashback: '1.5% cashback',
      recommended: false,
      expiryDate: '08/25',
      cardHolder: 'John Doe'
    },
    {
      id: 4,
      type: 'PayPal',
      details: '<EMAIL>',
      icon: '🅿️',
      isDefault: false,
      lastUsed: '3 days ago',
      cashback: 'Buyer protection',
      recommended: false,
      verified: true
    }
  ]

  const paymentMethods = [
    {
      id: 'upi',
      label: 'UPI',
      icon: '📱',
      processingTime: 'Instant',
      fees: 'Free',
      popularity: 95,
      recommended: true,
      description: 'Pay instantly using UPI apps',
      benefits: ['Instant transfer', 'No fees', '24/7 available'],
      providers: ['Google Pay', 'PhonePe', 'Paytm', 'BHIM']
    },
    {
      id: 'card',
      label: 'Credit/Debit Card',
      icon: '💳',
      processingTime: '2-3 mins',
      fees: '2.5%',
      popularity: 88,
      recommended: false,
      description: 'Visa, Mastercard, RuPay accepted',
      benefits: ['EMI available', 'Reward points', 'Secure payments'],
      providers: ['Visa', 'Mastercard', 'RuPay', 'American Express']
    },
    {
      id: 'wallet',
      label: 'Wallet',
      icon: '👛',
      processingTime: 'Instant',
      fees: 'Free',
      popularity: 72,
      recommended: false,
      description: 'Pay from your digital wallet',
      benefits: ['Quick checkout', 'Cashback offers', 'No card details needed'],
      providers: ['Paytm', 'PhonePe', 'Amazon Pay', 'MobiKwik']
    },
    {
      id: 'cod',
      label: 'Cash on Delivery',
      icon: '💵',
      processingTime: 'On delivery',
      fees: '₹40',
      popularity: 45,
      recommended: false,
      description: 'Pay when you receive your order',
      benefits: ['No advance payment', 'Inspect before paying', 'Widely accepted'],
      providers: ['Cash', 'Card on delivery']
    },
    {
      id: 'bnpl',
      label: 'Buy Now, Pay Later',
      icon: '📅',
      processingTime: 'Instant',
      fees: 'Varies',
      popularity: 38,
      recommended: false,
      description: 'Split payments into easy EMIs',
      benefits: ['No interest EMI', 'Flexible tenure', 'Quick approval'],
      providers: ['Simpl', 'LazyPay', 'ZestMoney', 'Slice']
    },
    {
      id: 'netbanking',
      label: 'Net Banking',
      icon: '🏦',
      processingTime: '3-5 mins',
      fees: 'Free',
      popularity: 65,
      recommended: false,
      description: 'Pay directly from your bank account',
      benefits: ['Direct bank transfer', 'High security', 'No extra charges'],
      providers: ['SBI', 'HDFC', 'ICICI', 'Axis Bank']
    },
    {
      id: 'crypto',
      label: 'Cryptocurrency',
      icon: '₿',
      processingTime: '10-30 mins',
      fees: '1%',
      popularity: 12,
      recommended: false,
      description: 'Pay with Bitcoin, Ethereum & more',
      benefits: ['Decentralized', 'Global payments', 'Low fees'],
      providers: ['Bitcoin', 'Ethereum', 'USDT', 'Litecoin']
    },
    {
      id: 'gift-card',
      label: 'Gift Card',
      icon: '🎁',
      processingTime: 'Instant',
      fees: 'Free',
      popularity: 25,
      recommended: false,
      description: 'Redeem your gift cards',
      benefits: ['Use existing balance', 'No expiry', 'Instant redemption'],
      providers: ['Amazon', 'Flipkart', 'Store Credit']
    }
  ]

  // Auto-select recommended payment method on load
  useEffect(() => {
    if (smartSuggestions && !selectedPaymentMethod) {
      const recommended = paymentMethods.find(method => method.recommended)
      if (recommended) {
        setSelectedPaymentMethod(recommended.id)
      }
    }
  }, [smartSuggestions, selectedPaymentMethod])

  // Auto-apply best offer when available
  useEffect(() => {
    if (selectedPaymentMethod === 'upi' || selectedPaymentMethod === 'wallet') {
      setApplyBestOffer(true)
    }
  }, [selectedPaymentMethod])

  const handleRemovePaymentMethod = (id) => {
    console.log('Remove payment method:', id)
    alert(`Payment method removed successfully`)
  }

  const handleQuickPay = (methodId) => {
    setSelectedPaymentMethod(methodId)
    setShowQuickPay(true)
    setTimeout(() => {
      handlePayNow()
    }, 500)
  }

  const handlePayNow = () => {
    if (!selectedPaymentMethod) {
      alert('Please select a payment method')
      return
    }

    setIsProcessing(true)

    // Simulate payment processing
    setTimeout(() => {
      setIsProcessing(false)
      alert('✅ Payment successful! Order confirmed.')
    }, 2000)
  }

  const handleBack = () => {
    console.log('Navigate back')
    alert('Navigate to Shipping page')
  }

  const handleNext = () => {
    console.log('Navigate next')
    alert('Navigate to next step')
  }

  const getRecommendedMethod = () => {
    return paymentMethods.find(method => method.recommended)
  }

  return (
    <div className="payment-page">
      {/* Progress Indicator */}
      <div className="progress-indicator">
        <div className="progress-step completed">
          <div className="step-circle">✓</div>
          <span>Shipping</span>
        </div>
        <div className="progress-line"></div>
        <div className="progress-step active">
          <div className="step-circle">2</div>
          <span>Payment</span>
        </div>
        <div className="progress-line inactive"></div>
        <div className="progress-step">
          <div className="step-circle">3</div>
          <span>Confirm</span>
        </div>
      </div>

      {/* Smart Suggestions Banner */}
      {smartSuggestions && getRecommendedMethod() && (
        <div className="smart-suggestion">
          <div className="suggestion-content">
            <span className="suggestion-icon">💡</span>
            <div className="suggestion-text">
              <strong>Smart Suggestion:</strong> {getRecommendedMethod().label} is recommended for faster checkout
            </div>
            <button
              className="quick-pay-btn"
              onClick={() => handleQuickPay(getRecommendedMethod().id)}
            >
              Quick Pay
            </button>
          </div>
        </div>
      )}

      <div className="payment-content">
        {/* Quick Access Saved Methods */}
        <div className="section">
          <div className="section-header">
            <h3 className="section-title">
              <span className="title-icon">⚡</span>
              Quick Pay
            </h3>
            <button
              className="expand-btn"
              onClick={() => setSavedMethodExpanded(!savedMethodExpanded)}
            >
              {savedMethodExpanded ? 'Show Less' : 'Show All'}
            </button>
          </div>

          <div className="quick-pay-methods">
            {savedPaymentMethods.slice(0, savedMethodExpanded ? savedPaymentMethods.length : 2).map((method) => (
              <div
                key={method.id}
                className={`quick-method ${method.recommended ? 'recommended' : ''} ${method.isDefault ? 'default' : ''}`}
                onClick={() => handleQuickPay(method.id)}
              >
                <div className="method-header">
                  <span className="method-icon">{method.icon}</span>
                  <div className="method-details">
                    <div className="method-name">
                      {method.type} {method.details}
                      {method.isDefault && <span className="default-badge">Default</span>}
                      {method.recommended && <span className="recommended-badge">Recommended</span>}
                      {method.verified && <span className="verified-badge">✓ Verified</span>}
                    </div>
                    <div className="method-meta">
                      <span className="last-used">Last used: {method.lastUsed}</span>
                      <span className="benefit">{method.cashback}</span>
                      {method.expiryDate && <span className="expiry">Expires: {method.expiryDate}</span>}
                      {method.provider && <span className="provider">{method.provider}</span>}
                    </div>
                  </div>
                </div>
                <div className="quick-actions">
                  <button
                    className="remove-btn"
                    onClick={(e) => {
                      e.stopPropagation()
                      handleRemovePaymentMethod(method.id)
                    }}
                  >
                    ×
                  </button>
                </div>
              </div>
            ))}
          </div>

          <button className="add-payment-link">
            <span className="add-icon">+</span>
            Add new payment method
          </button>
        </div>

        {/* Enhanced Payment Method Selection */}
        <div className="section">
          <h3 className="section-title">
            <span className="title-icon">💳</span>
            Choose Payment Method
          </h3>

          {/* Popular Methods */}
          <div className="method-category">
            <h4 className="category-title">
              <span className="category-icon">🔥</span>
              Popular Methods
            </h4>
            <div className="payment-methods-grid">
              {paymentMethods.filter(method => method.popularity >= 70).map((method) => (
                <div
                  key={method.id}
                  className={`payment-method-card ${selectedPaymentMethod === method.id ? 'selected' : ''} ${method.recommended ? 'recommended' : ''}`}
                  onClick={() => setSelectedPaymentMethod(method.id)}
                >
                  {method.recommended && (
                    <div className="recommended-tag">
                      <span>⭐ Recommended</span>
                    </div>
                  )}

                  <div className="method-content">
                    <div className="method-header">
                      <span className="method-icon">{method.icon}</span>
                      <div className="method-info">
                        <h4 className="method-name">{method.label}</h4>
                        <p className="method-description">{method.description}</p>
                      </div>
                      <div className="selection-indicator">
                        <div className="radio-custom"></div>
                      </div>
                    </div>

                    <div className="method-details">
                      <div className="detail-row">
                        <div className="detail-item">
                          <span className="detail-label">Processing:</span>
                          <span className="detail-value">{method.processingTime}</span>
                        </div>
                        <div className="detail-item">
                          <span className="detail-label">Fees:</span>
                          <span className="detail-value">{method.fees}</span>
                        </div>
                      </div>

                      <div className="benefits-list">
                        {method.benefits.slice(0, 2).map((benefit, index) => (
                          <span key={index} className="benefit-tag">✓ {benefit}</span>
                        ))}
                      </div>

                      <div className="popularity-bar">
                        <div className="popularity-fill" style={{width: `${method.popularity}%`}}></div>
                        <span className="popularity-text">{method.popularity}% users prefer</span>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Other Methods */}
          <div className="method-category">
            <h4 className="category-title">
              <span className="category-icon">💼</span>
              Other Payment Options
            </h4>
            <div className="payment-methods-grid compact">
              {paymentMethods.filter(method => method.popularity < 70).map((method) => (
                <div
                  key={method.id}
                  className={`payment-method-card compact ${selectedPaymentMethod === method.id ? 'selected' : ''}`}
                  onClick={() => setSelectedPaymentMethod(method.id)}
                >
                  <div className="method-content">
                    <div className="method-header">
                      <span className="method-icon">{method.icon}</span>
                      <div className="method-info">
                        <h4 className="method-name">{method.label}</h4>
                        <p className="method-description">{method.description}</p>
                      </div>
                      <div className="selection-indicator">
                        <div className="radio-custom"></div>
                      </div>
                    </div>

                    <div className="method-details compact">
                      <div className="detail-row">
                        <div className="detail-item">
                          <span className="detail-label">Processing:</span>
                          <span className="detail-value">{method.processingTime}</span>
                        </div>
                        <div className="detail-item">
                          <span className="detail-label">Fees:</span>
                          <span className="detail-value">{method.fees}</span>
                        </div>
                      </div>

                      <div className="providers-list">
                        <span className="providers-label">Supported:</span>
                        <div className="providers">
                          {method.providers.slice(0, 3).map((provider, index) => (
                            <span key={index} className="provider-tag">{provider}</span>
                          ))}
                          {method.providers.length > 3 && (
                            <span className="provider-tag more">+{method.providers.length - 3} more</span>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Smart Payment Options */}
        <div className="section">
          <h3 className="section-title">
            <span className="title-icon">⚙️</span>
            Payment Options
          </h3>

          <div className="options-grid">
            {/* Delivery Speed */}
            <div className="option-group">
              <label className="option-label">Delivery Speed</label>
              <div className="speed-options">
                {[
                  { value: 'same-day', label: 'Same Day', price: '+₹99', icon: '🚀' },
                  { value: 'next-day', label: 'Next Day', price: '+₹49', icon: '📦' },
                  { value: 'standard', label: 'Standard', price: 'Free', icon: '🚚' }
                ].map((option) => (
                  <div
                    key={option.value}
                    className={`speed-option ${paymentVariation === option.value ? 'selected' : ''}`}
                    onClick={() => setPaymentVariation(option.value)}
                  >
                    <span className="speed-icon">{option.icon}</span>
                    <div className="speed-info">
                      <span className="speed-label">{option.label}</span>
                      <span className="speed-price">{option.price}</span>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Payment Type */}
            <div className="option-group">
              <label className="option-label">Payment Type</label>
              <div className="payment-type-options">
                {[
                  { value: 'full', label: 'Full Payment', desc: 'Pay complete amount now', icon: '💰' },
                  { value: 'partial', label: 'Partial Payment', desc: 'Pay 50% now, rest later', icon: '📊' },
                  { value: 'installment', label: 'EMI', desc: 'Split into 3-12 months', icon: '📅' }
                ].map((option) => (
                  <div
                    key={option.value}
                    className={`payment-type-option ${productPaymentOption === option.value ? 'selected' : ''}`}
                    onClick={() => setProductPaymentOption(option.value)}
                  >
                    <span className="type-icon">{option.icon}</span>
                    <div className="type-info">
                      <span className="type-label">{option.label}</span>
                      <span className="type-desc">{option.desc}</span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Smart Offers */}
          <div className="smart-offers">
            <div className="offer-card">
              <div className="offer-content">
                <span className="offer-icon">🎯</span>
                <div className="offer-text">
                  <strong>Best Offer Applied!</strong>
                  <span>Save ₹50 with UPI payment</span>
                </div>
                <label className="offer-toggle">
                  <input
                    type="checkbox"
                    checked={applyBestOffer}
                    onChange={(e) => setApplyBestOffer(e.target.checked)}
                  />
                  <span className="toggle-slider"></span>
                </label>
              </div>
            </div>
          </div>
        </div>

        {/* Enhanced Order Summary */}
        <div className="section">
          <div className="order-summary-modern">
            <div className="summary-header">
              <h3 className="summary-title">
                <span className="title-icon">📋</span>
                Order Summary
              </h3>
            </div>

            <div className="summary-breakdown">
              <div className="summary-item">
                <span>Subtotal</span>
                <span>€39.99</span>
              </div>
              <div className="summary-item">
                <span>Delivery ({paymentVariation})</span>
                <span>{paymentVariation === 'same-day' ? '+€2.99' : paymentVariation === 'next-day' ? '+€1.49' : 'Free'}</span>
              </div>
              {applyBestOffer && (
                <div className="summary-item discount">
                  <span>Best Offer Discount</span>
                  <span>-€1.50</span>
                </div>
              )}
              <div className="summary-divider"></div>
              <div className="summary-total">
                <span>Total Amount</span>
                <span className="total-amount">€{(39.99 + (paymentVariation === 'same-day' ? 2.99 : paymentVariation === 'next-day' ? 1.49 : 0) - (applyBestOffer ? 1.50 : 0)).toFixed(2)}</span>
              </div>
            </div>

            <div className="notification-setting">
              <label className="modern-checkbox">
                <input
                  type="checkbox"
                  checked={sendNotification}
                  onChange={(e) => setSendNotification(e.target.checked)}
                />
                <span className="checkmark"></span>
                <div className="checkbox-text">
                  <span className="checkbox-label">Send payment notification</span>
                  <span className="checkbox-desc">Get updates via SMS & Email</span>
                </div>
              </label>
            </div>
          </div>
        </div>

        {/* Enhanced Pay Button */}
        <div className="section">
          <div className="payment-actions">
            <button
              className={`pay-now-btn-modern ${isProcessing ? 'processing' : ''}`}
              onClick={handlePayNow}
              disabled={isProcessing || !selectedPaymentMethod}
            >
              {isProcessing ? (
                <>
                  <span className="processing-spinner"></span>
                  Processing...
                </>
              ) : (
                <>
                  <span className="pay-icon">🔒</span>
                  Pay €{(39.99 + (paymentVariation === 'same-day' ? 2.99 : paymentVariation === 'next-day' ? 1.49 : 0) - (applyBestOffer ? 1.50 : 0)).toFixed(2)}
                </>
              )}
            </button>

            <div className="security-info">
              <span className="security-icon">🛡️</span>
              <span>Secured by 256-bit SSL encryption</span>
            </div>
          </div>
        </div>

        {/* Floating Navigation */}
        <div className="floating-navigation">
          <button className="nav-btn-modern back-btn" onClick={handleBack}>
            <span className="nav-icon">←</span>
            <span>Back to Shipping</span>
          </button>
          <div className="nav-progress">
            <span>Step 2 of 3</span>
          </div>
        </div>
      </div>
    </div>
  )
}

export default PaymentPage
