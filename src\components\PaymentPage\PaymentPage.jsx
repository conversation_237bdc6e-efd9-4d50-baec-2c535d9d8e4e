import React, { useState } from 'react'
import './PaymentPage.css'

const PaymentPage = () => {
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState('')
  const [paymentVariation, setPaymentVariation] = useState('same-day')
  const [applyBestOffer, setApplyBestOffer] = useState(false)
  const [productPaymentOption, setProductPaymentOption] = useState('')
  const [sendNotification, setSendNotification] = useState(true)

  const savedPaymentMethods = [
    {
      id: 1,
      type: 'VISA',
      details: '**** 1234',
      icon: 'VISA'
    },
    {
      id: 2,
      type: 'UPI',
      details: 'user@upi',
      icon: 'UPI'
    }
  ]

  const paymentMethods = [
    { id: 'upi', label: 'UPI' },
    { id: 'card', label: 'Credit/Debit Card' },
    { id: 'wallet', label: 'Wallet' },
    { id: 'cod', label: 'Cash on Delivery' },
    { id: 'bnpl', label: 'Buy Now, Pay Later' }
  ]

  const handleRemovePaymentMethod = (id) => {
    // Handle remove payment method
    console.log('Remove payment method:', id)
    // You can add actual removal logic here
    alert(`Removing payment method ${id}`)
  }

  const handlePayNow = () => {
    // Handle payment processing
    if (!selectedPaymentMethod) {
      alert('Please select a payment method')
      return
    }
    console.log('Processing payment...', {
      method: selectedPaymentMethod,
      variation: paymentVariation,
      offer: applyBestOffer,
      productOption: productPaymentOption,
      notification: sendNotification
    })
    alert('Payment processing... (Demo)')
  }

  const handleBack = () => {
    // Handle back navigation
    console.log('Navigate back')
    alert('Navigate to Shipping page')
  }

  const handleNext = () => {
    // Handle next navigation
    console.log('Navigate next')
    alert('Navigate to next step')
  }

  return (
    <div className="payment-page">
      {/* Navigation Tabs */}
      <div className="nav-tabs">
        <div className="nav-tab">Shipping</div>
        <div className="nav-tab active">Payment</div>
      </div>

      <div className="payment-content">
        {/* Saved Payment Methods */}
        <div className="section">
          <h3 className="section-title">Saved Payment Methods</h3>
          <div className="saved-methods">
            {savedPaymentMethods.map((method) => (
              <div key={method.id} className="saved-method">
                <div className="method-info">
                  <span className="method-type">{method.type}</span>
                  <span className="method-details">{method.details}</span>
                </div>
                <button 
                  className="remove-btn"
                  onClick={() => handleRemovePaymentMethod(method.id)}
                >
                  Remove
                </button>
              </div>
            ))}
          </div>
          <button className="add-payment-link">Add payment method</button>
        </div>

        {/* Payment Method Selection */}
        <div className="section">
          <h3 className="section-title">Payment Method</h3>
          <div className="payment-methods">
            {paymentMethods.map((method) => (
              <label key={method.id} className="payment-method">
                <input
                  type="radio"
                  name="paymentMethod"
                  value={method.id}
                  checked={selectedPaymentMethod === method.id}
                  onChange={(e) => setSelectedPaymentMethod(e.target.value)}
                />
                <span className="method-label">{method.label}</span>
              </label>
            ))}
          </div>
        </div>

        {/* Payment Variation */}
        <div className="section">
          <h3 className="section-title">Payment Variation</h3>
          <div className="payment-variation">
            <select 
              value={paymentVariation}
              onChange={(e) => setPaymentVariation(e.target.value)}
              className="variation-select"
            >
              <option value="same-day">Same day</option>
              <option value="next-day">Next day</option>
              <option value="standard">Standard</option>
            </select>
            <label className="checkbox-label">
              <input
                type="checkbox"
                checked={applyBestOffer}
                onChange={(e) => setApplyBestOffer(e.target.checked)}
              />
              <span>Apply best offer</span>
            </label>
          </div>
        </div>

        {/* Product-Level Payment */}
        <div className="section">
          <h3 className="section-title">Product-Level Payment</h3>
          <select 
            value={productPaymentOption}
            onChange={(e) => setProductPaymentOption(e.target.value)}
            className="product-payment-select"
          >
            <option value="">Payment Option</option>
            <option value="full">Full Payment</option>
            <option value="partial">Partial Payment</option>
            <option value="installment">Installment</option>
          </select>
        </div>

        {/* Order Summary */}
        <div className="section">
          <div className="order-summary">
            <div className="summary-header">
              <span className="summary-title">Order Summary</span>
              <span className="summary-amount">€42.99</span>
            </div>
            <label className="checkbox-label">
              <input
                type="checkbox"
                checked={sendNotification}
                onChange={(e) => setSendNotification(e.target.checked)}
              />
              <span>Send payment notification</span>
            </label>
          </div>
        </div>

        {/* Pay Now Button */}
        <div className="section">
          <button className="pay-now-btn" onClick={handlePayNow}>
            Pay Now
          </button>
        </div>

        {/* Navigation Buttons */}
        <div className="navigation-buttons">
          <button className="nav-btn back-btn" onClick={handleBack}>
            ← Back
          </button>
          <button className="nav-btn next-btn" onClick={handleNext}>
            Next
          </button>
        </div>
      </div>
    </div>
  )
}

export default PaymentPage
