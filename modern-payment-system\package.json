{"name": "modern-payment-system", "version": "1.0.0", "description": "Modern React.js Payment Interface with Advanced UX Features", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "serve": "vite preview --port 3000"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "framer-motion": "^10.16.4", "lucide-react": "^0.263.1", "react-hook-form": "^7.45.4", "react-hot-toast": "^2.4.1", "@hookform/resolvers": "^3.3.1", "zod": "^3.22.2"}, "devDependencies": {"@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@vitejs/plugin-react": "^4.0.3", "vite": "^4.4.5", "autoprefixer": "^10.4.15", "postcss": "^8.4.29", "tailwindcss": "^3.3.3"}, "keywords": ["payment", "react", "modern-ui", "ux", "payment-gateway", "responsive"], "author": "Payment System Team", "license": "MIT"}