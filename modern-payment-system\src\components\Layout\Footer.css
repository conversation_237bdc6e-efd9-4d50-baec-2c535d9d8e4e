.app-footer {
  background: var(--surface);
  border-top: 1px solid var(--border);
  padding: var(--spacing-2) 0;
  flex-shrink: 0;
}

.footer-content {
  padding: 0 var(--spacing-6);
  display: flex;
  align-items: center;
  justify-content: center;
}

.footer-text {
  display: flex;
  align-items: center;
  gap: var(--spacing-1);
  font-size: var(--font-size-xs);
  color: var(--text-secondary);
  margin: 0;
}

/* Dark theme adjustments */
[data-theme="dark"] .app-footer {
  background: rgba(31, 41, 55, 0.9);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

/* Responsive design */
@media (max-width: 768px) {
  .footer-content {
    flex-direction: column;
    gap: var(--spacing-4);
    text-align: center;
  }
  
  .security-features {
    flex-wrap: wrap;
    justify-content: center;
    gap: var(--spacing-4);
  }
  
  .footer-copyright {
    text-align: center;
  }
}

@media (max-width: 480px) {
  .footer-content {
    padding: 0 var(--spacing-2);
  }
  
  .security-features {
    flex-direction: column;
    gap: var(--spacing-2);
  }
  
  .feature-item {
    justify-content: center;
  }
}
