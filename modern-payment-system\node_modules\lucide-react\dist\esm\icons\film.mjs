/**
 * lucide-react v0.0.1 - ISC
 */

import createLucideIcon from '../createLucideIcon.mjs';

const Film = createLucideIcon("Film", [
  [
    "rect",
    {
      width: "20",
      height: "20",
      x: "2",
      y: "2",
      rx: "2.18",
      ry: "2.18",
      key: "vury4c"
    }
  ],
  ["line", { x1: "7", x2: "7", y1: "2", y2: "22", key: "t1e4qh" }],
  ["line", { x1: "17", x2: "17", y1: "2", y2: "22", key: "1tliql" }],
  ["line", { x1: "2", x2: "22", y1: "12", y2: "12", key: "1dnqot" }],
  ["line", { x1: "2", x2: "7", y1: "7", y2: "7", key: "1wdzzh" }],
  ["line", { x1: "2", x2: "7", y1: "17", y2: "17", key: "2fufxq" }],
  ["line", { x1: "17", x2: "22", y1: "17", y2: "17", key: "1xg577" }],
  ["line", { x1: "17", x2: "22", y1: "7", y2: "7", key: "acrv2l" }]
]);

export { Film as default };
//# sourceMappingURL=film.mjs.map
