{"version": 3, "file": "io-ts.umd.js", "sources": ["../src/arrayToPath.ts", "../src/errorsToRecord.ts", "../src/io-ts.ts"], "sourcesContent": ["import * as Either from 'fp-ts/Either';\nimport { pipe } from 'fp-ts/function';\n\nconst arrayToPath = (paths: Either.Either<string, number>[]): string =>\n  paths.reduce(\n    (previous, path, index) =>\n      pipe(\n        path,\n        Either.fold(\n          (key) => `${index > 0 ? '.' : ''}${key}`,\n          (key) => `[${key}]`,\n        ),\n        (path) => `${previous}${path}`,\n      ),\n    '',\n  );\n\nexport default arrayToPath;\n", "import * as Either from 'fp-ts/Either';\nimport * as Option from 'fp-ts/Option';\nimport * as ReadonlyArray from 'fp-ts/ReadonlyArray';\nimport * as ReadonlyRecord from 'fp-ts/ReadonlyRecord';\nimport * as SemiGroup from 'fp-ts/Semigroup';\nimport { absurd, flow, identity, not, pipe } from 'fp-ts/function';\nimport * as t from 'io-ts';\nimport {\n  ExactType,\n  IntersectionType,\n  RefinementType,\n  TaggedUnionType,\n  UnionType,\n  ValidationError,\n} from 'io-ts';\nimport arrayToPath from './arrayToPath';\nimport { ErrorObject, FieldErrorWithPath } from './types';\n\nconst INSTANCE_TYPES_TO_FILTER = [\n  TaggedUnionType,\n  UnionType,\n  IntersectionType,\n  ExactType,\n  RefinementType,\n];\nconst formatErrorPath = (context: t.Context): string =>\n  pipe(\n    context,\n    ReadonlyArray.filterMapWithIndex((index, contextEntry) => {\n      const previousIndex = index - 1;\n      const previousContextEntry =\n        previousIndex === -1 ? undefined : context[previousIndex];\n      const shouldBeFiltered =\n        previousContextEntry === undefined ||\n        INSTANCE_TYPES_TO_FILTER.some(\n          (type) => previousContextEntry.type instanceof type,\n        );\n\n      return shouldBeFiltered ? Option.none : Option.some(contextEntry);\n    }),\n    ReadonlyArray.map(({ key }) => key),\n    ReadonlyArray.map((key) =>\n      pipe(\n        key,\n        (k) => parseInt(k, 10),\n        Either.fromPredicate(not<number>(Number.isNaN), () => key),\n      ),\n    ),\n    ReadonlyArray.toArray,\n    arrayToPath,\n  );\n\nconst formatError = (e: t.ValidationError): FieldErrorWithPath => {\n  const path = formatErrorPath(e.context);\n\n  const message = pipe(\n    e.message,\n    Either.fromNullable(e.context),\n    Either.mapLeft(\n      flow(\n        ReadonlyArray.last,\n        Option.map(\n          (contextEntry) =>\n            `expected ${contextEntry.type.name} but got ${JSON.stringify(\n              contextEntry.actual,\n            )}`,\n        ),\n        Option.getOrElseW(() =>\n          absurd<string>('Error context is missing name' as never),\n        ),\n      ),\n    ),\n    Either.getOrElseW(identity),\n  );\n\n  const type = pipe(\n    e.context,\n    ReadonlyArray.last,\n    Option.map((contextEntry) => contextEntry.type.name),\n    Option.getOrElse(() => 'unknown'),\n  );\n\n  return { message, type, path };\n};\n\n// this is almost the same function like Semigroup.getObjectSemigroup but reversed\n// in order to get the first error\nconst getObjectSemigroup = <\n  A extends Record<string, unknown> = never,\n>(): SemiGroup.Semigroup<A> => ({\n  concat: (first, second) => Object.assign({}, second, first),\n});\n\nconst concatToSingleError = (\n  errors: ReadonlyArray<FieldErrorWithPath>,\n): ErrorObject =>\n  pipe(\n    errors,\n    ReadonlyArray.map((error) => ({\n      [error.path]: {\n        type: error.type,\n        message: error.message,\n      },\n    })),\n    (errors) => SemiGroup.fold(getObjectSemigroup<ErrorObject>())({}, errors),\n  );\n\nconst appendSeveralErrors: SemiGroup.Semigroup<FieldErrorWithPath> = {\n  concat: (a, b) => ({\n    ...b,\n    types: { ...a.types, [a.type]: a.message, [b.type]: b.message },\n  }),\n};\n\nconst concatToMultipleErrors = (\n  errors: ReadonlyArray<FieldErrorWithPath>,\n): ErrorObject =>\n  pipe(\n    ReadonlyRecord.fromFoldableMap(appendSeveralErrors, ReadonlyArray.Foldable)(\n      errors,\n      (error) => [error.path, error],\n    ),\n    ReadonlyRecord.map((errorWithPath) => {\n      // eslint-disable-next-line @typescript-eslint/no-unused-vars\n      const { path, ...error } = errorWithPath;\n\n      return error;\n    }),\n  );\n\nconst errorsToRecord =\n  (validateAllFieldCriteria: boolean) =>\n  (validationErrors: ReadonlyArray<ValidationError>): ErrorObject => {\n    const concat = validateAllFieldCriteria\n      ? concatToMultipleErrors\n      : concatToSingleError;\n\n    return pipe(validationErrors, ReadonlyArray.map(formatError), concat);\n  };\n\nexport default errorsToRecord;\n", "import { toNestErrors, validateFieldsNatively } from '@hookform/resolvers';\nimport * as Either from 'fp-ts/Either';\nimport { pipe } from 'fp-ts/function';\nimport errorsToRecord from './errorsToRecord';\nimport { Resolver } from './types';\n\nexport const ioTsResolver: Resolver = (codec) => (values, _context, options) =>\n  pipe(\n    values,\n    codec.decode,\n    Either.mapLeft(\n      errorsToRecord(\n        !options.shouldUseNativeValidation && options.criteriaMode === 'all',\n      ),\n    ),\n    Either.mapLeft((errors) => toNestErrors(errors, options)),\n    Either.fold(\n      (errors) => ({\n        values: {},\n        errors,\n      }),\n      (values) => {\n        options.shouldUseNativeValidation &&\n          validateFieldsNatively({}, options);\n\n        return {\n          values,\n          errors: {},\n        } as any;\n      },\n    ),\n  );\n"], "names": ["arrayToPath", "paths", "reduce", "previous", "path", "index", "pipe", "Either", "fold", "key", "_excluded", "INSTANCE_TYPES_TO_FILTER", "TaggedUnionType", "UnionType", "IntersectionType", "ExactType", "RefinementType", "formatError", "e", "context", "<PERSON><PERSON>lyA<PERSON><PERSON>", "filterMapWithIndex", "contextEntry", "previousIndex", "previousContextEntry", "undefined", "some", "type", "Option", "none", "map", "_ref", "k", "parseInt", "fromPredicate", "not", "Number", "isNaN", "toArray", "message", "fromNullable", "mapLeft", "flow", "last", "name", "JSON", "stringify", "actual", "getOrElseW", "absurd", "identity", "getOr<PERSON><PERSON>e", "concatToSingleError", "errors", "error", "_ref2", "SemiGroup", "concat", "first", "second", "Object", "assign", "appendSeveralErrors", "a", "b", "_extends2", "_extends", "types", "concatToMultipleErrors", "<PERSON><PERSON><PERSON>Record", "fromFoldableMap", "Foldable", "errorWithPath", "_objectWithoutPropertiesLoose", "codec", "values", "_context", "options", "decode", "validateAllFieldCriteria", "shouldUseNativeValidation", "criteriaMode", "validationErrors", "toNestErrors", "validateFieldsNatively"], "mappings": "mxCAGA,IAAMA,EAAc,SAACC,UACnBA,EAAMC,OACJ,SAACC,EAAUC,EAAMC,GAAK,OACpBC,EAAAA,KACEF,EACAG,EAAOC,KACL,SAACC,GAAG,OAAQJ,EAAQ,EAAI,IAAM,IAAKI,CAAG,EACtC,SAACA,GAAYA,MAAAA,IAAAA,QAEf,SAACL,YAAYD,EAAWC,CAAI,EAC7B,EACH,GACD,ECfHM,EAAA,CAAA,QAkBMC,EAA2B,CAC/BC,EAAeA,gBACfC,EAASA,UACTC,mBACAC,EAAAA,UACAC,EAAAA,gBA6BIC,EAAc,SAACC,GACnB,IA5BuBC,EA4BjBf,EA3BNE,EAAAA,KADuBa,EA4BMD,EAAEC,QAzB7BC,EAAcC,mBAAmB,SAAChB,EAAOiB,GACvC,IAAMC,EAAgBlB,EAAQ,EACxBmB,GACe,IAAnBD,OAAuBE,EAAYN,EAAQI,GAO7C,YAL2BE,IAAzBD,GACAb,EAAyBe,KACvB,SAACC,GAAS,OAAAH,EAAqBG,gBAAgBA,CAAI,GAG7BC,EAAOC,KAAOD,EAAOF,KAAKJ,EACtD,GACAF,EAAcU,IAAI,SAAAC,UAAMA,EAAHtB,GAAa,GAClCW,EAAcU,IAAI,SAACrB,GACjB,OAAAH,OACEG,EACA,SAACuB,GAAM,OAAAC,SAASD,EAAG,GAAG,EACtBzB,EAAO2B,cAAcC,EAAAA,IAAYC,OAAOC,OAAQ,WAAA,OAAM5B,CAAG,GAC1D,GAEHW,EAAckB,QACdtC,GAiCF,MAAO,CAAEuC,QA3BOjC,OACdY,EAAEqB,QACFhC,EAAOiC,aAAatB,EAAEC,SACtBZ,EAAOkC,QACLC,EAAAA,KACEtB,EAAcuB,KACdf,EAAOE,IACL,SAACR,GACaA,MAAAA,YAAAA,EAAaK,KAAKiB,KAAI,YAAYC,KAAKC,UACjDxB,EAAayB,OACd,GAELnB,EAAOoB,WAAW,WAAA,OAChBC,EAAMA,OAAS,gCAAyC,KAI9D1C,EAAOyC,WAAWE,EAAAA,WAUFvB,KAPLrB,EAAAA,KACXY,EAAEC,QACFC,EAAcuB,KACdf,EAAOE,IAAI,SAACR,GAAY,OAAKA,EAAaK,KAAKiB,IAAI,GACnDhB,EAAOuB,UAAU,WAAM,MAAA,SAAS,IAGV/C,KAAAA,EAC1B,EAUMgD,EAAsB,SAC1BC,GAEA,OAAA/C,OACE+C,EACAjC,EAAcU,IAAI,SAACwB,OAAKC,EAAA,OAAAA,EAAAA,CAAAA,GACrBD,EAAMlD,MAAO,CACZuB,KAAM2B,EAAM3B,KACZY,QAASe,EAAMf,SAChBgB,CAAA,GAEH,SAACF,GAAM,OAAKG,EAAUhD,KAfM,CAC9BiD,OAAQ,SAACC,EAAOC,UAAWC,OAAOC,OAAO,GAAIF,EAAQD,EAAM,GAc7CF,CAAkD,GAAIH,EAAO,EAC1E,EAEGS,EAA+D,CACnEL,OAAQ,SAACM,EAAGC,GAACC,IAAAA,SAAAC,EAAA,CAAA,EACRF,EAAC,CACJG,MAAKD,EAAOH,CAAAA,EAAAA,EAAEI,OAAKF,EAAA,CAAA,EAAAA,EAAGF,EAAEpC,MAAOoC,EAAExB,QAAO0B,EAAGD,EAAErC,MAAOqC,EAAEzB,QAAO0B,QAI3DG,EAAyB,SAC7Bf,GAAyC,OAEzC/C,EAAIA,KACF+D,EAAeC,gBAAgBR,EAAqB1C,EAAcmD,SAAlEF,CACEhB,EACA,SAACC,SAAU,CAACA,EAAMlD,KAAMkD,EAAM,GAEhCe,EAAevC,IAAI,SAAC0C,GAIlB,gJAFsBC,CAAKD,EAAa9D,EAG1C,GACD,iBC1HmC,SAACgE,GAAU,OAAA,SAACC,EAAQC,EAAUC,GAAO,OACzEvE,EAAAA,KACEqE,EACAD,EAAMI,OACNvE,EAAOkC,SDyHRsC,GCvHMF,EAAQG,2BAAsD,QAAzBH,EAAQI,sBDwHnDC,GACC,IAAMzB,EAASsB,EACXX,EACAhB,EAEJ,OAAO9C,EAAAA,KAAK4E,EAAkB9D,EAAcU,IAAIb,GAAcwC,EAChE,IC3HElD,EAAOkC,QAAQ,SAACY,GAAM,OAAK8B,EAAAA,aAAa9B,EAAQwB,EAAQ,GACxDtE,EAAOC,KACL,SAAC6C,GAAY,MAAA,CACXsB,OAAQ,CAAA,EACRtB,OAAAA,EACD,EACD,SAACsB,GAIC,OAHAE,EAAQG,2BACNI,yBAAuB,CAAA,EAAIP,GAEtB,CACLF,OAAAA,EACAtB,OAAQ,CAAA,EAEZ,IDsGJ,IAAC0B,CCpGA,CAAA"}