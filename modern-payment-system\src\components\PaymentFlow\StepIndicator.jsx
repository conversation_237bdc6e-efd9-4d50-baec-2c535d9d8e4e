import React from 'react'
import { motion } from 'framer-motion'
import { Check } from 'lucide-react'
import './StepIndicatorNew.css'

const StepIndicator = ({ steps, currentStep, onStepClick }) => {
  return (
    <div className="step-indicator">
      <div className="step-progress">
        <div className="step-info">
          <h2 className="current-step-title">{steps[currentStep - 1]?.title}</h2>
          <p className="step-counter">Step {currentStep} of {steps.length - 1}</p>
        </div>

        <div className="steps-track">
          {steps.slice(0, -1).map((step, index) => {
            const stepNumber = index + 1
            const isCompleted = stepNumber < currentStep
            const isCurrent = stepNumber === currentStep
            const isClickable = stepNumber <= currentStep

            return (
              <React.Fragment key={step.id}>
                <div
                  className={`step-dot ${isCompleted ? 'completed' : ''} ${isCurrent ? 'current' : ''} ${isClickable ? 'clickable' : ''}`}
                  onClick={() => isClickable && onStepClick(stepNumber)}
                >
                  {isCompleted ? (
                    <Check size={16} />
                  ) : (
                    <span>{stepNumber}</span>
                  )}
                </div>

                {index < steps.length - 2 && (
                  <div className={`step-line ${isCompleted ? 'completed' : ''}`} />
                )}
              </React.Fragment>
            )
          })}
        </div>
      </div>
    </div>
  )
}

export default StepIndicator
