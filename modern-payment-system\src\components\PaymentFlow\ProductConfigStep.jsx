import React from 'react'
import { motion } from 'framer-motion'
import { Package, CreditCard, Truck } from 'lucide-react'
import './Steps.css'

const ProductConfigStep = ({ paymentData, onUpdate }) => {
  const handleProductPaymentChange = (productId, paymentMethod) => {
    const updatedProducts = paymentData.products.map(product =>
      product.id === productId ? { ...product, paymentMethod } : product
    )
    onUpdate({ products: updatedProducts })
  }

  return (
    <div className="product-config-step">
      <div className="step-header">
        <h2 className="step-title">Product Configuration</h2>
        <p className="step-description">
          Configure payment method for each product individually
        </p>
      </div>

      <div className="products-list">
        {paymentData.products.map((product) => (
          <motion.div
            key={product.id}
            className="product-card"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
          >
            <div className="product-info">
              <Package size={24} />
              <div>
                <h3>{product.name}</h3>
                <p>₹{product.price.toLocaleString()}</p>
              </div>
            </div>
            
            <div className="payment-options">
              <button
                className={`option-btn ${product.paymentMethod === 'card' ? 'selected' : ''}`}
                onClick={() => handleProductPaymentChange(product.id, 'card')}
              >
                <CreditCard size={16} />
                Card
              </button>
              <button
                className={`option-btn ${product.paymentMethod === 'cod' ? 'selected' : ''}`}
                onClick={() => handleProductPaymentChange(product.id, 'cod')}
              >
                <Truck size={16} />
                COD
              </button>
            </div>
          </motion.div>
        ))}
      </div>
    </div>
  )
}

export default ProductConfigStep
