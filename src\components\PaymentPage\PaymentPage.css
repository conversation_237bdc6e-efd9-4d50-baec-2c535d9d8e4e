/* Payment Page Styles - Clean Black/White Theme */

.payment-page {
  max-width: 700px;
  margin: 0 auto;
  padding: 20px;
  background: #ffffff;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  color: #000000;
}

/* Navigation Tabs */
.nav-tabs {
  display: flex;
  margin-bottom: 30px;
  border-bottom: 1px solid #e0e0e0;
}

.nav-tab {
  padding: 12px 24px;
  font-size: 16px;
  font-weight: 500;
  color: #666666;
  cursor: pointer;
  border-bottom: 2px solid transparent;
  transition: all 0.2s ease;
}

.nav-tab.active {
  color: #000000;
  border-bottom-color: #000000;
  font-weight: 600;
}

.nav-tab:hover {
  color: #000000;
}

/* Payment Content */
.payment-content {
  display: flex;
  flex-direction: column;
  gap: 25px;
}

/* Section Styling */
.section {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  color: #000000;
  margin: 0;
}

/* Saved Payment Methods */
.saved-methods {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.saved-method {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background: #ffffff;
}

.method-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.method-type {
  font-weight: 600;
  font-size: 16px;
  color: #000000;
}

.method-details {
  color: #666666;
  font-size: 14px;
}

.remove-btn {
  background: none;
  border: none;
  color: #666666;
  cursor: pointer;
  font-size: 14px;
  text-decoration: underline;
  padding: 4px 8px;
}

.remove-btn:hover {
  color: #000000;
}

.add-payment-link {
  background: none;
  border: none;
  color: #000000;
  cursor: pointer;
  font-size: 14px;
  text-decoration: underline;
  text-align: left;
  padding: 0;
  margin-top: 8px;
}

.add-payment-link:hover {
  color: #333333;
}

/* Payment Methods */
.payment-methods {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

.payment-method {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  font-size: 14px;
  color: #000000;
}

.payment-method input[type="radio"] {
  width: 16px;
  height: 16px;
  margin: 0;
}

.method-label {
  user-select: none;
}

/* Payment Variation */
.payment-variation {
  display: flex;
  align-items: center;
  gap: 20px;
}

.variation-select {
  padding: 8px 12px;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  font-size: 14px;
  background: #ffffff;
  color: #000000;
  min-width: 120px;
}

.variation-select:focus {
  outline: none;
  border-color: #000000;
}

/* Product Payment Select */
.product-payment-select {
  padding: 8px 12px;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  font-size: 14px;
  background: #ffffff;
  color: #000000;
  width: 100%;
  max-width: 200px;
}

.product-payment-select:focus {
  outline: none;
  border-color: #000000;
}

/* Checkbox Styling */
.checkbox-label {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  font-size: 14px;
  color: #000000;
}

.checkbox-label input[type="checkbox"] {
  width: 16px;
  height: 16px;
  margin: 0;
}

.checkbox-label span {
  user-select: none;
}

/* Order Summary */
.order-summary {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 20px;
  background: #ffffff;
}

.summary-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 15px;
  border-bottom: 1px solid #e0e0e0;
}

.summary-title {
  font-size: 16px;
  font-weight: 600;
  color: #000000;
}

.summary-amount {
  font-size: 18px;
  font-weight: 700;
  color: #000000;
}

/* Pay Now Button */
.pay-now-btn {
  width: 100%;
  padding: 16px;
  background: #000000;
  color: #ffffff;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.pay-now-btn:hover {
  background: #333333;
}

.pay-now-btn:active {
  background: #000000;
}

/* Navigation Buttons */
.navigation-buttons {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 20px;
}

.nav-btn {
  background: none;
  border: none;
  color: #000000;
  cursor: pointer;
  font-size: 14px;
  padding: 8px 16px;
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.nav-btn:hover {
  background: #f5f5f5;
}

.back-btn {
  text-align: left;
}

.next-btn {
  text-align: right;
}

/* Responsive Design */
@media (max-width: 768px) {
  .payment-page {
    padding: 15px;
    max-width: 100%;
  }
  
  .payment-methods {
    grid-template-columns: 1fr;
  }
  
  .payment-variation {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
  
  .navigation-buttons {
    flex-direction: column;
    gap: 12px;
  }
  
  .nav-btn {
    width: 100%;
    text-align: center;
  }
}
