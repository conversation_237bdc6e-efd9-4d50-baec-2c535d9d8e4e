/* Modern Payment Page Styles - Enhanced UX */

.payment-page {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
  background: #ffffff;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  color: #000000;
  min-height: 100vh;
}

/* Progress Indicator */
.progress-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 30px;
  padding: 20px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 12px;
  border: 1px solid #e0e0e0;
}

.progress-step {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  font-weight: 500;
  color: #666666;
  transition: all 0.3s ease;
}

.progress-step.active {
  color: #000000;
  font-weight: 600;
}

.progress-step.completed {
  color: #28a745;
}

.step-circle {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: 600;
  background: #e9ecef;
  color: #666666;
  transition: all 0.3s ease;
}

.progress-step.active .step-circle {
  background: #000000;
  color: #ffffff;
}

.progress-step.completed .step-circle {
  background: #28a745;
  color: #ffffff;
}

.progress-line {
  width: 60px;
  height: 2px;
  background: #28a745;
  margin: 0 10px;
}

.progress-line.inactive {
  background: #e0e0e0;
}

/* Smart Suggestion Banner */
.smart-suggestion {
  background: linear-gradient(135deg, #000000 0%, #333333 100%);
  color: #ffffff;
  padding: 16px 20px;
  border-radius: 12px;
  margin-bottom: 25px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.suggestion-content {
  display: flex;
  align-items: center;
  gap: 12px;
}

.suggestion-icon {
  font-size: 20px;
}

.suggestion-text {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.suggestion-text strong {
  font-size: 14px;
}

.suggestion-text span {
  font-size: 12px;
  opacity: 0.8;
}

.quick-pay-btn {
  background: #ffffff;
  color: #000000;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.quick-pay-btn:hover {
  background: #f8f9fa;
  transform: translateY(-1px);
}

/* Payment Content */
.payment-content {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

/* Section Styling */
.section {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  color: #000000;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.title-icon {
  font-size: 16px;
}

.expand-btn {
  background: none;
  border: 1px solid #e0e0e0;
  color: #666666;
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.expand-btn:hover {
  border-color: #000000;
  color: #000000;
}

/* Quick Pay Methods */
.quick-pay-methods {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.quick-method {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border: 2px solid #e0e0e0;
  border-radius: 12px;
  background: #ffffff;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}

.quick-method:hover {
  border-color: #000000;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.quick-method.recommended {
  border-color: #28a745;
  background: linear-gradient(135deg, #f8fff9 0%, #ffffff 100%);
}

.quick-method.default {
  border-color: #007bff;
  background: linear-gradient(135deg, #f8fbff 0%, #ffffff 100%);
}

.method-header {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
}

.method-icon {
  font-size: 24px;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8f9fa;
  border-radius: 8px;
}

.method-details {
  flex: 1;
}

.method-name {
  font-weight: 600;
  font-size: 16px;
  color: #000000;
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
}

.default-badge, .recommended-badge {
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 4px;
  font-weight: 500;
}

.default-badge {
  background: #007bff;
  color: #ffffff;
}

.recommended-badge {
  background: #28a745;
  color: #ffffff;
}

.method-meta {
  display: flex;
  gap: 12px;
  font-size: 12px;
  color: #666666;
}

.last-used {
  opacity: 0.8;
}

.benefit {
  color: #28a745;
  font-weight: 500;
}

.quick-actions {
  display: flex;
  align-items: center;
}

.remove-btn {
  background: #f8f9fa;
  border: none;
  color: #666666;
  cursor: pointer;
  font-size: 16px;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.remove-btn:hover {
  background: #dc3545;
  color: #ffffff;
}

.add-payment-link {
  background: none;
  border: 2px dashed #e0e0e0;
  color: #666666;
  cursor: pointer;
  font-size: 14px;
  padding: 16px;
  border-radius: 12px;
  text-align: center;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.add-payment-link:hover {
  border-color: #000000;
  color: #000000;
  background: #f8f9fa;
}

.add-icon {
  font-size: 16px;
  font-weight: bold;
}

/* Enhanced Payment Methods Grid */
.payment-methods-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 16px;
}

.payment-method-card {
  border: 2px solid #e0e0e0;
  border-radius: 12px;
  padding: 20px;
  background: #ffffff;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.payment-method-card:hover {
  border-color: #000000;
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0,0,0,0.1);
}

.payment-method-card.selected {
  border-color: #000000;
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
  box-shadow: 0 4px 16px rgba(0,0,0,0.1);
}

.payment-method-card.recommended {
  border-color: #28a745;
}

.recommended-tag {
  position: absolute;
  top: 12px;
  right: 12px;
  background: #28a745;
  color: #ffffff;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 10px;
  font-weight: 600;
}

.method-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.method-header {
  display: flex;
  align-items: center;
  gap: 12px;
}

.method-icon {
  font-size: 24px;
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8f9fa;
  border-radius: 8px;
}

.method-info {
  flex: 1;
}

.method-name {
  font-size: 16px;
  font-weight: 600;
  color: #000000;
  margin: 0 0 4px 0;
}

.method-description {
  font-size: 12px;
  color: #666666;
  margin: 0;
}

.selection-indicator {
  width: 20px;
  height: 20px;
}

.radio-custom {
  width: 20px;
  height: 20px;
  border: 2px solid #e0e0e0;
  border-radius: 50%;
  position: relative;
  transition: all 0.2s ease;
}

.payment-method-card.selected .radio-custom {
  border-color: #000000;
  background: #000000;
}

.payment-method-card.selected .radio-custom::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 8px;
  height: 8px;
  background: #ffffff;
  border-radius: 50%;
}

.method-details {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
}

.detail-label {
  color: #666666;
}

.detail-value {
  color: #000000;
  font-weight: 500;
}

.popularity-bar {
  position: relative;
  height: 4px;
  background: #e0e0e0;
  border-radius: 2px;
  overflow: hidden;
  margin-top: 4px;
}

.popularity-fill {
  height: 100%;
  background: linear-gradient(90deg, #28a745 0%, #20c997 100%);
  border-radius: 2px;
  transition: width 0.3s ease;
}

.popularity-text {
  font-size: 10px;
  color: #666666;
  margin-top: 4px;
  display: block;
}

/* Smart Payment Options */
.options-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
}

.option-group {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.option-label {
  font-size: 14px;
  font-weight: 600;
  color: #000000;
}

/* Speed Options */
.speed-options {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.speed-option {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  border: 2px solid #e0e0e0;
  border-radius: 8px;
  background: #ffffff;
  cursor: pointer;
  transition: all 0.2s ease;
}

.speed-option:hover {
  border-color: #000000;
}

.speed-option.selected {
  border-color: #000000;
  background: #f8f9fa;
}

.speed-icon {
  font-size: 16px;
}

.speed-info {
  display: flex;
  flex-direction: column;
  flex: 1;
}

.speed-label {
  font-size: 14px;
  font-weight: 500;
  color: #000000;
}

.speed-price {
  font-size: 12px;
  color: #666666;
}

/* Payment Type Options */
.payment-type-options {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.payment-type-option {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  border: 2px solid #e0e0e0;
  border-radius: 8px;
  background: #ffffff;
  cursor: pointer;
  transition: all 0.2s ease;
}

.payment-type-option:hover {
  border-color: #000000;
}

.payment-type-option.selected {
  border-color: #000000;
  background: #f8f9fa;
}

.type-icon {
  font-size: 16px;
}

.type-info {
  display: flex;
  flex-direction: column;
  flex: 1;
}

.type-label {
  font-size: 14px;
  font-weight: 500;
  color: #000000;
}

.type-desc {
  font-size: 12px;
  color: #666666;
}

/* Smart Offers */
.smart-offers {
  margin-top: 20px;
}

.offer-card {
  background: linear-gradient(135deg, #fff3cd 0%, #ffffff 100%);
  border: 1px solid #ffeaa7;
  border-radius: 12px;
  padding: 16px 20px;
}

.offer-content {
  display: flex;
  align-items: center;
  gap: 12px;
}

.offer-icon {
  font-size: 20px;
}

.offer-text {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.offer-text strong {
  font-size: 14px;
  color: #000000;
}

.offer-text span {
  font-size: 12px;
  color: #666666;
}

/* Modern Toggle Switch */
.offer-toggle {
  position: relative;
  display: inline-block;
  width: 44px;
  height: 24px;
}

.offer-toggle input {
  opacity: 0;
  width: 0;
  height: 0;
}

.toggle-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: .4s;
  border-radius: 24px;
}

.toggle-slider:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 3px;
  bottom: 3px;
  background-color: white;
  transition: .4s;
  border-radius: 50%;
}

input:checked + .toggle-slider {
  background-color: #000000;
}

input:checked + .toggle-slider:before {
  transform: translateX(20px);
}

/* Modern Order Summary */
.order-summary-modern {
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
  border: 2px solid #e0e0e0;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.05);
}

.summary-header {
  margin-bottom: 20px;
}

.summary-title {
  font-size: 18px;
  font-weight: 600;
  color: #000000;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.summary-breakdown {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 20px;
}

.summary-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
  color: #000000;
}

.summary-item.discount {
  color: #28a745;
  font-weight: 500;
}

.summary-divider {
  height: 1px;
  background: #e0e0e0;
  margin: 8px 0;
}

.summary-total {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 16px;
  font-weight: 600;
  color: #000000;
  padding-top: 12px;
  border-top: 2px solid #e0e0e0;
}

.total-amount {
  font-size: 20px;
  font-weight: 700;
  color: #000000;
}

/* Modern Checkbox */
.modern-checkbox {
  display: flex;
  align-items: center;
  gap: 12px;
  cursor: pointer;
  padding: 12px;
  border-radius: 8px;
  transition: background-color 0.2s ease;
}

.modern-checkbox:hover {
  background: rgba(0,0,0,0.02);
}

.modern-checkbox input[type="checkbox"] {
  display: none;
}

.checkmark {
  width: 20px;
  height: 20px;
  border: 2px solid #e0e0e0;
  border-radius: 4px;
  position: relative;
  transition: all 0.2s ease;
}

.modern-checkbox input:checked + .checkmark {
  background: #000000;
  border-color: #000000;
}

.modern-checkbox input:checked + .checkmark::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: #ffffff;
  font-size: 12px;
  font-weight: bold;
}

.checkbox-text {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.checkbox-label {
  font-size: 14px;
  font-weight: 500;
  color: #000000;
}

.checkbox-desc {
  font-size: 12px;
  color: #666666;
}

/* Enhanced Payment Actions */
.payment-actions {
  display: flex;
  flex-direction: column;
  gap: 16px;
  align-items: center;
}

.pay-now-btn-modern {
  width: 100%;
  padding: 18px 24px;
  background: linear-gradient(135deg, #000000 0%, #333333 100%);
  color: #ffffff;
  border: none;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  box-shadow: 0 4px 16px rgba(0,0,0,0.2);
  position: relative;
  overflow: hidden;
}

.pay-now-btn-modern:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0,0,0,0.3);
}

.pay-now-btn-modern:active:not(:disabled) {
  transform: translateY(0);
}

.pay-now-btn-modern:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.pay-now-btn-modern.processing {
  background: linear-gradient(135deg, #666666 0%, #999999 100%);
}

.pay-icon {
  font-size: 16px;
}

.processing-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid #ffffff;
  border-top: 2px solid transparent;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.security-info {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  color: #666666;
}

.security-icon {
  font-size: 14px;
}

/* Floating Navigation */
.floating-navigation {
  position: fixed;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  align-items: center;
  gap: 16px;
  background: rgba(255,255,255,0.95);
  backdrop-filter: blur(10px);
  padding: 12px 20px;
  border-radius: 50px;
  box-shadow: 0 8px 32px rgba(0,0,0,0.1);
  border: 1px solid rgba(255,255,255,0.2);
  z-index: 1000;
}

.nav-btn-modern {
  background: none;
  border: 1px solid #e0e0e0;
  color: #000000;
  cursor: pointer;
  font-size: 14px;
  padding: 8px 16px;
  border-radius: 20px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 6px;
}

.nav-btn-modern:hover {
  background: #000000;
  color: #ffffff;
  border-color: #000000;
}

.nav-icon {
  font-size: 12px;
}

.nav-progress {
  font-size: 12px;
  color: #666666;
  font-weight: 500;
}

/* Enhanced Responsive Design */
@media (max-width: 768px) {
  .payment-page {
    padding: 15px;
    max-width: 100%;
  }

  .progress-indicator {
    padding: 15px;
  }

  .progress-step {
    font-size: 10px;
  }

  .step-circle {
    width: 28px;
    height: 28px;
    font-size: 12px;
  }

  .progress-line {
    width: 40px;
  }

  .smart-suggestion {
    padding: 12px 16px;
  }

  .suggestion-content {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .quick-pay-btn {
    align-self: stretch;
    text-align: center;
  }

  .options-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .speed-options, .payment-type-options {
    gap: 6px;
  }

  .speed-option, .payment-type-option {
    padding: 10px 12px;
  }

  .floating-navigation {
    position: relative;
    bottom: auto;
    left: auto;
    transform: none;
    margin-top: 20px;
    width: 100%;
    justify-content: space-between;
    border-radius: 12px;
  }

  .nav-btn-modern {
    flex: 1;
    justify-content: center;
  }

  .method-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .method-icon {
    width: 36px;
    height: 36px;
    font-size: 20px;
  }
}

@media (max-width: 480px) {
  .payment-page {
    padding: 10px;
  }

  .section {
    gap: 15px;
  }

  .payment-content {
    gap: 20px;
  }

  .quick-method {
    padding: 12px 16px;
  }

  .method-name {
    font-size: 14px;
  }

  .method-meta {
    flex-direction: column;
    gap: 4px;
  }

  .payment-method-card {
    padding: 16px;
  }

  .method-content {
    gap: 12px;
  }

  .order-summary-modern {
    padding: 20px;
  }

  .pay-now-btn-modern {
    padding: 16px 20px;
    font-size: 15px;
  }
}

/* Dark mode support (optional) */
@media (prefers-color-scheme: dark) {
  .payment-page {
    background: #1a1a1a;
    color: #ffffff;
  }

  .quick-method, .payment-method-card, .order-summary-modern {
    background: #2a2a2a;
    border-color: #404040;
  }

  .section-title, .method-name, .summary-title {
    color: #ffffff;
  }

  .method-details, .checkbox-desc, .detail-label {
    color: #cccccc;
  }

  .floating-navigation {
    background: rgba(42,42,42,0.95);
    border-color: rgba(64,64,64,0.2);
  }
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Focus states for keyboard navigation */
.quick-method:focus,
.payment-method-card:focus,
.speed-option:focus,
.payment-type-option:focus,
.pay-now-btn-modern:focus,
.nav-btn-modern:focus {
  outline: 2px solid #000000;
  outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .payment-method-card,
  .quick-method,
  .speed-option,
  .payment-type-option {
    border-width: 3px;
  }

  .pay-now-btn-modern {
    border: 2px solid #ffffff;
  }
}
